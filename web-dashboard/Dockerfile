# Version-Craft Dashboard Dockerfile
# 多阶段构建，优化镜像大小

# 阶段1: 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# 复制 package 文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production --silent

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 阶段2: 运行阶段
FROM node:18-alpine AS runtime

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S dashboard -u 1001

# 设置工作目录
WORKDIR /app

# 安装运行时依赖
RUN apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# 复制 package 文件
COPY package*.json ./

# 仅安装生产依赖
RUN npm ci --only=production --silent && \
    npm cache clean --force

# 从构建阶段复制构建产物
COPY --from=builder --chown=dashboard:nodejs /app/dist ./dist
COPY --from=builder --chown=dashboard:nodejs /app/public ./public

# 创建必要目录
RUN mkdir -p logs data uploads && \
    chown -R dashboard:nodejs logs data uploads

# 复制启动脚本
COPY --chown=dashboard:nodejs docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# 切换到非 root 用户
USER dashboard

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# 设置环境变量
ENV NODE_ENV=production \
    PORT=3000 \
    LOG_LEVEL=info

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["docker-entrypoint.sh"]
