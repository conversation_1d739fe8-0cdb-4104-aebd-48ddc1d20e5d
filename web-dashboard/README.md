# Version-Craft Web Dashboard

Version-Craft 的现代化 Web 管理界面，提供直观的版本管理、构建监控和部署管理功能。

## 🌟 功能特性

### 📊 版本管理
- **可视化版本历史** - 直观查看所有版本变更记录
- **智能版本升级** - 支持 major/minor/patch 和预发布版本
- **一键版本回滚** - 安全的版本回滚操作
- **版本对比分析** - 详细的版本差异对比

### 🔨 构建管理
- **多平台构建** - 支持 Web、Android、iOS、Windows、Mac 平台
- **实时构建监控** - WebSocket 实时显示构建进度
- **构建历史追踪** - 完整的构建记录和状态管理
- **构建失败诊断** - 详细的错误信息和解决建议

### 🚀 部署管理
- **多环境部署** - 支持开发、测试、生产环境
- **自动化部署流程** - 一键部署到目标环境
- **部署状态监控** - 实时监控部署进度和状态
- **回滚机制** - 部署失败时快速回滚

### 📈 系统监控
- **实时系统状态** - CPU、内存、磁盘使用情况
- **性能指标监控** - 构建时间、部署时间等关键指标
- **日志管理** - 结构化日志查看和搜索
- **告警通知** - 关键事件的实时通知

## 🏗️ 技术架构

### 后端技术栈
- **Node.js + TypeScript** - 现代化的服务端开发
- **Express.js** - 轻量级 Web 框架
- **Socket.IO** - 实时双向通信
- **JWT** - 安全的身份认证
- **Helmet** - 安全中间件

### 前端技术栈
- **Vue 3 + TypeScript** - 现代化的前端框架
- **Vite** - 快速的构建工具
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Tailwind CSS** - 实用优先的 CSS 框架
- **Chart.js** - 数据可视化

### 开发工具
- **ESLint + Prettier** - 代码质量和格式化
- **Husky** - Git hooks
- **Commitizen** - 规范化提交信息

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖
```bash
# 安装依赖
npm install

# 或使用 yarn
yarn install
```

### 开发环境
```bash
# 启动开发服务器 (前端 + 后端)
npm run dev

# 仅启动前端开发服务器
npm run client:dev

# 仅启动后端开发服务器
npm run server:dev
```

### 生产构建
```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 📁 项目结构

```
web-dashboard/
├── src/
│   ├── client/                 # 前端代码
│   │   ├── components/         # Vue 组件
│   │   │   ├── layout/         # 布局组件
│   │   │   ├── modals/         # 模态框组件
│   │   │   └── charts/         # 图表组件
│   │   ├── views/              # 页面组件
│   │   ├── stores/             # Pinia 状态管理
│   │   ├── utils/              # 工具函数
│   │   ├── types/              # TypeScript 类型定义
│   │   └── assets/             # 静态资源
│   └── server/                 # 后端代码
│       ├── controllers/        # 控制器
│       ├── routes/             # 路由定义
│       ├── middleware/         # 中间件
│       ├── websocket/          # WebSocket 处理
│       └── utils/              # 工具函数
├── dist/                       # 构建输出
├── public/                     # 公共静态文件
└── docs/                       # 文档
```

## 🔧 配置说明

### 环境变量
创建 `.env` 文件配置环境变量：

```env
# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置 (如果使用)
DATABASE_URL=sqlite:./data/dashboard.db

# JWT 配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d

# CORS 配置
CORS_ORIGIN=http://localhost:5173

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/dashboard.log
```

### 前端配置
在 `vite.config.ts` 中配置前端构建选项：

```typescript
export default defineConfig({
  server: {
    port: 5173,
    proxy: {
      '/api': 'http://localhost:3000',
      '/socket.io': {
        target: 'http://localhost:3000',
        ws: true
      }
    }
  }
});
```

## 📊 API 文档

### 版本管理 API

#### 获取当前版本
```http
GET /api/versions/current
```

#### 获取版本历史
```http
GET /api/versions?page=1&limit=20&filter=search
```

#### 版本升级
```http
POST /api/versions/bump
Content-Type: application/json

{
  "type": "minor",
  "prerelease": "beta",
  "message": "Add new features"
}
```

#### 版本回滚
```http
POST /api/versions/rollback
Content-Type: application/json

{
  "targetVersion": "1.2.0",
  "force": false
}
```

### 构建管理 API

#### 开始构建
```http
POST /api/builds
Content-Type: application/json

{
  "platform": "web-mobile",
  "version": "1.2.3",
  "options": {
    "minify": true,
    "compress": true
  }
}
```

#### 获取构建状态
```http
GET /api/builds/{buildId}
```

## 🔌 WebSocket 事件

### 客户端监听事件
- `version:changed` - 版本变更通知
- `build:progress` - 构建进度更新
- `deployment:progress` - 部署进度更新
- `system:status` - 系统状态更新
- `error` - 错误通知
- `success` - 成功通知

### 客户端发送事件
- `subscribe` - 订阅频道
- `unsubscribe` - 取消订阅
- `ping` - 心跳检测

## 🧪 测试

```bash
# 运行单元测试
npm run test

# 运行端到端测试
npm run test:e2e

# 生成测试覆盖率报告
npm run test:coverage
```

## 📦 部署

### Docker 部署
```bash
# 构建 Docker 镜像
docker build -t version-craft-dashboard .

# 运行容器
docker run -p 3000:3000 -e NODE_ENV=production version-craft-dashboard
```

### PM2 部署
```bash
# 安装 PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题或有建议，请：

1. 查看 [FAQ](docs/FAQ.md)
2. 搜索 [Issues](https://github.com/your-org/version-craft/issues)
3. 创建新的 Issue
4. 联系维护团队

## 🎯 路线图

- [ ] 多语言支持 (i18n)
- [ ] 深色模式
- [ ] 移动端适配
- [ ] 插件系统
- [ ] API 文档生成
- [ ] 性能监控面板
- [ ] 自动化测试集成
- [ ] 云原生部署支持
