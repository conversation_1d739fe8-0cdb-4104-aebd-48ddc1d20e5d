{"name": "version-craft-dashboard", "version": "1.0.0", "description": "Web Dashboard for Version-Craft", "main": "dist/server.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "build": "npm run client:build && npm run server:build", "server:dev": "nodemon --exec ts-node src/server/index.ts", "server:build": "tsc -p tsconfig.server.json", "client:dev": "vite", "client:build": "vite build", "preview": "vite preview", "start": "node dist/server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "ws": "^8.14.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "socket.io": "^4.7.4", "simple-git": "^3.20.0", "semver": "^7.5.4", "fs-extra": "^11.1.1", "chokidar": "^3.5.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/ws": "^8.5.10", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "concurrently": "^8.2.2", "vite": "^5.0.8", "vue": "^3.3.11", "vue-router": "^4.2.5", "pinia": "^2.1.7", "@vitejs/plugin-vue": "^4.5.2", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "axios": "^1.6.2", "chart.js": "^4.4.1", "vue-chartjs": "^5.3.0", "date-fns": "^2.30.0", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "vue-toastification": "^2.0.0-rc.5"}, "keywords": ["version-management", "dashboard", "web-interface", "cocos-creator"], "author": "Version-Craft Team", "license": "MIT"}