#!/bin/bash

# Version-Craft Dashboard 启动脚本
# 用于快速启动开发或生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Node.js 版本
check_node_version() {
    log_info "检查 Node.js 版本..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js 16.0.0 或更高版本"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2)
    REQUIRED_VERSION="16.0.0"
    
    if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION') ? 0 : 1)" 2>/dev/null; then
        log_error "Node.js 版本过低，当前版本: $NODE_VERSION，要求版本: >= $REQUIRED_VERSION"
        exit 1
    fi
    
    log_success "Node.js 版本检查通过: $NODE_VERSION"
}

# 检查依赖
check_dependencies() {
    log_info "检查项目依赖..."
    
    if [ ! -d "node_modules" ]; then
        log_warning "依赖未安装，正在安装..."
        npm install
    else
        log_success "依赖检查通过"
    fi
}

# 检查环境变量
check_environment() {
    log_info "检查环境配置..."
    
    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，创建默认配置..."
        cat > .env << EOF
# 服务器配置
PORT=3000
NODE_ENV=development

# JWT 配置
JWT_SECRET=your-secret-key-change-in-production
JWT_EXPIRES_IN=7d

# CORS 配置
CORS_ORIGIN=http://localhost:5173

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/dashboard.log

# Version-Craft 项目路径
PROJECT_ROOT=../
EOF
        log_success "已创建默认 .env 文件"
    else
        log_success "环境配置检查通过"
    fi
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p logs
    mkdir -p data
    mkdir -p uploads
    
    log_success "目录创建完成"
}

# 启动开发环境
start_development() {
    log_info "启动开发环境..."
    
    # 检查端口是否被占用
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 3000 已被占用，尝试终止占用进程..."
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    if lsof -Pi :5173 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 5173 已被占用，尝试终止占用进程..."
        lsof -ti:5173 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    log_success "开发环境启动中..."
    log_info "前端地址: http://localhost:5173"
    log_info "后端地址: http://localhost:3000"
    log_info "按 Ctrl+C 停止服务"
    
    npm run dev
}

# 启动生产环境
start_production() {
    log_info "启动生产环境..."
    
    # 构建项目
    log_info "构建项目..."
    npm run build
    
    # 检查构建结果
    if [ ! -d "dist" ]; then
        log_error "构建失败，dist 目录不存在"
        exit 1
    fi
    
    log_success "项目构建完成"
    
    # 设置生产环境变量
    export NODE_ENV=production
    
    log_success "生产环境启动中..."
    log_info "服务地址: http://localhost:3000"
    log_info "按 Ctrl+C 停止服务"
    
    npm start
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查后端服务
    if curl -f http://localhost:3000/health >/dev/null 2>&1; then
        log_success "后端服务正常"
    else
        log_error "后端服务异常"
        return 1
    fi
    
    # 检查前端服务 (仅开发环境)
    if [ "$NODE_ENV" != "production" ]; then
        if curl -f http://localhost:5173 >/dev/null 2>&1; then
            log_success "前端服务正常"
        else
            log_error "前端服务异常"
            return 1
        fi
    fi
    
    log_success "健康检查通过"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    # 停止端口占用的进程
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    
    # 停止 PM2 进程 (如果存在)
    if command -v pm2 &> /dev/null; then
        pm2 stop version-craft-dashboard 2>/dev/null || true
        pm2 delete version-craft-dashboard 2>/dev/null || true
    fi
    
    log_success "服务已停止"
}

# 清理缓存
clean_cache() {
    log_info "清理缓存..."
    
    rm -rf node_modules/.cache
    rm -rf dist
    rm -rf .vite
    
    log_success "缓存清理完成"
}

# 显示帮助信息
show_help() {
    echo "Version-Craft Dashboard 启动脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  dev         启动开发环境 (默认)"
    echo "  prod        启动生产环境"
    echo "  build       仅构建项目"
    echo "  health      健康检查"
    echo "  stop        停止服务"
    echo "  clean       清理缓存"
    echo "  help        显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev      # 启动开发环境"
    echo "  $0 prod     # 启动生产环境"
    echo "  $0 health   # 执行健康检查"
}

# 主函数
main() {
    local command=${1:-dev}
    
    echo "🚀 Version-Craft Dashboard"
    echo "=========================="
    
    case $command in
        "dev"|"development")
            check_node_version
            check_dependencies
            check_environment
            create_directories
            start_development
            ;;
        "prod"|"production")
            check_node_version
            check_dependencies
            check_environment
            create_directories
            start_production
            ;;
        "build")
            check_node_version
            check_dependencies
            log_info "构建项目..."
            npm run build
            log_success "构建完成"
            ;;
        "health")
            health_check
            ;;
        "stop")
            stop_services
            ;;
        "clean")
            clean_cache
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'log_info "收到中断信号，正在停止服务..."; stop_services; exit 0' INT TERM

# 执行主函数
main "$@"
