# Version-Craft Web Dashboard 演示指南

本文档将指导您快速体验 Version-Craft Web Dashboard 的核心功能。

## 🚀 快速启动

### 1. 环境准备
```bash
# 确保您在 version-craft 项目根目录
cd /path/to/version-craft

# 进入 web-dashboard 目录
cd web-dashboard

# 安装依赖
npm install
```

### 2. 启动开发环境
```bash
# 使用启动脚本 (推荐)
chmod +x scripts/start.sh
./scripts/start.sh dev

# 或者直接使用 npm
npm run dev
```

### 3. 访问应用
- **前端界面**: http://localhost:5173
- **后端 API**: http://localhost:3000
- **健康检查**: http://localhost:3000/health

## 📊 功能演示

### 版本管理功能

#### 1. 查看当前版本
访问首页，您将看到：
- 当前项目版本号
- 版本创建时间
- 构建和部署状态
- 版本类型标识

#### 2. 版本历史浏览
在版本管理页面，您可以：
- 查看完整的版本变更历史
- 搜索特定版本
- 按版本类型过滤 (major/minor/patch/alpha/beta/rc)
- 分页浏览历史记录

#### 3. 版本升级操作
点击"版本升级"按钮：
1. 选择升级类型 (Major/Minor/Patch)
2. 选择预发布类型 (可选: Alpha/Beta/RC)
3. 填写变更说明
4. 确认执行升级

**实时体验**:
```bash
# 在另一个终端中，使用 CLI 进行版本升级
cd /path/to/version-craft/test-project
node ../dist/cli.js bump patch

# 观察 Web Dashboard 中的实时更新
```

#### 4. 版本回滚操作
点击"版本回滚"按钮：
1. 选择目标回滚版本
2. 查看回滚影响预览
3. 确认执行回滚

### 构建管理功能

#### 1. 查看构建历史
在构建管理页面，您可以：
- 查看所有平台的构建记录
- 实时监控构建进度
- 查看构建日志和错误信息
- 下载构建产物

#### 2. 启动新构建
点击"开始构建"按钮：
1. 选择构建平台 (Web/Android/iOS/Windows/Mac)
2. 选择构建版本 (默认当前版本)
3. 配置构建选项
4. 启动构建任务

**实时体验**:
```bash
# 在另一个终端中启动构建
cd /path/to/version-craft/test-project
node ../dist/cli.js build web-mobile

# 在 Web Dashboard 中观察构建进度
```

#### 3. 构建状态监控
- 实时构建进度条
- 构建日志流式显示
- 构建时间和性能统计
- 构建失败时的错误诊断

### 部署管理功能

#### 1. 环境状态概览
在部署管理页面查看：
- 各环境的当前版本
- 部署状态和健康检查
- 环境配置信息
- 部署历史记录

#### 2. 执行部署操作
选择目标环境和版本：
1. 选择部署环境 (Development/Staging/Production)
2. 选择部署版本
3. 配置部署参数
4. 执行部署任务

#### 3. 部署监控
- 实时部署进度
- 部署日志查看
- 回滚机制
- 部署后验证

### 系统监控功能

#### 1. 系统状态面板
- CPU 和内存使用率
- 磁盘空间监控
- 网络连接状态
- 服务健康检查

#### 2. 性能指标
- 构建时间趋势
- 部署成功率
- 系统响应时间
- 错误率统计

#### 3. 日志管理
- 结构化日志查看
- 日志级别过滤
- 关键词搜索
- 日志导出功能

## 🔌 WebSocket 实时功能

### 实时通知演示
1. 打开浏览器开发者工具的网络面板
2. 查看 WebSocket 连接状态
3. 在 CLI 中执行版本操作
4. 观察 Web Dashboard 中的实时更新

### 支持的实时事件
- 版本变更通知
- 构建进度更新
- 部署状态变化
- 系统状态更新
- 错误和成功提示

## 🎨 用户界面特性

### 响应式设计
- 桌面端完整功能
- 平板端适配
- 移动端基础功能

### 主题和样式
- 现代化的 Material Design 风格
- 一致的颜色系统
- 流畅的动画效果
- 直观的图标系统

### 交互体验
- 快捷键支持
- 拖拽操作
- 批量操作
- 撤销/重做功能

## 🔧 高级功能演示

### 1. 批量操作
```javascript
// 在浏览器控制台中执行
// 批量选择版本进行操作
const versions = ['1.0.0', '1.0.1', '1.0.2'];
versions.forEach(version => {
  console.log(`Processing version: ${version}`);
});
```

### 2. 自定义过滤器
- 按时间范围过滤
- 按作者过滤
- 按版本类型过滤
- 自定义搜索条件

### 3. 数据导出
- 版本历史导出 (CSV/JSON)
- 构建报告导出
- 系统日志导出
- 性能数据导出

## 🧪 测试场景

### 场景1: 完整的版本发布流程
1. 创建新的预发布版本 (Alpha)
2. 构建所有平台
3. 部署到测试环境
4. 升级到 Beta 版本
5. 部署到预生产环境
6. 发布正式版本
7. 部署到生产环境

### 场景2: 紧急回滚场景
1. 发现生产环境问题
2. 快速回滚到稳定版本
3. 重新构建和部署
4. 验证回滚结果

### 场景3: 多平台构建管理
1. 同时启动多个平台构建
2. 监控构建进度
3. 处理构建失败
4. 重试失败的构建

## 📱 移动端体验

### 移动端访问
使用手机浏览器访问 http://your-server:3000

### 移动端功能
- 版本信息查看
- 构建状态监控
- 基础的版本操作
- 推送通知支持

## 🔍 故障排除

### 常见问题

#### 1. WebSocket 连接失败
```bash
# 检查防火墙设置
sudo ufw status

# 检查端口占用
lsof -i :3000
lsof -i :5173
```

#### 2. 构建失败
```bash
# 检查 Cocos Creator 路径
which CocosCreator

# 检查项目配置
cat version-craft.config.json
```

#### 3. 权限问题
```bash
# 检查文件权限
ls -la logs/
ls -la data/

# 修复权限
chmod 755 logs data uploads
```

### 调试模式
```bash
# 启用调试日志
export LOG_LEVEL=debug
npm run dev

# 查看详细日志
tail -f logs/dashboard.log
```

## 📈 性能优化建议

### 1. 生产环境配置
```bash
# 使用 PM2 管理进程
npm install -g pm2
pm2 start ecosystem.config.js

# 启用 Gzip 压缩
# 配置反向代理 (Nginx)
```

### 2. 数据库优化
```bash
# 定期清理历史数据
# 添加数据库索引
# 配置连接池
```

### 3. 缓存策略
```bash
# 启用 Redis 缓存
# 配置 CDN
# 启用浏览器缓存
```

## 🎯 下一步

体验完基础功能后，您可以：

1. **自定义配置**: 修改 `version-craft.config.json` 适配您的项目
2. **集成 CI/CD**: 将 Web Dashboard 集成到您的持续集成流程
3. **扩展功能**: 基于 API 开发自定义功能
4. **部署生产**: 使用 Docker 或 PM2 部署到生产环境

## 💡 提示和技巧

- 使用快捷键 `Ctrl+K` 打开命令面板
- 双击版本号快速复制
- 使用浏览器书签保存常用操作
- 开启浏览器通知获取实时提醒

---

**享受使用 Version-Craft Web Dashboard！** 🎉

如有问题或建议，请查看 [FAQ](docs/FAQ.md) 或提交 Issue。
