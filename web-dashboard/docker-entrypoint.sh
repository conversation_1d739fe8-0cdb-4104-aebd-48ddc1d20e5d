#!/bin/sh

# Version-Craft Dashboard Docker 入口脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 等待依赖服务
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local timeout=${4:-30}
    
    log_info "等待 $service_name 服务启动 ($host:$port)..."
    
    local count=0
    while ! nc -z "$host" "$port" 2>/dev/null; do
        if [ $count -ge $timeout ]; then
            log_error "$service_name 服务启动超时"
            exit 1
        fi
        
        count=$((count + 1))
        sleep 1
    done
    
    log_success "$service_name 服务已就绪"
}

# 初始化数据库
init_database() {
    if [ -n "$DATABASE_URL" ]; then
        log_info "初始化数据库..."
        
        # 这里可以添加数据库迁移逻辑
        # npm run db:migrate
        
        log_success "数据库初始化完成"
    fi
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p logs data uploads
    
    log_success "目录创建完成"
}

# 设置权限
set_permissions() {
    log_info "设置文件权限..."
    
    # 确保日志目录可写
    chmod 755 logs data uploads
    
    log_success "权限设置完成"
}

# 验证环境变量
validate_environment() {
    log_info "验证环境变量..."
    
    # 检查必需的环境变量
    local required_vars="NODE_ENV PORT"
    
    for var in $required_vars; do
        if [ -z "$(eval echo \$$var)" ]; then
            log_error "必需的环境变量 $var 未设置"
            exit 1
        fi
    done
    
    # 设置默认值
    export JWT_SECRET=${JWT_SECRET:-"change-me-in-production"}
    export JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-"7d"}
    export LOG_LEVEL=${LOG_LEVEL:-"info"}
    export CORS_ORIGIN=${CORS_ORIGIN:-"*"}
    
    log_success "环境变量验证通过"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:$PORT/health >/dev/null 2>&1; then
            log_success "应用健康检查通过"
            return 0
        fi
        
        log_info "健康检查失败，重试 $attempt/$max_attempts..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "应用健康检查失败"
    return 1
}

# 优雅关闭处理
graceful_shutdown() {
    log_info "收到关闭信号，正在优雅关闭..."
    
    # 发送 SIGTERM 信号给 Node.js 进程
    if [ -n "$NODE_PID" ]; then
        kill -TERM "$NODE_PID" 2>/dev/null || true
        
        # 等待进程结束
        local count=0
        while kill -0 "$NODE_PID" 2>/dev/null && [ $count -lt 30 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        # 如果进程仍在运行，强制终止
        if kill -0 "$NODE_PID" 2>/dev/null; then
            log_warning "强制终止应用进程"
            kill -KILL "$NODE_PID" 2>/dev/null || true
        fi
    fi
    
    log_success "应用已关闭"
    exit 0
}

# 主函数
main() {
    log_info "🚀 启动 Version-Craft Dashboard"
    log_info "================================"
    
    # 设置信号处理
    trap graceful_shutdown TERM INT
    
    # 初始化步骤
    validate_environment
    create_directories
    set_permissions
    
    # 等待外部依赖服务 (如果配置了)
    if [ -n "$DATABASE_HOST" ] && [ -n "$DATABASE_PORT" ]; then
        wait_for_service "$DATABASE_HOST" "$DATABASE_PORT" "Database"
    fi
    
    if [ -n "$REDIS_HOST" ] && [ -n "$REDIS_PORT" ]; then
        wait_for_service "$REDIS_HOST" "$REDIS_PORT" "Redis"
    fi
    
    # 初始化数据库
    init_database
    
    # 显示配置信息
    log_info "配置信息:"
    log_info "  Node.js 版本: $(node --version)"
    log_info "  环境: $NODE_ENV"
    log_info "  端口: $PORT"
    log_info "  日志级别: $LOG_LEVEL"
    log_info "  工作目录: $(pwd)"
    
    # 启动应用
    log_success "启动 Node.js 应用..."
    
    if [ "$NODE_ENV" = "development" ]; then
        # 开发模式
        npm run server:dev &
    else
        # 生产模式
        node dist/server.js &
    fi
    
    NODE_PID=$!
    log_info "应用进程 PID: $NODE_PID"
    
    # 等待应用启动
    sleep 5
    
    # 执行健康检查
    if health_check; then
        log_success "✅ Version-Craft Dashboard 启动成功!"
        log_info "🌐 访问地址: http://localhost:$PORT"
        log_info "📊 健康检查: http://localhost:$PORT/health"
    else
        log_error "❌ 应用启动失败"
        exit 1
    fi
    
    # 等待进程结束
    wait $NODE_PID
}

# 执行主函数
main "$@"
