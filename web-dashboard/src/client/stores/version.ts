import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { apiClient } from '@utils/api';
import type { Version, VersionHistory, BumpVersionRequest, RollbackVersionRequest } from '@types/version';

export const useVersionStore = defineStore('version', () => {
  // 状态
  const currentVersion = ref<Version>({
    version: '',
    hasBuilds: false,
    hasDeployments: false,
    gitTag: '',
    createdAt: '',
    fileChanges: [],
    buildStatus: 'unknown',
    deploymentStatus: 'unknown'
  });

  const versions = ref<VersionHistory[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // 分页信息
  const pagination = ref({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });

  // 计算属性
  const hasVersions = computed(() => versions.value.length > 0);
  const isCurrentVersionStable = computed(() => {
    return !currentVersion.value.version.includes('-');
  });

  // 操作方法
  const loadCurrentVersion = async () => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await apiClient.get('/api/versions/current');
      
      if (response.data.success) {
        currentVersion.value = response.data.data;
      } else {
        throw new Error(response.data.error || '获取当前版本失败');
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取当前版本失败';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const loadVersionHistory = async (page: number = 1, limit: number = 20, filter?: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });

      if (filter) {
        params.append('filter', filter);
      }

      const response = await apiClient.get(`/api/versions?${params}`);
      
      if (response.data.success) {
        versions.value = response.data.data.versions;
        pagination.value = response.data.data.pagination;
      } else {
        throw new Error(response.data.error || '获取版本历史失败');
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取版本历史失败';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const bumpVersion = async (request: BumpVersionRequest) => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await apiClient.post('/api/versions/bump', request);
      
      if (response.data.success) {
        // 更新当前版本
        await loadCurrentVersion();
        // 刷新版本历史
        await loadVersionHistory(1, pagination.value.limit);
        
        return response.data.data;
      } else {
        throw new Error(response.data.error || '版本升级失败');
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '版本升级失败';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const rollbackVersion = async (request: RollbackVersionRequest) => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await apiClient.post('/api/versions/rollback', request);
      
      if (response.data.success) {
        // 更新当前版本
        await loadCurrentVersion();
        // 刷新版本历史
        await loadVersionHistory(1, pagination.value.limit);
        
        return response.data.data;
      } else {
        throw new Error(response.data.error || '版本回滚失败');
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '版本回滚失败';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const getVersionDetails = async (version: string) => {
    try {
      const response = await apiClient.get(`/api/versions/${version}`);
      
      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error || '获取版本详情失败');
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取版本详情失败';
      throw err;
    }
  };

  const searchVersions = async (query: string) => {
    return loadVersionHistory(1, pagination.value.limit, query);
  };

  const refreshData = async () => {
    await Promise.all([
      loadCurrentVersion(),
      loadVersionHistory(pagination.value.page, pagination.value.limit)
    ]);
  };

  // 清除错误
  const clearError = () => {
    error.value = null;
  };

  // 重置状态
  const reset = () => {
    currentVersion.value = {
      version: '',
      hasBuilds: false,
      hasDeployments: false,
      gitTag: '',
      createdAt: '',
      fileChanges: [],
      buildStatus: 'unknown',
      deploymentStatus: 'unknown'
    };
    versions.value = [];
    pagination.value = {
      page: 1,
      limit: 20,
      total: 0,
      totalPages: 0,
      hasNext: false,
      hasPrev: false
    };
    error.value = null;
    isLoading.value = false;
  };

  // 版本比较工具
  const compareVersions = (v1: string, v2: string): number => {
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);
    
    for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
      const part1 = parts1[i] || 0;
      const part2 = parts2[i] || 0;
      
      if (part1 > part2) return 1;
      if (part1 < part2) return -1;
    }
    
    return 0;
  };

  // 获取版本类型
  const getVersionType = (version: string): string => {
    if (version.includes('-alpha')) return 'alpha';
    if (version.includes('-beta')) return 'beta';
    if (version.includes('-rc')) return 'rc';
    if (version.includes('-')) return 'prerelease';
    return 'stable';
  };

  // 检查是否可以回滚到指定版本
  const canRollbackTo = (targetVersion: string): boolean => {
    const currentVer = currentVersion.value.version;
    if (!currentVer || !targetVersion) return false;
    
    // 不能回滚到当前版本
    if (currentVer === targetVersion) return false;
    
    // 可以回滚到任何历史版本
    return versions.value.some(v => v.version === targetVersion);
  };

  // 获取推荐的升级类型
  const getRecommendedBumpType = (): 'major' | 'minor' | 'patch' => {
    // 这里可以基于代码变更分析来推荐升级类型
    // 目前返回默认的 patch
    return 'patch';
  };

  return {
    // 状态
    currentVersion,
    versions,
    isLoading,
    error,
    pagination,
    
    // 计算属性
    hasVersions,
    isCurrentVersionStable,
    
    // 操作方法
    loadCurrentVersion,
    loadVersionHistory,
    bumpVersion,
    rollbackVersion,
    getVersionDetails,
    searchVersions,
    refreshData,
    clearError,
    reset,
    
    // 工具方法
    compareVersions,
    getVersionType,
    canRollbackTo,
    getRecommendedBumpType
  };
});
