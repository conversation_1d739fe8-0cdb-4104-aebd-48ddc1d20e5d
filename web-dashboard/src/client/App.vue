<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-600">正在加载 Version-Craft Dashboard...</p>
      </div>
    </div>

    <!-- 主应用 -->
    <div v-else class="flex h-screen">
      <!-- 侧边栏 -->
      <Sidebar 
        :is-collapsed="isSidebarCollapsed"
        @toggle="toggleSidebar"
        class="flex-shrink-0"
      />

      <!-- 主内容区域 -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- 顶部导航栏 -->
        <Header 
          :is-sidebar-collapsed="isSidebarCollapsed"
          @toggle-sidebar="toggleSidebar"
          class="flex-shrink-0"
        />

        <!-- 内容区域 -->
        <main class="flex-1 overflow-auto bg-gray-50">
          <div class="container mx-auto px-4 py-6">
            <!-- 面包屑导航 -->
            <Breadcrumb class="mb-6" />
            
            <!-- 路由视图 -->
            <router-view v-slot="{ Component, route }">
              <transition
                name="page"
                mode="out-in"
                enter-active-class="transition-all duration-300 ease-out"
                enter-from-class="opacity-0 transform translate-x-4"
                enter-to-class="opacity-100 transform translate-x-0"
                leave-active-class="transition-all duration-200 ease-in"
                leave-from-class="opacity-100 transform translate-x-0"
                leave-to-class="opacity-0 transform -translate-x-4"
              >
                <component :is="Component" :key="route.path" />
              </transition>
            </router-view>
          </div>
        </main>
      </div>
    </div>

    <!-- WebSocket 连接状态指示器 -->
    <div 
      v-if="!websocketStore.isConnected" 
      class="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2"
    >
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
      <span>连接已断开</span>
    </div>

    <!-- 全局通知容器 -->
    <div id="toast-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'vue-toastification';

// 组件导入
import Sidebar from '@components/layout/Sidebar.vue';
import Header from '@components/layout/Header.vue';
import Breadcrumb from '@components/layout/Breadcrumb.vue';

// Store 导入
import { useAuthStore } from '@stores/auth';
import { useWebSocketStore } from '@stores/websocket';
import { useSystemStore } from '@stores/system';

// 响应式数据
const isLoading = ref(true);
const isSidebarCollapsed = ref(false);

// 组合式 API
const router = useRouter();
const toast = useToast();

// Store 实例
const authStore = useAuthStore();
const websocketStore = useWebSocketStore();
const systemStore = useSystemStore();

// 方法
const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value;
  // 保存到本地存储
  localStorage.setItem('sidebar-collapsed', String(isSidebarCollapsed.value));
};

const initializeApp = async () => {
  try {
    // 从本地存储恢复侧边栏状态
    const savedSidebarState = localStorage.getItem('sidebar-collapsed');
    if (savedSidebarState !== null) {
      isSidebarCollapsed.value = savedSidebarState === 'true';
    }

    // 初始化认证状态
    await authStore.initializeAuth();

    // 如果未认证，跳转到登录页
    if (!authStore.isAuthenticated) {
      router.push('/login');
      return;
    }

    // 连接 WebSocket
    await websocketStore.connect();

    // 加载系统状态
    await systemStore.loadSystemStatus();

    // 设置 WebSocket 事件监听
    setupWebSocketListeners();

  } catch (error) {
    console.error('应用初始化失败:', error);
    toast.error('应用初始化失败，请刷新页面重试');
  } finally {
    isLoading.value = false;
  }
};

const setupWebSocketListeners = () => {
  // 监听系统通知
  websocketStore.on('success', (data: any) => {
    toast.success(data.message);
  });

  websocketStore.on('error', (data: any) => {
    toast.error(data.message);
  });

  websocketStore.on('warning', (data: any) => {
    toast.warning(data.message);
  });

  websocketStore.on('info', (data: any) => {
    toast.info(data.message);
  });

  // 监听版本变更
  websocketStore.on('version:changed', (data: any) => {
    toast.success(`版本已更新: ${data.newVersion}`);
    // 可以在这里刷新相关数据
  });

  // 监听构建状态
  websocketStore.on('build:progress', (data: any) => {
    // 更新构建进度
    systemStore.updateBuildProgress(data);
  });

  // 监听部署状态
  websocketStore.on('deployment:progress', (data: any) => {
    // 更新部署进度
    systemStore.updateDeploymentProgress(data);
  });
};

// 生命周期
onMounted(() => {
  initializeApp();
});

onUnmounted(() => {
  // 断开 WebSocket 连接
  websocketStore.disconnect();
});

// 监听路由变化
router.beforeEach((to, from, next) => {
  // 检查认证状态
  if (to.meta.requiresAuth !== false && !authStore.isAuthenticated) {
    next('/login');
  } else {
    next();
  }
});
</script>

<style scoped>
/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 自定义滚动条 */
:deep(.overflow-auto) {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

:deep(.overflow-auto::-webkit-scrollbar) {
  width: 6px;
}

:deep(.overflow-auto::-webkit-scrollbar-track) {
  background: #f7fafc;
}

:deep(.overflow-auto::-webkit-scrollbar-thumb) {
  background: #cbd5e0;
  border-radius: 3px;
}

:deep(.overflow-auto::-webkit-scrollbar-thumb:hover) {
  background: #a0aec0;
}
</style>
