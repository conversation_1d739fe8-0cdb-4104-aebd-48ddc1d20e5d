import { createApp } from 'vue';
import { createPinia } from 'pinia';
import Toast from 'vue-toastification';
import App from './App.vue';
import router from './router';

// 样式导入
import './assets/styles/main.css';
import 'vue-toastification/dist/index.css';

// 创建应用实例
const app = createApp(App);

// 状态管理
const pinia = createPinia();
app.use(pinia);

// 路由
app.use(router);

// 通知组件
app.use(Toast, {
  position: 'top-right',
  timeout: 5000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: 'button',
  icon: true,
  rtl: false
});

// 全局属性
app.config.globalProperties.$appVersion = __APP_VERSION__;
app.config.globalProperties.$buildTime = __BUILD_TIME__;

// 错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err);
  console.error('Component:', vm);
  console.error('Info:', info);
  
  // 可以在这里集成错误报告服务
  // errorReportingService.report(err, { vm, info });
};

// 挂载应用
app.mount('#app');

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('🚀 Version-Craft Dashboard');
  console.log('📦 Version:', __APP_VERSION__);
  console.log('🕐 Build Time:', __BUILD_TIME__);
  console.log('🌍 Environment:', import.meta.env.MODE);
}
