<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">版本管理</h1>
        <p class="text-gray-600 mt-1">管理项目版本，执行版本升级和回滚操作</p>
      </div>
      
      <div class="flex space-x-3">
        <button
          @click="refreshData"
          :disabled="isLoading"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <svg class="w-4 h-4 mr-2" :class="{ 'animate-spin': isLoading }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          刷新
        </button>
        
        <button
          @click="showBumpModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          版本升级
        </button>
      </div>
    </div>

    <!-- 当前版本卡片 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-2">当前版本</h2>
          <div class="flex items-center space-x-4">
            <span class="text-3xl font-bold text-blue-600">{{ currentVersion.version || 'N/A' }}</span>
            <div class="flex space-x-2">
              <span 
                v-if="currentVersion.hasBuilds" 
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
              >
                已构建
              </span>
              <span 
                v-if="currentVersion.hasDeployments" 
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                已部署
              </span>
            </div>
          </div>
          <p class="text-gray-600 mt-2">
            创建时间: {{ formatDate(currentVersion.createdAt) }}
          </p>
        </div>
        
        <div class="text-right">
          <button
            @click="showRollbackModal = true"
            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
            </svg>
            版本回滚
          </button>
        </div>
      </div>
    </div>

    <!-- 版本历史 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h2 class="text-lg font-semibold text-gray-900">版本历史</h2>
          
          <!-- 搜索和过滤 -->
          <div class="flex space-x-3">
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索版本..."
                class="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            
            <select
              v-model="selectedFilter"
              class="block w-32 pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm rounded-md"
            >
              <option value="">所有类型</option>
              <option value="major">Major</option>
              <option value="minor">Minor</option>
              <option value="patch">Patch</option>
              <option value="alpha">Alpha</option>
              <option value="beta">Beta</option>
              <option value="rc">RC</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 版本列表 -->
      <div class="divide-y divide-gray-200">
        <div
          v-for="version in filteredVersions"
          :key="version.version"
          class="px-6 py-4 hover:bg-gray-50 transition-colors duration-150"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3">
                <span class="text-lg font-semibold text-gray-900">{{ version.version }}</span>
                <span 
                  :class="getVersionTypeBadgeClass(version.type)"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                >
                  {{ version.type.toUpperCase() }}
                </span>
                <span 
                  v-if="version.version === currentVersion.version"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                >
                  当前版本
                </span>
              </div>
              
              <p class="text-gray-600 mt-1">{{ version.message || '无描述' }}</p>
              
              <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                <span>{{ formatDate(version.timestamp) }}</span>
                <span>作者: {{ version.author || 'Unknown' }}</span>
                <span v-if="version.previousVersion">
                  从 {{ version.previousVersion }} 升级
                </span>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <button
                @click="viewVersionDetails(version)"
                class="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                查看详情
              </button>
              
              <button
                v-if="version.version !== currentVersion.version"
                @click="rollbackToVersion(version.version)"
                class="text-orange-600 hover:text-orange-800 text-sm font-medium"
              >
                回滚到此版本
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.totalPages > 1" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示 {{ (pagination.page - 1) * pagination.limit + 1 }} 到 
            {{ Math.min(pagination.page * pagination.limit, pagination.total) }} 
            共 {{ pagination.total }} 条记录
          </div>
          
          <div class="flex space-x-2">
            <button
              @click="loadPage(pagination.page - 1)"
              :disabled="!pagination.hasPrev"
              class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            
            <button
              @click="loadPage(pagination.page + 1)"
              :disabled="!pagination.hasNext"
              class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 版本升级模态框 -->
    <VersionBumpModal
      v-if="showBumpModal"
      @close="showBumpModal = false"
      @success="handleBumpSuccess"
    />

    <!-- 版本回滚模态框 -->
    <VersionRollbackModal
      v-if="showRollbackModal"
      :versions="versions"
      @close="showRollbackModal = false"
      @success="handleRollbackSuccess"
    />

    <!-- 版本详情模态框 -->
    <VersionDetailsModal
      v-if="showDetailsModal"
      :version="selectedVersion"
      @close="showDetailsModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useToast } from 'vue-toastification';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// 组件导入
import VersionBumpModal from '@components/modals/VersionBumpModal.vue';
import VersionRollbackModal from '@components/modals/VersionRollbackModal.vue';
import VersionDetailsModal from '@components/modals/VersionDetailsModal.vue';

// Store 导入
import { useVersionStore } from '@stores/version';

// 响应式数据
const isLoading = ref(false);
const showBumpModal = ref(false);
const showRollbackModal = ref(false);
const showDetailsModal = ref(false);
const selectedVersion = ref(null);
const searchQuery = ref('');
const selectedFilter = ref('');

// 组合式 API
const toast = useToast();
const versionStore = useVersionStore();

// 计算属性
const currentVersion = computed(() => versionStore.currentVersion);
const versions = computed(() => versionStore.versions);
const pagination = computed(() => versionStore.pagination);

const filteredVersions = computed(() => {
  let filtered = versions.value;

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(version => 
      version.version.toLowerCase().includes(query) ||
      version.message?.toLowerCase().includes(query) ||
      version.author?.toLowerCase().includes(query)
    );
  }

  // 类型过滤
  if (selectedFilter.value) {
    filtered = filtered.filter(version => 
      version.type.includes(selectedFilter.value)
    );
  }

  return filtered;
});

// 方法
const refreshData = async () => {
  isLoading.value = true;
  try {
    await Promise.all([
      versionStore.loadCurrentVersion(),
      versionStore.loadVersionHistory()
    ]);
  } catch (error) {
    toast.error('刷新数据失败');
  } finally {
    isLoading.value = false;
  }
};

const loadPage = async (page: number) => {
  await versionStore.loadVersionHistory(page);
};

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A';
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN });
};

const getVersionTypeBadgeClass = (type: string) => {
  const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
  
  if (type.includes('major')) return `${baseClasses} bg-red-100 text-red-800`;
  if (type.includes('minor')) return `${baseClasses} bg-blue-100 text-blue-800`;
  if (type.includes('patch')) return `${baseClasses} bg-green-100 text-green-800`;
  if (type.includes('alpha')) return `${baseClasses} bg-yellow-100 text-yellow-800`;
  if (type.includes('beta')) return `${baseClasses} bg-purple-100 text-purple-800`;
  if (type.includes('rc')) return `${baseClasses} bg-indigo-100 text-indigo-800`;
  
  return `${baseClasses} bg-gray-100 text-gray-800`;
};

const viewVersionDetails = (version: any) => {
  selectedVersion.value = version;
  showDetailsModal.value = true;
};

const rollbackToVersion = (version: string) => {
  // 这里可以直接触发回滚，或者打开回滚模态框并预选版本
  showRollbackModal.value = true;
};

const handleBumpSuccess = (result: any) => {
  showBumpModal.value = false;
  toast.success(`版本升级成功: ${result.newVersion}`);
  refreshData();
};

const handleRollbackSuccess = (result: any) => {
  showRollbackModal.value = false;
  toast.success(`版本回滚成功: ${result.newVersion}`);
  refreshData();
};

// 监听搜索和过滤变化
watch([searchQuery, selectedFilter], () => {
  // 可以在这里添加防抖逻辑
});

// 生命周期
onMounted(() => {
  refreshData();
});
</script>

<style scoped>
/* 自定义样式 */
.version-card {
  transition: all 0.2s ease-in-out;
}

.version-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 搜索框动画 */
.search-input:focus {
  transform: scale(1.02);
}

/* 版本类型徽章动画 */
.version-badge {
  transition: all 0.2s ease-in-out;
}

.version-badge:hover {
  transform: scale(1.05);
}
</style>
