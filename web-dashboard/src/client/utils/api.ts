import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { useToast } from 'vue-toastification';

// API 响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  details?: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

// API 错误类
export class ApiError extends Error {
  public status: number;
  public response?: AxiosResponse;

  constructor(message: string, status: number, response?: AxiosResponse) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.response = response;
  }
}

// 创建 axios 实例
const createApiClient = (): AxiosInstance => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || '/api';
  
  const client = axios.create({
    baseURL,
    timeout: 30000, // 30秒超时
    headers: {
      'Content-Type': 'application/json',
    },
    withCredentials: true, // 支持 cookies
  });

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      // 添加认证 token
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // 添加请求 ID 用于追踪
      config.headers['X-Request-ID'] = generateRequestId();

      // 开发环境下打印请求信息
      if (import.meta.env.DEV) {
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          headers: config.headers,
          data: config.data,
          params: config.params,
        });
      }

      return config;
    },
    (error) => {
      console.error('❌ Request Error:', error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      // 开发环境下打印响应信息
      if (import.meta.env.DEV) {
        console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
          status: response.status,
          data: response.data,
        });
      }

      return response;
    },
    (error: AxiosError) => {
      const toast = useToast();

      // 开发环境下打印错误信息
      if (import.meta.env.DEV) {
        console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });
      }

      // 处理不同类型的错误
      if (error.response) {
        const { status, data } = error.response;
        
        switch (status) {
          case 401:
            // 未授权，清除 token 并跳转到登录页
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
            toast.error('登录已过期，请重新登录');
            break;
            
          case 403:
            toast.error('权限不足，无法执行此操作');
            break;
            
          case 404:
            toast.error('请求的资源不存在');
            break;
            
          case 422:
            // 验证错误，显示具体的字段错误
            if (data.errors && Array.isArray(data.errors)) {
              data.errors.forEach((err: any) => {
                toast.error(`${err.field}: ${err.message}`);
              });
            } else {
              toast.error(data.error || '请求参数验证失败');
            }
            break;
            
          case 429:
            toast.error('请求过于频繁，请稍后再试');
            break;
            
          case 500:
            toast.error('服务器内部错误，请稍后再试');
            break;
            
          default:
            toast.error(data.error || `请求失败 (${status})`);
        }

        throw new ApiError(
          data.error || `HTTP ${status} Error`,
          status,
          error.response
        );
      } else if (error.request) {
        // 网络错误
        toast.error('网络连接失败，请检查网络设置');
        throw new ApiError('Network Error', 0);
      } else {
        // 其他错误
        toast.error('请求配置错误');
        throw new ApiError(error.message, 0);
      }
    }
  );

  return client;
};

// 生成请求 ID
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 创建 API 客户端实例
export const apiClient = createApiClient();

// 便捷方法
export const api = {
  // GET 请求
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return apiClient.get(url, config);
  },

  // POST 请求
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return apiClient.post(url, data, config);
  },

  // PUT 请求
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return apiClient.put(url, data, config);
  },

  // PATCH 请求
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return apiClient.patch(url, data, config);
  },

  // DELETE 请求
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return apiClient.delete(url, config);
  },

  // 文件上传
  upload: <T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<AxiosResponse<ApiResponse<T>>> => {
    const formData = new FormData();
    formData.append('file', file);

    return apiClient.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  },

  // 下载文件
  download: (url: string, filename?: string): Promise<void> => {
    return apiClient.get(url, {
      responseType: 'blob',
    }).then((response) => {
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    });
  },
};

// 请求重试工具
export const withRetry = async <T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error as Error;
      
      if (i === maxRetries) {
        throw lastError;
      }

      // 指数退避延迟
      const retryDelay = delay * Math.pow(2, i);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  throw lastError!;
};

// 批量请求工具
export const batchRequests = async <T>(
  requests: Array<() => Promise<T>>,
  concurrency: number = 3
): Promise<T[]> => {
  const results: T[] = [];
  const executing: Promise<void>[] = [];

  for (const request of requests) {
    const promise = request().then((result) => {
      results.push(result);
    });

    executing.push(promise);

    if (executing.length >= concurrency) {
      await Promise.race(executing);
      executing.splice(executing.findIndex(p => p === promise), 1);
    }
  }

  await Promise.all(executing);
  return results;
};

// 导出默认实例
export default apiClient;
