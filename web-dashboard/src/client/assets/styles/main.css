@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 自定义字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* 基础样式重置 */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Firefox 滚动条 */
  * {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
  }
}

/* 组件样式 */
@layer components {
  /* 按钮组件 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }

  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }

  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }

  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }

  /* 卡片组件 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* 表单组件 */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm;
  }

  .form-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm;
  }

  .form-textarea {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm resize-vertical;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply text-sm text-danger-600 mt-1;
  }

  /* 徽章组件 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }

  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }

  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }

  /* 加载动画 */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  /* 模态框 */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
  }

  .modal-content {
    @apply bg-white rounded-lg shadow-xl max-w-md w-full max-h-screen overflow-y-auto;
  }

  /* 侧边栏 */
  .sidebar {
    @apply bg-white border-r border-gray-200 flex flex-col;
  }

  .sidebar-collapsed {
    @apply w-16;
  }

  .sidebar-expanded {
    @apply w-64;
  }

  /* 导航项 */
  .nav-item {
    @apply flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150;
  }

  .nav-item-active {
    @apply nav-item bg-primary-50 text-primary-700 border-r-2 border-primary-600;
  }

  /* 面包屑 */
  .breadcrumb {
    @apply flex items-center space-x-2 text-sm text-gray-500;
  }

  .breadcrumb-item {
    @apply hover:text-gray-700 transition-colors duration-150;
  }

  .breadcrumb-separator {
    @apply text-gray-400;
  }

  /* 表格 */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-row {
    @apply hover:bg-gray-50 transition-colors duration-150;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* 状态指示器 */
  .status-dot {
    @apply inline-block w-2 h-2 rounded-full;
  }

  .status-online {
    @apply status-dot bg-success-500;
  }

  .status-offline {
    @apply status-dot bg-gray-400;
  }

  .status-error {
    @apply status-dot bg-danger-500;
  }

  .status-warning {
    @apply status-dot bg-warning-500;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本截断 */
  .text-truncate {
    @apply truncate;
  }

  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 玻璃效果 */
  .glass {
    @apply bg-white bg-opacity-80 backdrop-blur-sm;
  }

  /* 渐变背景 */
  .gradient-primary {
    background: linear-gradient(135deg, theme('colors.primary.600'), theme('colors.primary.800'));
  }

  .gradient-success {
    background: linear-gradient(135deg, theme('colors.success.500'), theme('colors.success.700'));
  }

  /* 动画延迟 */
  .animate-delay-100 {
    animation-delay: 100ms;
  }

  .animate-delay-200 {
    animation-delay: 200ms;
  }

  .animate-delay-300 {
    animation-delay: 300ms;
  }

  /* 自定义阴影 */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .shadow-hard {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 2px 10px -2px rgba(0, 0, 0, 0.04);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-expanded {
    @apply fixed inset-y-0 left-0 z-40 w-64 transform transition-transform duration-300 ease-in-out;
  }

  .sidebar-collapsed {
    @apply transform -translate-x-full;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .dark {
    @apply bg-gray-900 text-gray-100;
  }

  .dark .card {
    @apply bg-gray-800 border-gray-700;
  }

  .dark .form-input,
  .dark .form-select,
  .dark .form-textarea {
    @apply bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    @apply hidden;
  }

  .print-only {
    @apply block;
  }

  body {
    @apply text-black bg-white;
  }
}
