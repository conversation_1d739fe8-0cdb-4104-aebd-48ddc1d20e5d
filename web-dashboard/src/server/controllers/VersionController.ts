import { Request, Response } from 'express';
import path from 'path';
import { VersionManager } from '../../../src/modules/version/VersionManager';
import { ConfigManager } from '../../../src/modules/config/ConfigManager';
import { VersionHistory } from '../../../src/modules/version/VersionHistory';
import { RollbackCommand } from '../../../src/commands/RollbackCommand';
import { logger } from '../utils/logger';
import { WebSocketManager } from '../websocket/WebSocketManager';

export class VersionController {
  private versionManager: VersionManager;
  private configManager: ConfigManager;
  private versionHistory: VersionHistory;
  private rollbackCommand: RollbackCommand;
  private wsManager: WebSocketManager;

  constructor() {
    // 获取项目根目录 (假设从 web-dashboard 目录运行)
    const projectRoot = process.env.PROJECT_ROOT || path.resolve(process.cwd(), '..');
    
    this.configManager = new ConfigManager(projectRoot);
    this.versionManager = new VersionManager(projectRoot, this.configManager);
    this.versionHistory = new VersionHistory(projectRoot);
    this.rollbackCommand = new RollbackCommand();
    this.wsManager = WebSocketManager.getInstance();
  }

  /**
   * 获取版本历史
   */
  async getVersionHistory(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const filter = req.query.filter as string;

      const history = await this.versionHistory.getHistory();
      
      // 过滤
      let filteredHistory = history;
      if (filter) {
        filteredHistory = history.filter(entry => 
          entry.version.includes(filter) || 
          entry.message?.includes(filter) ||
          entry.type.includes(filter)
        );
      }

      // 分页
      const total = filteredHistory.length;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedHistory = filteredHistory.slice(startIndex, endIndex);

      // 增强版本信息
      const enhancedHistory = await Promise.all(
        paginatedHistory.map(async (entry) => {
          const details = await this.getVersionDetailsInternal(entry.version);
          return {
            ...entry,
            ...details
          };
        })
      );

      res.json({
        success: true,
        data: {
          versions: enhancedHistory,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
            hasNext: endIndex < total,
            hasPrev: page > 1
          }
        }
      });
    } catch (error) {
      logger.error('获取版本历史失败:', error);
      res.status(500).json({
        success: false,
        error: '获取版本历史失败',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取当前版本
   */
  async getCurrentVersion(req: Request, res: Response): Promise<void> {
    try {
      const currentVersion = await this.versionManager.getCurrentVersion();
      const details = await this.getVersionDetailsInternal(currentVersion);

      res.json({
        success: true,
        data: {
          version: currentVersion,
          ...details
        }
      });
    } catch (error) {
      logger.error('获取当前版本失败:', error);
      res.status(500).json({
        success: false,
        error: '获取当前版本失败',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 版本升级
   */
  async bumpVersion(req: Request, res: Response): Promise<void> {
    try {
      const { type, prerelease, message } = req.body;
      
      // 通知开始升级
      this.wsManager.broadcast('version:bump:start', {
        type,
        prerelease,
        message,
        timestamp: new Date().toISOString()
      });

      const result = await this.versionManager.bumpVersion(type, prerelease);
      
      // 记录版本历史
      await this.versionHistory.addEntry({
        version: result.newVersion,
        previousVersion: result.oldVersion,
        type: prerelease ? `${type}-${prerelease}` : type,
        message: message || `${type} version bump`,
        timestamp: new Date().toISOString(),
        author: req.user?.name || 'System',
        changes: result.changes || []
      });

      // 通知升级完成
      this.wsManager.broadcast('version:bump:complete', {
        oldVersion: result.oldVersion,
        newVersion: result.newVersion,
        type,
        prerelease,
        timestamp: new Date().toISOString()
      });

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      logger.error('版本升级失败:', error);
      
      // 通知升级失败
      this.wsManager.broadcast('version:bump:error', {
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      });

      res.status(500).json({
        success: false,
        error: '版本升级失败',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 版本回滚
   */
  async rollbackVersion(req: Request, res: Response): Promise<void> {
    try {
      const { targetVersion, force } = req.body;
      
      // 通知开始回滚
      this.wsManager.broadcast('version:rollback:start', {
        targetVersion,
        force,
        timestamp: new Date().toISOString()
      });

      // 执行回滚
      await this.rollbackCommand.execute([targetVersion], { force });

      // 获取回滚后的版本信息
      const newVersion = await this.versionManager.getCurrentVersion();

      // 通知回滚完成
      this.wsManager.broadcast('version:rollback:complete', {
        targetVersion,
        newVersion,
        timestamp: new Date().toISOString()
      });

      res.json({
        success: true,
        data: {
          targetVersion,
          newVersion,
          message: `成功回滚到版本 ${targetVersion}`
        }
      });
    } catch (error) {
      logger.error('版本回滚失败:', error);
      
      // 通知回滚失败
      this.wsManager.broadcast('version:rollback:error', {
        targetVersion: req.body.targetVersion,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      });

      res.status(500).json({
        success: false,
        error: '版本回滚失败',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取版本详情
   */
  async getVersionDetails(req: Request, res: Response): Promise<void> {
    try {
      const { version } = req.params;
      const details = await this.getVersionDetailsInternal(version);

      res.json({
        success: true,
        data: details
      });
    } catch (error) {
      logger.error('获取版本详情失败:', error);
      res.status(500).json({
        success: false,
        error: '获取版本详情失败',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 内部方法：获取版本详情
   */
  private async getVersionDetailsInternal(version: string): Promise<any> {
    try {
      // 这里可以集成更多版本详情信息
      return {
        hasBuilds: false, // TODO: 检查是否有构建记录
        hasDeployments: false, // TODO: 检查是否有部署记录
        gitTag: `v${version}`,
        createdAt: new Date().toISOString(), // TODO: 从 Git 获取实际创建时间
        fileChanges: [], // TODO: 获取文件变更信息
        buildStatus: 'unknown', // TODO: 获取构建状态
        deploymentStatus: 'unknown' // TODO: 获取部署状态
      };
    } catch (error) {
      logger.warn(`获取版本 ${version} 详情时出错:`, error);
      return {};
    }
  }
}
