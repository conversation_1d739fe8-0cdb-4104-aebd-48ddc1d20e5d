import { Server as SocketIOServer } from 'socket.io';
import { WebSocketManager } from './WebSocketManager';
import { logger } from '../utils/logger';

export const websocketHandler = (io: SocketIOServer) => {
  // 初始化 WebSocket 管理器
  const wsManager = WebSocketManager.getInstance();
  wsManager.initialize(io);

  logger.info('WebSocket 服务器已初始化');

  // 可以在这里添加额外的 WebSocket 事件处理逻辑
  io.on('connection', (socket) => {
    logger.info(`新的 WebSocket 连接: ${socket.id}`);

    // 处理客户端特定事件
    socket.on('join-room', (room: string) => {
      socket.join(room);
      logger.debug(`客户端 ${socket.id} 加入房间: ${room}`);
    });

    socket.on('leave-room', (room: string) => {
      socket.leave(room);
      logger.debug(`客户端 ${socket.id} 离开房间: ${room}`);
    });

    // 处理自定义事件
    socket.on('custom-event', (data: any) => {
      logger.debug(`收到自定义事件:`, data);
      // 处理自定义事件逻辑
    });

    socket.on('disconnect', (reason) => {
      logger.info(`WebSocket 连接断开: ${socket.id}, 原因: ${reason}`);
    });
  });
};
