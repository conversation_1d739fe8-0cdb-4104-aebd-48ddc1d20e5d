import { Server as SocketIOServer, Socket } from 'socket.io';
import { logger } from '../utils/logger';

export class WebSocketManager {
  private static instance: WebSocketManager;
  private io: SocketIOServer | null = null;
  private connectedClients: Map<string, Socket> = new Map();

  private constructor() {}

  public static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  public initialize(io: SocketIOServer): void {
    this.io = io;
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    if (!this.io) return;

    this.io.on('connection', (socket: Socket) => {
      logger.info(`客户端连接: ${socket.id}`);
      this.connectedClients.set(socket.id, socket);

      // 发送欢迎消息
      socket.emit('connected', {
        message: 'Welcome to Version-Craft Dashboard',
        timestamp: new Date().toISOString(),
        clientId: socket.id
      });

      // 处理客户端订阅
      socket.on('subscribe', (channels: string[]) => {
        channels.forEach(channel => {
          socket.join(channel);
          logger.debug(`客户端 ${socket.id} 订阅频道: ${channel}`);
        });
      });

      // 处理客户端取消订阅
      socket.on('unsubscribe', (channels: string[]) => {
        channels.forEach(channel => {
          socket.leave(channel);
          logger.debug(`客户端 ${socket.id} 取消订阅频道: ${channel}`);
        });
      });

      // 处理心跳
      socket.on('ping', () => {
        socket.emit('pong', { timestamp: new Date().toISOString() });
      });

      // 处理断开连接
      socket.on('disconnect', (reason) => {
        logger.info(`客户端断开连接: ${socket.id}, 原因: ${reason}`);
        this.connectedClients.delete(socket.id);
      });

      // 处理错误
      socket.on('error', (error) => {
        logger.error(`WebSocket 错误 (${socket.id}):`, error);
      });
    });
  }

  /**
   * 广播消息给所有连接的客户端
   */
  public broadcast(event: string, data: any): void {
    if (!this.io) {
      logger.warn('WebSocket 服务器未初始化，无法广播消息');
      return;
    }

    this.io.emit(event, {
      ...data,
      timestamp: data.timestamp || new Date().toISOString()
    });

    logger.debug(`广播消息: ${event}`, data);
  }

  /**
   * 向特定频道发送消息
   */
  public broadcastToChannel(channel: string, event: string, data: any): void {
    if (!this.io) {
      logger.warn('WebSocket 服务器未初始化，无法发送频道消息');
      return;
    }

    this.io.to(channel).emit(event, {
      ...data,
      timestamp: data.timestamp || new Date().toISOString()
    });

    logger.debug(`向频道 ${channel} 发送消息: ${event}`, data);
  }

  /**
   * 向特定客户端发送消息
   */
  public sendToClient(clientId: string, event: string, data: any): void {
    const socket = this.connectedClients.get(clientId);
    if (!socket) {
      logger.warn(`客户端 ${clientId} 不存在或已断开连接`);
      return;
    }

    socket.emit(event, {
      ...data,
      timestamp: data.timestamp || new Date().toISOString()
    });

    logger.debug(`向客户端 ${clientId} 发送消息: ${event}`, data);
  }

  /**
   * 获取连接的客户端数量
   */
  public getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  /**
   * 获取所有连接的客户端ID
   */
  public getConnectedClientIds(): string[] {
    return Array.from(this.connectedClients.keys());
  }

  /**
   * 发送系统状态更新
   */
  public broadcastSystemStatus(status: any): void {
    this.broadcast('system:status', status);
  }

  /**
   * 发送构建进度更新
   */
  public broadcastBuildProgress(buildId: string, progress: any): void {
    this.broadcastToChannel('builds', 'build:progress', {
      buildId,
      ...progress
    });
  }

  /**
   * 发送部署进度更新
   */
  public broadcastDeploymentProgress(deploymentId: string, progress: any): void {
    this.broadcastToChannel('deployments', 'deployment:progress', {
      deploymentId,
      ...progress
    });
  }

  /**
   * 发送版本变更通知
   */
  public broadcastVersionChange(change: any): void {
    this.broadcast('version:changed', change);
  }

  /**
   * 发送错误通知
   */
  public broadcastError(error: any): void {
    this.broadcast('error', {
      message: error.message || '发生未知错误',
      type: error.type || 'general',
      details: error.details || null
    });
  }

  /**
   * 发送成功通知
   */
  public broadcastSuccess(message: string, data?: any): void {
    this.broadcast('success', {
      message,
      data: data || null
    });
  }

  /**
   * 发送警告通知
   */
  public broadcastWarning(message: string, data?: any): void {
    this.broadcast('warning', {
      message,
      data: data || null
    });
  }

  /**
   * 发送信息通知
   */
  public broadcastInfo(message: string, data?: any): void {
    this.broadcast('info', {
      message,
      data: data || null
    });
  }
}
