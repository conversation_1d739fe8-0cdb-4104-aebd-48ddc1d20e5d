import fs from 'fs';
import path from 'path';

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  data?: any;
  requestId?: string;
}

class Logger {
  private logLevel: LogLevel;
  private logFile?: string;

  constructor() {
    this.logLevel = this.getLogLevel();
    this.logFile = process.env.LOG_FILE;
    
    // 确保日志目录存在
    if (this.logFile) {
      const logDir = path.dirname(this.logFile);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
    }
  }

  private getLogLevel(): LogLevel {
    const level = process.env.LOG_LEVEL?.toLowerCase() || 'info';
    switch (level) {
      case 'error': return LogLevel.ERROR;
      case 'warn': return LogLevel.WARN;
      case 'info': return LogLevel.INFO;
      case 'debug': return LogLevel.DEBUG;
      default: return LogLevel.INFO;
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.logLevel;
  }

  private formatMessage(level: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const logEntry: LogEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      ...(data && { data })
    };

    return JSON.stringify(logEntry);
  }

  private writeLog(level: string, message: string, data?: any): void {
    const formattedMessage = this.formatMessage(level, message, data);
    
    // 控制台输出
    const coloredMessage = this.colorizeMessage(level, formattedMessage);
    console.log(coloredMessage);

    // 文件输出
    if (this.logFile) {
      fs.appendFileSync(this.logFile, formattedMessage + '\n');
    }
  }

  private colorizeMessage(level: string, message: string): string {
    const colors = {
      ERROR: '\x1b[31m', // 红色
      WARN: '\x1b[33m',  // 黄色
      INFO: '\x1b[36m',  // 青色
      DEBUG: '\x1b[37m', // 白色
      RESET: '\x1b[0m'   // 重置
    };

    const color = colors[level.toUpperCase() as keyof typeof colors] || colors.INFO;
    return `${color}${message}${colors.RESET}`;
  }

  error(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      this.writeLog('error', message, data);
    }
  }

  warn(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.WARN)) {
      this.writeLog('warn', message, data);
    }
  }

  info(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.INFO)) {
      this.writeLog('info', message, data);
    }
  }

  debug(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      this.writeLog('debug', message, data);
    }
  }

  // 获取日志文件内容
  getLogs(level?: string, limit: number = 100): LogEntry[] {
    if (!this.logFile || !fs.existsSync(this.logFile)) {
      return [];
    }

    try {
      const content = fs.readFileSync(this.logFile, 'utf-8');
      const lines = content.trim().split('\n').filter(line => line.trim());
      
      let logs = lines
        .map(line => {
          try {
            return JSON.parse(line) as LogEntry;
          } catch {
            return null;
          }
        })
        .filter(log => log !== null) as LogEntry[];

      // 按级别过滤
      if (level) {
        logs = logs.filter(log => log.level.toLowerCase() === level.toLowerCase());
      }

      // 限制数量并按时间倒序
      return logs
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit);
    } catch (error) {
      this.error('读取日志文件失败', error);
      return [];
    }
  }

  // 清理旧日志
  cleanOldLogs(days: number = 7): void {
    if (!this.logFile || !fs.existsSync(this.logFile)) {
      return;
    }

    try {
      const content = fs.readFileSync(this.logFile, 'utf-8');
      const lines = content.trim().split('\n').filter(line => line.trim());
      
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const filteredLines = lines.filter(line => {
        try {
          const log = JSON.parse(line) as LogEntry;
          return new Date(log.timestamp) > cutoffDate;
        } catch {
          return false;
        }
      });

      fs.writeFileSync(this.logFile, filteredLines.join('\n') + '\n');
      this.info(`清理了 ${lines.length - filteredLines.length} 条旧日志`);
    } catch (error) {
      this.error('清理日志失败', error);
    }
  }
}

// 创建全局日志实例
export const logger = new Logger();

// 定期清理日志 (每天执行一次)
setInterval(() => {
  logger.cleanOldLogs();
}, 24 * 60 * 60 * 1000);
