import { Router } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { body, validationResult } from 'express-validator';

const router = Router();

// 临时用户存储 (生产环境应使用数据库)
const users = [
  {
    id: 1,
    username: 'admin',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
    role: 'admin',
    name: 'Administrator'
  }
];

// 验证中间件
const validate = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  next();
};

// 登录
router.post('/login',
  body('username').isLength({ min: 3 }).withMessage('用户名至少3个字符'),
  body('password').isLength({ min: 6 }).withMessage('密码至少6个字符'),
  validate,
  async (req, res) => {
    try {
      const { username, password } = req.body;

      // 查找用户
      const user = users.find(u => u.username === username);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: '用户名或密码错误'
        });
      }

      // 验证密码
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          error: '用户名或密码错误'
        });
      }

      // 生成 JWT token
      const token = jwt.sign(
        { 
          id: user.id, 
          username: user.username, 
          role: user.role 
        },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
      );

      res.json({
        success: true,
        data: {
          token,
          user: {
            id: user.id,
            username: user.username,
            name: user.name,
            role: user.role
          }
        }
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        error: '登录失败'
      });
    }
  }
);

// 注册 (可选)
router.post('/register',
  body('username').isLength({ min: 3 }).withMessage('用户名至少3个字符'),
  body('password').isLength({ min: 6 }).withMessage('密码至少6个字符'),
  body('name').isLength({ min: 2 }).withMessage('姓名至少2个字符'),
  validate,
  async (req, res) => {
    try {
      const { username, password, name } = req.body;

      // 检查用户是否已存在
      const existingUser = users.find(u => u.username === username);
      if (existingUser) {
        return res.status(400).json({
          success: false,
          error: '用户名已存在'
        });
      }

      // 加密密码
      const hashedPassword = await bcrypt.hash(password, 10);

      // 创建新用户
      const newUser = {
        id: users.length + 1,
        username,
        password: hashedPassword,
        name,
        role: 'user'
      };

      users.push(newUser);

      res.json({
        success: true,
        data: {
          message: '注册成功',
          user: {
            id: newUser.id,
            username: newUser.username,
            name: newUser.name,
            role: newUser.role
          }
        }
      });
    } catch (error) {
      console.error('Register error:', error);
      res.status(500).json({
        success: false,
        error: '注册失败'
      });
    }
  }
);

// 获取当前用户信息
router.get('/me', (req: any, res) => {
  // 这个路由需要认证中间件，但为了简化，我们先返回默认用户
  res.json({
    success: true,
    data: {
      id: 1,
      username: 'admin',
      name: 'Administrator',
      role: 'admin'
    }
  });
});

// 登出
router.post('/logout', (req, res) => {
  res.json({
    success: true,
    data: {
      message: '登出成功'
    }
  });
});

export { router as authRouter };
