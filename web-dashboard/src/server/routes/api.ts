import { Router } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { VersionController } from '../controllers/VersionController';
import { BuildController } from '../controllers/BuildController';
import { DeployController } from '../controllers/DeployController';
import { ProjectController } from '../controllers/ProjectController';
import { SystemController } from '../controllers/SystemController';

const router = Router();

// 验证中间件
const validate = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  next();
};

// 版本管理路由
const versionController = new VersionController();

// 获取版本历史
router.get('/versions', 
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('filter').optional().isString(),
  validate,
  versionController.getVersionHistory.bind(versionController)
);

// 获取当前版本
router.get('/versions/current', 
  versionController.getCurrentVersion.bind(versionController)
);

// 版本升级
router.post('/versions/bump',
  body('type').isIn(['major', 'minor', 'patch']),
  body('prerelease').optional().isIn(['alpha', 'beta', 'rc']),
  body('message').optional().isString().isLength({ max: 500 }),
  validate,
  versionController.bumpVersion.bind(versionController)
);

// 版本回滚
router.post('/versions/rollback',
  body('targetVersion').isString().matches(/^\d+\.\d+\.\d+/),
  body('force').optional().isBoolean(),
  validate,
  versionController.rollbackVersion.bind(versionController)
);

// 获取版本详情
router.get('/versions/:version',
  param('version').isString(),
  validate,
  versionController.getVersionDetails.bind(versionController)
);

// 构建管理路由
const buildController = new BuildController();

// 获取构建历史
router.get('/builds',
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('platform').optional().isString(),
  query('status').optional().isIn(['pending', 'running', 'success', 'failed']),
  validate,
  buildController.getBuildHistory.bind(buildController)
);

// 开始构建
router.post('/builds',
  body('platform').isIn(['web-mobile', 'android', 'ios', 'windows', 'mac']),
  body('version').optional().isString(),
  body('options').optional().isObject(),
  validate,
  buildController.startBuild.bind(buildController)
);

// 获取构建状态
router.get('/builds/:buildId',
  param('buildId').isString(),
  validate,
  buildController.getBuildStatus.bind(buildController)
);

// 取消构建
router.delete('/builds/:buildId',
  param('buildId').isString(),
  validate,
  buildController.cancelBuild.bind(buildController)
);

// 部署管理路由
const deployController = new DeployController();

// 获取部署历史
router.get('/deployments',
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('environment').optional().isString(),
  validate,
  deployController.getDeploymentHistory.bind(deployController)
);

// 开始部署
router.post('/deployments',
  body('version').isString(),
  body('environment').isIn(['development', 'staging', 'production']),
  body('options').optional().isObject(),
  validate,
  deployController.startDeployment.bind(deployController)
);

// 获取环境状态
router.get('/environments',
  deployController.getEnvironmentStatus.bind(deployController)
);

// 项目管理路由
const projectController = new ProjectController();

// 获取项目信息
router.get('/project',
  projectController.getProjectInfo.bind(projectController)
);

// 更新项目配置
router.put('/project/config',
  body('config').isObject(),
  validate,
  projectController.updateConfig.bind(projectController)
);

// 获取项目统计
router.get('/project/stats',
  projectController.getProjectStats.bind(projectController)
);

// 系统管理路由
const systemController = new SystemController();

// 获取系统状态
router.get('/system/status',
  systemController.getSystemStatus.bind(systemController)
);

// 获取系统日志
router.get('/system/logs',
  query('level').optional().isIn(['error', 'warn', 'info', 'debug']),
  query('limit').optional().isInt({ min: 1, max: 1000 }),
  validate,
  systemController.getLogs.bind(systemController)
);

// 清理系统缓存
router.delete('/system/cache',
  systemController.clearCache.bind(systemController)
);

export { router as apiRouter };
