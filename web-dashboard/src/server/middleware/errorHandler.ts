import { Request, Response, NextFunction } from 'express';

export interface AppError extends Error {
  statusCode?: number;
  status?: string;
  isOperational?: boolean;
}

export const errorHandler = (
  err: AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let error = { ...err };
  error.message = err.message;

  // 设置默认错误
  let statusCode = error.statusCode || 500;
  let message = error.message || '服务器内部错误';

  // Mongoose 错误处理
  if (err.name === 'CastError') {
    message = '资源未找到';
    statusCode = 404;
  }

  // Mongoose 重复字段错误
  if (err.name === 'MongoError' && (err as any).code === 11000) {
    message = '重复字段值';
    statusCode = 400;
  }

  // Mongoose 验证错误
  if (err.name === 'ValidationError') {
    const errors = Object.values((err as any).errors).map((val: any) => val.message);
    message = `输入数据无效: ${errors.join(', ')}`;
    statusCode = 400;
  }

  // JWT 错误
  if (err.name === 'JsonWebTokenError') {
    message = '无效的访问令牌';
    statusCode = 401;
  }

  if (err.name === 'TokenExpiredError') {
    message = '访问令牌已过期';
    statusCode = 401;
  }

  // 开发环境下显示详细错误信息
  const isDevelopment = process.env.NODE_ENV === 'development';

  console.error('Error:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  res.status(statusCode).json({
    success: false,
    error: message,
    ...(isDevelopment && {
      details: err.message,
      stack: err.stack
    })
  });
};

// 404 处理中间件
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error: AppError = new Error(`路径 ${req.originalUrl} 未找到`);
  error.statusCode = 404;
  next(error);
};

// 异步错误捕获包装器
export const asyncHandler = (fn: Function) => (req: Request, res: Response, next: NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};
