{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "target": "ES2020",
    "module": "CommonJS",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "noEmit": false,
    "jsx": "react",
    
    /* Node.js specific */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    
    /* Path mapping for server */
    "baseUrl": ".",
    "paths": {
      "@server/*": ["src/server/*"],
      "@shared/*": ["src/shared/*"]
    },

    /* Type definitions */
    "types": ["node", "@types/express", "@types/cors"]
  },
  "include": [
    "src/server/**/*.ts",
    "src/shared/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "src/client"
  ],
  "ts-node": {
    "esm": false,
    "experimentalSpecifierResolution": "node"
  }
}
