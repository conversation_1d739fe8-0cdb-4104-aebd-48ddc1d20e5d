{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/client/*"],
      "@components/*": ["src/client/components/*"],
      "@views/*": ["src/client/views/*"],
      "@stores/*": ["src/client/stores/*"],
      "@utils/*": ["src/client/utils/*"],
      "@types/*": ["src/client/types/*"],
      "@assets/*": ["src/client/assets/*"]
    },

    /* Type definitions */
    "types": ["vite/client", "node"]
  },
  "include": [
    "src/client/**/*.ts",
    "src/client/**/*.d.ts",
    "src/client/**/*.tsx",
    "src/client/**/*.vue"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "src/server"
  ],
  "references": [
    { "path": "./tsconfig.server.json" }
  ]
}
