{"project": {"name": "test-keywords-only", "type": "cocos-creator", "version": "1.0.0", "description": "Test project with only keywords"}, "build": {"platforms": ["web-mobile"], "outputDir": "./dist", "cocosCreator": {"projectPath": "."}, "optimization": {"compress": true, "minify": true, "sourcemap": false}, "excludeFiles": ["*.log", "node_modules/**", ".git/**", "temp/**", "library/**", "local/**", "build/**"]}, "deploy": {"web": {"staging": "https://staging.example.com", "production": "https://production.example.com"}}, "environments": {"dev": "./config/dev", "test": "./config/test", "prod": "./config/prod"}, "git": {"autoTag": true, "tagPrefix": "v", "generateChangelog": true, "changelogPath": "./CHANGELOG.md"}, "notification": {"enabled": false}}