<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900">版本管理</h1>
      <div class="flex space-x-3">
        <button
          @click="refreshData"
          :disabled="isLoading"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <svg class="w-4 h-4 mr-2" :class="{ 'animate-spin': isLoading }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          刷新
        </button>
        
        <button
          @click="showBumpModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          版本升级
        </button>
      </div>
    </div>

    <!-- 当前版本卡片 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-2">当前版本</h2>
          <div class="flex items-center space-x-4">
            <span class="text-3xl font-bold text-blue-600">{{ currentVersion?.version || 'N/A' }}</span>
            <div class="flex space-x-2">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                稳定版
              </span>
            </div>
          </div>
          <p class="text-gray-600 mt-2">
            创建时间: {{ formatDate(currentVersion?.lastModified) }}
          </p>
        </div>
        
        <div class="text-right">
          <button
            @click="showRollbackModal = true"
            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
            </svg>
            版本回滚
          </button>
        </div>
      </div>
    </div>

    <!-- 版本历史 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">版本历史</h2>
      </div>

      <div class="divide-y divide-gray-200">
        <div
          v-for="version in versionHistory"
          :key="version.version"
          class="px-6 py-4 hover:bg-gray-50 transition-colors duration-150"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3">
                <span class="text-lg font-semibold text-gray-900">{{ version.version }}</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {{ version.type || 'release' }}
                </span>
                <span 
                  v-if="version.version === currentVersion?.version"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                >
                  当前版本
                </span>
              </div>
              
              <p class="text-gray-600 mt-1">{{ version.message || '无描述' }}</p>
              
              <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                <span>{{ formatDate(version.date) }}</span>
                <span>作者: {{ version.author || 'Unknown' }}</span>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <button
                v-if="version.version !== currentVersion?.version"
                @click="rollbackToVersion(version.version)"
                class="text-orange-600 hover:text-orange-800 text-sm font-medium"
              >
                回滚到此版本
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 版本升级模态框 -->
    <div v-if="showBumpModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">版本升级</h3>
        </div>
        <div class="px-6 py-4">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">升级类型</label>
              <div class="space-y-2">
                <label class="flex items-center">
                  <input v-model="bumpType" type="radio" value="patch" class="mr-2">
                  <span>Patch ({{ getNextVersion('patch') }}) - 修复更新</span>
                </label>
                <label class="flex items-center">
                  <input v-model="bumpType" type="radio" value="minor" class="mr-2">
                  <span>Minor ({{ getNextVersion('minor') }}) - 功能更新</span>
                </label>
                <label class="flex items-center">
                  <input v-model="bumpType" type="radio" value="major" class="mr-2">
                  <span>Major ({{ getNextVersion('major') }}) - 重大更新</span>
                </label>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">更新说明</label>
              <textarea
                v-model="bumpMessage"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows="3"
                placeholder="请输入版本更新说明..."
              ></textarea>
            </div>
          </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showBumpModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            @click="executeBump"
            :disabled="!bumpType"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            确认升级
          </button>
        </div>
      </div>
    </div>

    <!-- 版本回滚模态框 -->
    <div v-if="showRollbackModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">版本回滚</h3>
        </div>
        <div class="px-6 py-4">
          <p class="text-gray-600 mb-4">选择要回滚到的版本：</p>
          <div class="space-y-2 max-h-60 overflow-y-auto">
            <label
              v-for="version in availableVersions"
              :key="version.version"
              class="flex items-center p-2 border rounded hover:bg-gray-50"
            >
              <input v-model="rollbackTarget" type="radio" :value="version.version" class="mr-3">
              <div>
                <div class="font-medium">{{ version.version }}</div>
                <div class="text-sm text-gray-500">{{ formatDate(version.date) }}</div>
              </div>
            </label>
          </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showRollbackModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            @click="executeRollback"
            :disabled="!rollbackTarget"
            class="px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-md hover:bg-orange-700 disabled:opacity-50"
          >
            确认回滚
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useAppStore } from '../stores/app';

// Store
const appStore = useAppStore();

// 响应式数据
const isLoading = ref(false);
const showBumpModal = ref(false);
const showRollbackModal = ref(false);
const bumpType = ref<'major' | 'minor' | 'patch'>('patch');
const bumpMessage = ref('');
const rollbackTarget = ref('');

// 计算属性
const currentVersion = computed(() => appStore.currentVersion);
const versionHistory = computed(() => appStore.versionHistory);

const availableVersions = computed(() => {
  return versionHistory.value.filter(v => v.version !== currentVersion.value?.version);
});

// 方法
const refreshData = async () => {
  isLoading.value = true;
  try {
    await appStore.refreshAll();
  } catch (error) {
    console.error('Refresh data error:', error);
  } finally {
    isLoading.value = false;
  }
};

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A';
  try {
    return format(new Date(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN });
  } catch {
    return 'N/A';
  }
};

const getNextVersion = (type: 'major' | 'minor' | 'patch') => {
  const current = currentVersion.value?.version || '1.0.0';
  const parts = current.split('.').map(Number);
  
  switch (type) {
    case 'major':
      return `${parts[0] + 1}.0.0`;
    case 'minor':
      return `${parts[0]}.${parts[1] + 1}.0`;
    case 'patch':
      return `${parts[0]}.${parts[1]}.${parts[2] + 1}`;
    default:
      return current;
  }
};

const executeBump = async () => {
  console.log('[VersionManagement] User clicked confirm bump');
  console.log('[VersionManagement] Bump parameters:', {
    type: bumpType.value,
    message: bumpMessage.value
  });

  try {
    console.log('[VersionManagement] Calling appStore.bumpVersion...');
    await appStore.bumpVersion({
      type: bumpType.value,
      message: bumpMessage.value
    });

    console.log('[VersionManagement] Version bump completed, closing modal');
    showBumpModal.value = false;
    bumpMessage.value = '';

    // 刷新数据
    console.log('[VersionManagement] Refreshing page data...');
    await refreshData();
    console.log('[VersionManagement] Data refresh completed');
  } catch (error) {
    console.error('[VersionManagement] Version bump failed:', error);
    // 这里应该显示错误提示给用户
    alert(`Version bump failed: ${error instanceof Error ? error.message : error}`);
  }
};

const executeRollback = async () => {
  try {
    await appStore.rollbackVersion(rollbackTarget.value);
    
    showRollbackModal.value = false;
    rollbackTarget.value = '';
    
    // 刷新数据
    await refreshData();
  } catch (error) {
    console.error('Rollback version error:', error);
  }
};

const rollbackToVersion = (version: string) => {
  rollbackTarget.value = version;
  showRollbackModal.value = true;
};

// 生命周期
onMounted(() => {
  if (appStore.hasProject) {
    refreshData();
  }
});
</script>
