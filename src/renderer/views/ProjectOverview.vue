<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900">项目概览</h1>
      <button
        @click="refreshData"
        :disabled="isLoading"
        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
      >
        <ArrowPathIcon class="w-4 h-4 mr-2" :class="{ 'animate-spin': isLoading }" />
        刷新
      </button>
    </div>

    <!-- 项目信息卡片 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 基本信息 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">项目信息</h2>
        
        <div v-if="projectInfo" class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-600">项目名称:</span>
            <span class="font-medium">{{ projectInfo.package?.name || projectName }}</span>
          </div>
          
          <div class="flex justify-between">
            <span class="text-gray-600">当前版本:</span>
            <span class="font-medium text-blue-600">{{ currentVersion?.version || 'N/A' }}</span>
          </div>
          
          <div class="flex justify-between">
            <span class="text-gray-600">项目路径:</span>
            <span class="font-mono text-sm text-gray-500 truncate max-w-xs" :title="projectInfo.path">
              {{ projectInfo.path }}
            </span>
          </div>
          
          <div class="flex justify-between">
            <span class="text-gray-600">描述:</span>
            <span class="text-gray-800">{{ projectInfo.package?.description || '无描述' }}</span>
          </div>
          
          <div class="flex justify-between">
            <span class="text-gray-600">Git 仓库:</span>
            <span class="flex items-center">
              <span :class="projectInfo.git?.hasGit ? 'text-green-600' : 'text-gray-400'">
                {{ projectInfo.git?.hasGit ? '已初始化' : '未初始化' }}
              </span>
              <div 
                :class="projectInfo.git?.hasGit ? 'bg-green-500' : 'bg-gray-400'"
                class="w-2 h-2 rounded-full ml-2"
              ></div>
            </span>
          </div>
        </div>
        
        <div v-else class="text-center text-gray-500 py-8">
          <DocumentIcon class="w-12 h-12 mx-auto mb-2 text-gray-400" />
          <p>加载项目信息中...</p>
        </div>
      </div>

      <!-- 项目统计 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">项目统计</h2>
        
        <div v-if="projectInfo?.stats" class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center p-3 bg-blue-50 rounded-lg">
              <div class="text-2xl font-bold text-blue-600">{{ projectInfo.stats.totalFiles }}</div>
              <div class="text-sm text-gray-600">总文件数</div>
            </div>
            
            <div class="text-center p-3 bg-green-50 rounded-lg">
              <div class="text-2xl font-bold text-green-600">{{ formatFileSize(projectInfo.stats.totalSize) }}</div>
              <div class="text-sm text-gray-600">项目大小</div>
            </div>
          </div>
          
          <div class="space-y-2">
            <h3 class="font-medium text-gray-900">目录统计</h3>
            
            <div v-for="(dir, name) in projectInfo.stats.directories" :key="name" class="flex justify-between items-center">
              <span class="text-gray-600 capitalize">{{ name }}:</span>
              <span v-if="dir.exists" class="text-sm">
                {{ dir.fileCount }} 文件, {{ formatFileSize(dir.size) }}
              </span>
              <span v-else class="text-gray-400 text-sm">不存在</span>
            </div>
          </div>
          
          <div class="pt-2 border-t border-gray-200">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">最后修改:</span>
              <span class="text-gray-800">{{ formatDate(projectInfo.stats.lastModified) }}</span>
            </div>
          </div>
        </div>
        
        <div v-else class="text-center text-gray-500 py-8">
          <ChartBarIcon class="w-12 h-12 mx-auto mb-2 text-gray-400" />
          <p>加载统计信息中...</p>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <router-link
          to="/version"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
        >
          <div class="flex-shrink-0">
            <TagIcon class="w-8 h-8 text-blue-600 group-hover:text-blue-700" />
          </div>
          <div class="ml-4">
            <h3 class="font-medium text-gray-900">版本管理</h3>
            <p class="text-sm text-gray-600">升级版本、回滚版本</p>
          </div>
        </router-link>
        
        <router-link
          to="/build"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
        >
          <div class="flex-shrink-0">
            <CogIcon class="w-8 h-8 text-green-600 group-hover:text-green-700" />
          </div>
          <div class="ml-4">
            <h3 class="font-medium text-gray-900">构建项目</h3>
            <p class="text-sm text-gray-600">多平台构建管理</p>
          </div>
        </router-link>
        
        <router-link
          to="/config"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
        >
          <div class="flex-shrink-0">
            <AdjustmentsHorizontalIcon class="w-8 h-8 text-purple-600 group-hover:text-purple-700" />
          </div>
          <div class="ml-4">
            <h3 class="font-medium text-gray-900">项目配置</h3>
            <p class="text-sm text-gray-600">编辑项目设置</p>
          </div>
        </router-link>
      </div>
    </div>

    <!-- 最近版本历史 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-900">最近版本</h2>
        <router-link
          to="/version"
          class="text-blue-600 hover:text-blue-700 text-sm font-medium"
        >
          查看全部 →
        </router-link>
      </div>
      
      <div v-if="recentVersions.length > 0" class="space-y-3">
        <div
          v-for="version in recentVersions"
          :key="version.version"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span class="font-medium">{{ version.version }}</span>
            <span class="text-sm text-gray-600">{{ version.message || '无描述' }}</span>
          </div>
          <span class="text-sm text-gray-500">{{ formatDate(version.date) }}</span>
        </div>
      </div>
      
      <div v-else class="text-center text-gray-500 py-8">
        <ClockIcon class="w-12 h-12 mx-auto mb-2 text-gray-400" />
        <p>暂无版本历史</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  ArrowPathIcon,
  DocumentIcon,
  ChartBarIcon,
  TagIcon,
  CogIcon,
  AdjustmentsHorizontalIcon,
  ClockIcon
} from '@heroicons/vue/24/outline';

import { useAppStore } from '../stores/app';

// Store
const appStore = useAppStore();

// 计算属性
const projectInfo = computed(() => appStore.projectInfo);
const currentVersion = computed(() => appStore.currentVersion);
const versionHistory = computed(() => appStore.versionHistory);
const isLoading = computed(() => appStore.isLoading);
const projectName = computed(() => appStore.projectName);

const recentVersions = computed(() => {
  return versionHistory.value.slice(0, 5);
});

// 方法
const refreshData = async () => {
  try {
    await appStore.refreshAll();
  } catch (error) {
    console.error('Refresh data error:', error);
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

const formatDate = (dateString: string): string => {
  if (!dateString) return 'N/A';
  try {
    return format(new Date(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN });
  } catch {
    return 'N/A';
  }
};

// 生命周期
onMounted(() => {
  if (appStore.hasProject && !projectInfo.value) {
    refreshData();
  }
});
</script>
