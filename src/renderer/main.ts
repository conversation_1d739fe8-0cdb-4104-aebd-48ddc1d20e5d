import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';

// 样式导入
import './assets/styles/main.css';

// 创建应用实例
const app = createApp(App);

// 状态管理
const pinia = createPinia();
app.use(pinia);

// 路由
app.use(router);

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err);
  console.error('Component:', vm);
  console.error('Info:', info);
};

// 挂载应用
app.mount('#app');

// 开发环境调试信息
if (import.meta.env.DEV) {
  console.log('🚀 Version-Craft GUI');
  console.log('🌍 Environment:', import.meta.env.MODE);
}
