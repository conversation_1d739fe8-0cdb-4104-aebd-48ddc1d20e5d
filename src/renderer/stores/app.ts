import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useAppStore = defineStore('app', () => {
  // 状态
  const currentProject = ref<string | null>(null);
  const projectInfo = ref<any>(null);
  const currentVersion = ref<any>(null);
  const versionHistory = ref<any[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // 计算属性
  const hasProject = computed(() => !!currentProject.value);
  const projectName = computed(() => {
    if (!currentProject.value) return '';
    return currentProject.value.split(/[/\\]/).pop() || '';
  });

  // 操作方法
  const setCurrentProject = async (projectPath: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      currentProject.value = projectPath;

      // 检查 electronAPI 是否可用
      if (!window.electronAPI) {
        console.warn('electronAPI 不可用，使用模拟数据');
        // 设置模拟数据
        projectInfo.value = {
          name: 'Demo Project',
          path: projectPath,
          package: { name: 'demo-project', version: '1.0.0' }
        };
        currentVersion.value = { version: '1.0.0', lastModified: new Date().toISOString() };
        versionHistory.value = [
          { version: '1.0.0', date: new Date().toISOString(), message: '初始版本' }
        ];
        return;
      }

      // 加载项目信息
      await loadProjectInfo();
      await loadCurrentVersion();
      await loadVersionHistory();

    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载项目失败';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const loadProjectInfo = async () => {
    if (!currentProject.value) return;
    
    try {
      const result = await window.electronAPI.getProjectInfo();
      if (result.success) {
        projectInfo.value = result.data;
      } else {
        throw new Error(result.error || '获取项目信息失败');
      }
    } catch (err) {
      console.error('Load project info error:', err);
      throw err;
    }
  };

  const loadCurrentVersion = async () => {
    if (!currentProject.value) return;
    
    try {
      const result = await window.electronAPI.getCurrentVersion();
      if (result.success) {
        currentVersion.value = result.data;
      } else {
        throw new Error(result.error || '获取当前版本失败');
      }
    } catch (err) {
      console.error('Load current version error:', err);
      throw err;
    }
  };

  const loadVersionHistory = async () => {
    if (!currentProject.value) return;
    
    try {
      const result = await window.electronAPI.getVersionHistory();
      if (result.success) {
        versionHistory.value = result.data || [];
      } else {
        throw new Error(result.error || '获取版本历史失败');
      }
    } catch (err) {
      console.error('Load version history error:', err);
      throw err;
    }
  };

  const bumpVersion = async (options: {
    type: 'major' | 'minor' | 'patch';
    prerelease?: string;
    message?: string;
  }) => {
    if (!currentProject.value) {
      throw new Error('未选择项目');
    }

    try {
      isLoading.value = true;
      error.value = null;

      const result = await window.electronAPI.bumpVersion(options);
      
      if (result.success) {
        // 刷新版本信息
        await loadCurrentVersion();
        await loadVersionHistory();
        return result.data;
      } else {
        throw new Error(result.error || '版本升级失败');
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '版本升级失败';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const rollbackVersion = async (targetVersion: string, force: boolean = false) => {
    if (!currentProject.value) {
      throw new Error('未选择项目');
    }

    try {
      isLoading.value = true;
      error.value = null;

      const result = await window.electronAPI.rollbackVersion(targetVersion, force);
      
      if (result.success) {
        // 刷新版本信息
        await loadCurrentVersion();
        await loadVersionHistory();
        return result.data;
      } else {
        throw new Error(result.error || '版本回滚失败');
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '版本回滚失败';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const startBuild = async (platform: string, options: any = {}) => {
    if (!currentProject.value) {
      throw new Error('未选择项目');
    }

    try {
      const result = await window.electronAPI.startBuild(platform, options);
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error || '开始构建失败');
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '开始构建失败';
      throw err;
    }
  };

  const refreshAll = async () => {
    if (!currentProject.value) return;
    
    try {
      isLoading.value = true;
      await Promise.all([
        loadProjectInfo(),
        loadCurrentVersion(),
        loadVersionHistory()
      ]);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '刷新数据失败';
    } finally {
      isLoading.value = false;
    }
  };

  const refreshCurrentVersion = async () => {
    if (currentProject.value) {
      await loadCurrentVersion();
    }
  };

  const clearError = () => {
    error.value = null;
  };

  const reset = () => {
    currentProject.value = null;
    projectInfo.value = null;
    currentVersion.value = null;
    versionHistory.value = [];
    error.value = null;
    isLoading.value = false;
  };

  return {
    // 状态
    currentProject,
    projectInfo,
    currentVersion,
    versionHistory,
    isLoading,
    error,
    
    // 计算属性
    hasProject,
    projectName,
    
    // 操作方法
    setCurrentProject,
    loadProjectInfo,
    loadCurrentVersion,
    loadVersionHistory,
    bumpVersion,
    rollbackVersion,
    startBuild,
    refreshAll,
    refreshCurrentVersion,
    clearError,
    reset
  };
});
