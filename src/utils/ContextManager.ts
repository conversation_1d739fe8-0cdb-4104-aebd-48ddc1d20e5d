import * as path from 'path';
import chalk from 'chalk';
import { ProjectDetector } from './ProjectDetector';
import { Logger } from './Logger';

/**
 * 上下文管理器 - 管理工具运行时的全局上下文
 * 解决项目路径传递和状态管理问题
 */
export class ContextManager {
  private static instance: ContextManager;
  private projectRoot: string | null = null;
  private logger: Logger;
  private initialized = false;

  private constructor() {
    this.logger = Logger.getInstance();
  }

  static getInstance(): ContextManager {
    if (!ContextManager.instance) {
      ContextManager.instance = new ContextManager();
    }
    return ContextManager.instance;
  }

  /**
   * 初始化上下文
   * @param options 初始化选项
   */
  async initialize(options: {
    projectPath?: string;
    verbose?: boolean;
    skipValidation?: boolean;
  } = {}): Promise<void> {
    const { projectPath, verbose = false, skipValidation = false } = options;

    try {
      // 设置日志级别
      if (verbose) {
        this.logger.setLogLevel(0); // DEBUG
      }

      // 检查是否在工具目录内运行
      if (ProjectDetector.isRunningInsideProject()) {
        console.log(chalk.yellow('⚠️  检测到在工具目录内运行，这可能不是预期行为'));
        console.log(chalk.gray('   建议在目标项目目录中执行命令'));
      }

      // 解析项目路径
      this.projectRoot = ProjectDetector.resolveProjectPath(projectPath);
      
      if (!skipValidation) {
        // 验证项目
        const validation = ProjectDetector.validateProjectRoot(this.projectRoot);
        if (!validation.valid) {
          throw new Error(`项目验证失败: ${validation.issues.join(', ')}`);
        }

        // 显示警告
        if (validation.warnings.length > 0) {
          validation.warnings.forEach(warning => {
            this.logger.warn(warning);
          });
        }
      }

      // 显示项目信息
      if (verbose) {
        ProjectDetector.displayProjectSummary(this.projectRoot);
      }

      this.initialized = true;
      this.logger.info(`上下文初始化完成: ${this.projectRoot}`);

    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error('上下文初始化失败', message);
      throw error;
    }
  }

  /**
   * 获取项目根目录
   * @returns 项目根目录路径
   */
  getProjectRoot(): string {
    if (!this.initialized || !this.projectRoot) {
      throw new Error('上下文未初始化，请先调用 initialize()');
    }
    return this.projectRoot;
  }

  /**
   * 检查是否已初始化
   * @returns 是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 重置上下文
   */
  reset(): void {
    this.projectRoot = null;
    this.initialized = false;
    this.logger.info('上下文已重置');
  }

  /**
   * 获取项目相对路径
   * @param targetPath 目标路径
   * @returns 相对于项目根目录的路径
   */
  getRelativePath(targetPath: string): string {
    const projectRoot = this.getProjectRoot();
    return path.relative(projectRoot, targetPath);
  }

  /**
   * 获取项目内的绝对路径
   * @param relativePath 相对路径
   * @returns 绝对路径
   */
  getAbsolutePath(relativePath: string): string {
    const projectRoot = this.getProjectRoot();
    return path.resolve(projectRoot, relativePath);
  }

  /**
   * 检查路径是否在项目内
   * @param targetPath 目标路径
   * @returns 是否在项目内
   */
  isPathInProject(targetPath: string): boolean {
    const projectRoot = this.getProjectRoot();
    const relativePath = path.relative(projectRoot, path.resolve(targetPath));
    return !relativePath.startsWith('..') && !path.isAbsolute(relativePath);
  }

  /**
   * 获取项目信息
   * @returns 项目信息
   */
  getProjectInfo(): {
    name: string;
    version: string;
    type: string;
    description?: string;
    root: string;
  } {
    const projectRoot = this.getProjectRoot();
    const info = ProjectDetector.getProjectInfo(projectRoot);
    
    return {
      ...info,
      root: projectRoot
    };
  }

  /**
   * 创建带项目上下文的错误
   * @param message 错误消息
   * @param cause 原因
   * @returns 错误对象
   */
  createError(message: string, cause?: Error): Error {
    const projectInfo = this.isInitialized() ? ` (项目: ${this.getProjectInfo().name})` : '';
    const fullMessage = `${message}${projectInfo}`;
    
    if (cause) {
      const error = new Error(fullMessage);
      error.cause = cause;
      return error;
    }
    
    return new Error(fullMessage);
  }

  /**
   * 安全执行操作（带上下文检查）
   * @param operation 要执行的操作
   * @param operationName 操作名称
   * @returns 操作结果
   */
  async safeExecute<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    if (!this.isInitialized()) {
      throw new Error(`无法执行 ${operationName}：上下文未初始化`);
    }

    try {
      this.logger.debug(`开始执行: ${operationName}`);
      const result = await operation();
      this.logger.debug(`完成执行: ${operationName}`);
      return result;
    } catch (error) {
      const contextError = this.createError(
        `执行 ${operationName} 失败`,
        error instanceof Error ? error : new Error(String(error))
      );
      this.logger.error(contextError.message);
      throw contextError;
    }
  }
}

// 导出单例实例
export const contextManager = ContextManager.getInstance();
