import * as fs from 'fs-extra';
import * as path from 'path';
import chalk from 'chalk';

/**
 * 项目检测器 - 负责查找和验证目标项目
 * 解决工具独立后的项目路径定位问题
 */
export class ProjectDetector {
  private static readonly CONFIG_FILE = 'version-craft.config.json';
  private static readonly PACKAGE_FILE = 'package.json';
  
  /**
   * 从指定目录向上查找项目根目录
   * @param startDir 开始搜索的目录，默认为当前工作目录
   * @returns 项目根目录的绝对路径
   */
  static findProjectRoot(startDir?: string): string {
    let currentDir = path.resolve(startDir || process.cwd());
    const rootDir = path.parse(currentDir).root;
    
    while (currentDir !== rootDir) {
      const configPath = path.join(currentDir, this.CONFIG_FILE);
      
      if (fs.existsSync(configPath)) {
        console.log(chalk.gray(`✓ 找到项目根目录: ${currentDir}`));
        return currentDir;
      }
      
      currentDir = path.dirname(currentDir);
    }
    
    throw new Error(
      chalk.red(`❌ 未找到 ${this.CONFIG_FILE}\n`) +
      chalk.yellow(`请确保在包含配置文件的项目目录或其子目录中执行命令\n`) +
      chalk.gray(`搜索起始目录: ${startDir || process.cwd()}`)
    );
  }

  /**
   * 验证项目目录的有效性
   * @param projectRoot 项目根目录路径
   * @returns 验证结果和详细信息
   */
  static validateProjectRoot(projectRoot: string): {
    valid: boolean;
    issues: string[];
    warnings: string[];
  } {
    const issues: string[] = [];
    const warnings: string[] = [];
    
    // 检查目录是否存在
    if (!fs.existsSync(projectRoot)) {
      issues.push(`项目目录不存在: ${projectRoot}`);
      return { valid: false, issues, warnings };
    }
    
    // 检查是否为目录
    const stat = fs.statSync(projectRoot);
    if (!stat.isDirectory()) {
      issues.push(`指定路径不是目录: ${projectRoot}`);
      return { valid: false, issues, warnings };
    }
    
    // 检查配置文件
    const configPath = path.join(projectRoot, this.CONFIG_FILE);
    if (!fs.existsSync(configPath)) {
      issues.push(`缺少配置文件: ${this.CONFIG_FILE}`);
    }
    
    // 检查package.json（可选但推荐）
    const packagePath = path.join(projectRoot, this.PACKAGE_FILE);
    if (!fs.existsSync(packagePath)) {
      warnings.push(`建议添加 ${this.PACKAGE_FILE} 文件`);
    }
    
    // 检查Git仓库
    const gitPath = path.join(projectRoot, '.git');
    if (!fs.existsSync(gitPath)) {
      warnings.push('项目不是Git仓库，某些功能可能无法使用');
    }
    
    return {
      valid: issues.length === 0,
      issues,
      warnings
    };
  }

  /**
   * 获取项目基本信息
   * @param projectRoot 项目根目录
   * @returns 项目信息
   */
  static getProjectInfo(projectRoot: string): {
    name: string;
    version: string;
    type: string;
    description?: string;
  } {
    const configPath = path.join(projectRoot, this.CONFIG_FILE);
    
    if (!fs.existsSync(configPath)) {
      throw new Error(`配置文件不存在: ${configPath}`);
    }
    
    try {
      const config = fs.readJSONSync(configPath);
      return {
        name: config.project?.name || 'Unknown',
        version: config.project?.version || '0.0.0',
        type: config.project?.type || 'unknown',
        description: config.project?.description
      };
    } catch (error) {
      throw new Error(`读取配置文件失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 智能项目路径解析
   * 支持相对路径、绝对路径、以及自动检测
   * @param inputPath 用户输入的路径
   * @returns 解析后的绝对路径
   */
  static resolveProjectPath(inputPath?: string): string {
    if (!inputPath) {
      // 没有指定路径，从当前目录向上查找
      return this.findProjectRoot();
    }
    
    // 解析为绝对路径
    const absolutePath = path.resolve(inputPath);
    
    // 验证路径
    const validation = this.validateProjectRoot(absolutePath);
    if (!validation.valid) {
      throw new Error(
        chalk.red('❌ 项目路径验证失败:\n') +
        validation.issues.map(issue => chalk.red(`  • ${issue}`)).join('\n')
      );
    }
    
    // 显示警告
    if (validation.warnings.length > 0) {
      console.log(chalk.yellow('⚠️  警告:'));
      validation.warnings.forEach(warning => {
        console.log(chalk.yellow(`  • ${warning}`));
      });
    }
    
    return absolutePath;
  }

  /**
   * 显示项目信息摘要
   * @param projectRoot 项目根目录
   */
  static displayProjectSummary(projectRoot: string): void {
    try {
      const info = this.getProjectInfo(projectRoot);
      
      console.log(chalk.blue('\n📋 项目信息:'));
      console.log(chalk.green(`  名称: ${info.name}`));
      console.log(chalk.green(`  版本: ${info.version}`));
      console.log(chalk.green(`  类型: ${info.type}`));
      console.log(chalk.green(`  路径: ${projectRoot}`));
      
      if (info.description) {
        console.log(chalk.green(`  描述: ${info.description}`));
      }
      
      console.log('');
    } catch (error) {
      console.log(chalk.yellow(`⚠️  无法读取项目信息: ${error instanceof Error ? error.message : String(error)}`));
    }
  }

  /**
   * 检查工具是否在项目内部运行（用于调试）
   * @returns 是否在项目内部
   */
  static isRunningInsideProject(): boolean {
    const toolRoot = path.resolve(__dirname, '../..');
    const currentDir = process.cwd();
    
    // 检查当前目录是否包含工具代码
    const toolPackagePath = path.join(toolRoot, 'package.json');
    const currentPackagePath = path.join(currentDir, 'package.json');
    
    if (fs.existsSync(toolPackagePath) && fs.existsSync(currentPackagePath)) {
      try {
        const toolPackage = fs.readJSONSync(toolPackagePath);
        const currentPackage = fs.readJSONSync(currentPackagePath);
        
        // 如果名称相同，说明在工具目录内运行
        return toolPackage.name === currentPackage.name;
      } catch {
        return false;
      }
    }
    
    return false;
  }
}
