import * as fs from 'fs-extra';
import * as path from 'path';
import chalk from 'chalk';
import { simpleGit } from 'simple-git';
import { CocosCreatorDetector } from './CocosCreatorDetector';

/**
 * 项目检测器
 * 基于 process.cwd() 的检测逻辑
 */
export class ProjectDetector {
  
  /**
   * 验证当前工作目录是否适合执行 version-craft 命令
   */
  static async validateWorkingDirectory(): Promise<{
    valid: boolean;
    needsInit: boolean;
    error?: string;
    suggestions?: string[];
  }> {
    const cwd = process.cwd();
    
    try {
      // 1. 检查是否在工具目录内
      if (this.isVersionCraftToolDirectory(cwd)) {
        return {
          valid: false,
          needsInit: false,
          error: '不能在 version-craft 工具目录中执行命令',
          suggestions: [
            '请切换到要管理的 Cocos Creator 项目目录',
            '例如: cd /path/to/your/cocos-project'
          ]
        };
      }
      
      // 2. 检查是否为 Git 仓库根目录
      const gitValidation = await this.validateGitRoot(cwd);
      if (!gitValidation.valid) {
        return {
          valid: false,
          needsInit: false,
          error: gitValidation.error,
          suggestions: gitValidation.suggestions
        };
      }
      
      // 3. 检查是否为 Cocos Creator 项目
      const cocosValidation = this.validateCocosCreatorProject(cwd);
      if (!cocosValidation.valid) {
        return {
          valid: false,
          needsInit: false,
          error: cocosValidation.error,
          suggestions: cocosValidation.suggestions
        };
      }
      
      // 4. 检查是否被 version-craft 管理
      const managedValidation = this.validateVersionCraftManagement(cwd);
      if (!managedValidation.valid) {
        return {
          valid: false,
          needsInit: true,
          error: managedValidation.error,
          suggestions: managedValidation.suggestions
        };
      }
      
      return { valid: true, needsInit: false };
      
    } catch (error) {
      return {
        valid: false,
        needsInit: false,
        error: `验证过程中发生错误: ${error instanceof Error ? error.message : String(error)}`,
        suggestions: ['请检查目录权限和网络连接']
      };
    }
  }
  
  /**
   * 检查是否在 version-craft 工具目录内
   */
  private static isVersionCraftToolDirectory(cwd: string): boolean {
    try {
      const packagePath = path.join(cwd, 'package.json');
      if (!fs.existsSync(packagePath)) {
        return false;
      }
      
      const packageJson = fs.readJSONSync(packagePath);
      return packageJson.name === 'version-craft';
    } catch {
      return false;
    }
  }
  
  /**
   * 验证是否为 Git 仓库根目录
   */
  private static async validateGitRoot(cwd: string): Promise<{
    valid: boolean;
    error?: string;
    suggestions?: string[];
  }> {
    try {
      const git = simpleGit(cwd);
      
      // 检查是否为 Git 仓库
      const isRepo = await git.checkIsRepo();
      if (!isRepo) {
        return {
          valid: false,
          error: '当前目录不是 Git 仓库',
          suggestions: [
            '请在 Git 仓库根目录中执行命令',
            '或者先初始化 Git 仓库: git init'
          ]
        };
      }
      
      // 检查是否为 Git 根目录
      const gitRoot = await git.revparse(['--show-toplevel']);
      const normalizedCwd = path.resolve(cwd);
      const normalizedGitRoot = path.resolve(gitRoot);
      
      if (normalizedCwd !== normalizedGitRoot) {
        return {
          valid: false,
          error: '请在 Git 仓库根目录执行命令',
          suggestions: [
            `Git 根目录: ${normalizedGitRoot}`,
            `执行: cd "${normalizedGitRoot}" && version-craft <command>`
          ]
        };
      }
      
      return { valid: true };
      
    } catch (error) {
      return {
        valid: false,
        error: `Git 验证失败: ${error instanceof Error ? error.message : String(error)}`,
        suggestions: ['请确保当前目录是有效的 Git 仓库']
      };
    }
  }
  
  /**
   * 验证是否为 Cocos Creator 项目
   */
  private static validateCocosCreatorProject(cwd: string): {
    valid: boolean;
    error?: string;
    suggestions?: string[];
  } {
    try {
      const validation = CocosCreatorDetector.validateProject(cwd);

      if (!validation.valid) {
        return {
          valid: false,
          error: '当前目录不是 Cocos Creator 项目',
          suggestions: [
            'version-craft 专为 Cocos Creator 项目设计',
            '请确保项目包含以下任一特征:',
            '• package.json 中有 "creator" 字段和版本信息',
            '• package.json 的 keywords 包含 "cocos-creator"',
            '• 存在 project.json 文件且包含 uuid 字段',
            '• 存在 assets/ 目录及典型的 Cocos Creator 结构'
          ]
        };
      }

      return { valid: true };

    } catch (error) {
      return {
        valid: false,
        error: '项目类型检测失败',
        suggestions: [
          `检测错误: ${error instanceof Error ? error.message : String(error)}`,
          '请确保在有效的 Cocos Creator 项目目录中执行命令'
        ]
      };
    }
  }
  
  /**
   * 验证是否被 version-craft 管理
   */
  private static validateVersionCraftManagement(cwd: string): {
    valid: boolean;
    error?: string;
    suggestions?: string[];
  } {
    const configPath = path.join(cwd, 'version-craft.config.json');
    
    if (!fs.existsSync(configPath)) {
      return {
        valid: false,
        error: '项目未被 version-craft 管理',
        suggestions: [
          '需要先初始化 version-craft 配置',
          '将自动创建 version-craft.config.json 配置文件'
        ]
      };
    }
    
    // 验证配置文件格式
    try {
      const config = fs.readJSONSync(configPath);
      
      if (!config.project) {
        return {
          valid: false,
          error: '配置文件格式错误: 缺少 project 配置节',
          suggestions: ['请重新初始化配置文件']
        };
      }
      
      if (config.project.type !== 'cocos-creator') {
        return {
          valid: false,
          error: `不支持的项目类型: ${config.project.type}`,
          suggestions: ['version-craft 目前只支持 cocos-creator 项目']
        };
      }
      
      return { valid: true };
      
    } catch (error) {
      return {
        valid: false,
        error: '配置文件格式错误',
        suggestions: [
          `解析错误: ${error instanceof Error ? error.message : String(error)}`,
          '请重新初始化配置文件'
        ]
      };
    }
  }
  
  /**
   * 显示验证错误信息
   */
  static displayValidationError(result: {
    valid: boolean;
    needsInit: boolean;
    error?: string;
    suggestions?: string[];
  }): void {
    if (result.valid) return;
    
    console.log('');
    console.log(chalk.red(`❌ ${result.error}`));
    
    if (result.suggestions && result.suggestions.length > 0) {
      console.log('');
      if (result.needsInit) {
        console.log(chalk.blue('💡 解决方案:'));
      } else {
        console.log(chalk.yellow('💡 建议:'));
      }
      
      result.suggestions.forEach(suggestion => {
        console.log(chalk.gray(`   ${suggestion}`));
      });
    }
    
    console.log('');
  }
  
  /**
   * 获取项目基本信息
   */
  static getProjectInfo(cwd: string = process.cwd()): {
    name: string;
    version: string;
    type: string;
    description?: string;
  } {
    const configPath = path.join(cwd, 'version-craft.config.json');
    
    if (!fs.existsSync(configPath)) {
      throw new Error('配置文件不存在');
    }
    
    try {
      const config = fs.readJSONSync(configPath);
      return {
        name: config.project?.name || 'Unknown',
        version: config.project?.version || '0.0.0',
        type: config.project?.type || 'unknown',
        description: config.project?.description
      };
    } catch (error) {
      throw new Error(`读取配置文件失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
