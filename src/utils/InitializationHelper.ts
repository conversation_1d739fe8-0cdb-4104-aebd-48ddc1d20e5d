import * as fs from 'fs-extra';
import * as path from 'path';
import chalk from 'chalk';
import inquirer from 'inquirer';
import { GameVersionConfig } from '../types/config';

/**
 * 初始化助手
 * 提供友好的项目初始化体验
 */
export class InitializationHelper {
  
  /**
   * 交互式初始化 version-craft 配置
   */
  static async interactiveInit(cwd: string = process.cwd()): Promise<boolean> {
    console.log(chalk.blue('\n🚀 初始化 version-craft 项目管理\n'));
    
    try {
      // 1. 确认初始化
      const { shouldInit } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'shouldInit',
          message: '是否初始化 version-craft 管理？',
          default: true
        }
      ]);
      
      if (!shouldInit) {
        console.log(chalk.yellow('已取消初始化'));
        return false;
      }
      
      // 2. 收集项目信息
      const projectInfo = await this.collectProjectInfo(cwd);
      
      // 3. 收集构建配置
      const buildConfig = await this.collectBuildConfig();
      
      // 4. 生成配置文件
      const config = this.generateConfig(projectInfo, buildConfig);
      
      // 5. 保存配置文件
      const configPath = path.join(cwd, 'version-craft.config.json');
      await fs.writeJSON(configPath, config, { spaces: 2 });
      
      // 6. 显示成功信息
      this.displaySuccessMessage(projectInfo.name, configPath);
      
      return true;
      
    } catch (error) {
      console.log(chalk.red(`\n❌ 初始化失败: ${error instanceof Error ? error.message : String(error)}`));
      return false;
    }
  }
  
  /**
   * 收集项目信息
   */
  private static async collectProjectInfo(cwd: string): Promise<{
    name: string;
    version: string;
    description: string;
  }> {
    // 尝试从现有文件中获取默认值
    const defaults = this.getProjectDefaults(cwd);
    
    console.log(chalk.blue('📋 项目信息配置:'));
    
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'name',
        message: '项目名称:',
        default: defaults.name,
        validate: (input: string) => {
          if (!input.trim()) {
            return '项目名称不能为空';
          }
          if (!/^[a-zA-Z0-9\-_]+$/.test(input)) {
            return '项目名称只能包含字母、数字、连字符和下划线';
          }
          return true;
        }
      },
      {
        type: 'input',
        name: 'version',
        message: '初始版本:',
        default: defaults.version,
        validate: (input: string) => {
          if (!/^\d+\.\d+\.\d+$/.test(input)) {
            return '版本号格式应为 x.y.z (如: 1.0.0)';
          }
          return true;
        }
      },
      {
        type: 'input',
        name: 'description',
        message: '项目描述:',
        default: defaults.description
      }
    ]);
    
    return answers;
  }
  
  /**
   * 收集构建配置
   */
  private static async collectBuildConfig(): Promise<{
    platforms: string[];
    outputDir: string;
  }> {
    console.log(chalk.blue('\n🔧 构建配置:'));
    
    const answers = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'platforms',
        message: '选择构建平台:',
        choices: [
          { name: 'Web Mobile', value: 'web-mobile', checked: true },
          { name: 'Android', value: 'android' },
          { name: 'iOS', value: 'ios' },
          { name: 'Windows', value: 'windows' },
          { name: 'Mac', value: 'mac' }
        ],
        validate: (input: string[]) => {
          if (input.length === 0) {
            return '至少选择一个构建平台';
          }
          return true;
        }
      },
      {
        type: 'input',
        name: 'outputDir',
        message: '构建输出目录:',
        default: './dist',
        validate: (input: string) => {
          if (!input.trim()) {
            return '输出目录不能为空';
          }
          return true;
        }
      }
    ]);
    
    return answers;
  }
  
  /**
   * 从现有文件获取默认值
   */
  private static getProjectDefaults(cwd: string): {
    name: string;
    version: string;
    description: string;
  } {
    const defaults = {
      name: path.basename(cwd),
      version: '0.1.0',
      description: 'Cocos Creator 游戏项目'
    };
    
    // 尝试从 package.json 获取信息
    try {
      const packagePath = path.join(cwd, 'package.json');
      if (fs.existsSync(packagePath)) {
        const packageJson = fs.readJSONSync(packagePath);
        if (packageJson.name) defaults.name = packageJson.name;
        if (packageJson.version) defaults.version = packageJson.version;
        if (packageJson.description) defaults.description = packageJson.description;
      }
    } catch {
      // 忽略错误，使用默认值
    }
    
    // 尝试从 project.json 获取信息
    try {
      const projectPath = path.join(cwd, 'project.json');
      if (fs.existsSync(projectPath)) {
        const projectJson = fs.readJSONSync(projectPath);
        if (projectJson.name) defaults.name = projectJson.name;
        if (projectJson.version) defaults.version = projectJson.version;
      }
    } catch {
      // 忽略错误，使用默认值
    }
    
    return defaults;
  }
  
  /**
   * 生成配置文件
   */
  private static generateConfig(
    projectInfo: { name: string; version: string; description: string },
    buildConfig: { platforms: string[]; outputDir: string }
  ): GameVersionConfig {
    return {
      project: {
        name: projectInfo.name,
        type: 'cocos-creator',
        version: projectInfo.version,
        description: projectInfo.description
      },
      build: {
        platforms: buildConfig.platforms,
        outputDir: buildConfig.outputDir,
        cocosCreator: {
          projectPath: '.',
          builderPath: undefined
        },
        optimization: {
          compress: true,
          minify: true,
          sourcemap: false
        },
        excludeFiles: [
          '*.log',
          'node_modules/**',
          '.git/**',
          'temp/**',
          'library/**',
          'local/**',
          'build/**'
        ]
      },
      deploy: {
        web: {
          staging: 'https://staging.example.com',
          production: 'https://production.example.com'
        }
      },
      environments: {
        dev: './config/dev',
        test: './config/test',
        prod: './config/prod'
      },
      git: {
        autoTag: true,
        tagPrefix: 'v',
        generateChangelog: true,
        changelogPath: './CHANGELOG.md'
      },
      notification: {
        enabled: false
      }
    };
  }
  
  /**
   * 显示成功信息
   */
  private static displaySuccessMessage(projectName: string, configPath: string): void {
    console.log(chalk.green('\n✅ 初始化完成！\n'));
    
    console.log(chalk.blue('📋 项目信息:'));
    console.log(chalk.gray(`   名称: ${projectName}`));
    console.log(chalk.gray(`   配置文件: ${configPath}`));
    
    console.log(chalk.blue('\n🚀 快速开始:'));
    console.log(chalk.green('   version-craft current          ') + chalk.gray('# 查看当前版本'));
    console.log(chalk.green('   version-craft bump patch       ') + chalk.gray('# 升级补丁版本'));
    console.log(chalk.green('   version-craft build web-mobile ') + chalk.gray('# 构建 Web 版本'));
    console.log(chalk.green('   version-craft --help-all       ') + chalk.gray('# 查看所有命令'));
    
    console.log(chalk.blue('\n📚 更多信息:'));
    console.log(chalk.gray('   使用 version-craft <command> --help 查看具体命令帮助'));
    console.log('');
  }
  
  /**
   * 静默初始化（用于自动化场景）
   */
  static async silentInit(
    cwd: string = process.cwd(),
    options: {
      name?: string;
      version?: string;
      description?: string;
      platforms?: string[];
      outputDir?: string;
    } = {}
  ): Promise<boolean> {
    try {
      const defaults = this.getProjectDefaults(cwd);
      
      const projectInfo = {
        name: options.name || defaults.name,
        version: options.version || defaults.version,
        description: options.description || defaults.description
      };
      
      const buildConfig = {
        platforms: options.platforms || ['web-mobile'],
        outputDir: options.outputDir || './dist'
      };
      
      const config = this.generateConfig(projectInfo, buildConfig);
      const configPath = path.join(cwd, 'version-craft.config.json');
      
      await fs.writeJSON(configPath, config, { spaces: 2 });
      
      return true;
    } catch (error) {
      console.error(chalk.red(`静默初始化失败: ${error instanceof Error ? error.message : String(error)}`));
      return false;
    }
  }
  
  /**
   * 检查是否需要初始化
   */
  static needsInitialization(cwd: string = process.cwd()): boolean {
    const configPath = path.join(cwd, 'version-craft.config.json');
    return !fs.existsSync(configPath);
  }
}
