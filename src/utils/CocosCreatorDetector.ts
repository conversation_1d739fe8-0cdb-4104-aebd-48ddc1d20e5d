import * as fs from 'fs-extra';
import * as path from 'path';
import chalk from 'chalk';

/**
 * Cocos Creator 项目检测器
 * 专门用于检测和验证 Cocos Creator 项目
 */
export class CocosCreatorDetector {
  
  /**
   * 检测是否为 Cocos Creator 项目
   */
  static isCocosCreatorProject(projectPath: string = process.cwd()): boolean {
    const indicators = this.getProjectIndicators();
    
    for (const indicator of indicators) {
      const fullPath = path.join(projectPath, indicator.path);
      
      if (!fs.existsSync(fullPath)) {
        return false;
      }
      
      if (indicator.isDirectory) {
        const stat = fs.statSync(fullPath);
        if (!stat.isDirectory()) {
          return false;
        }
      }
    }
    
    return true;
  }
  
  /**
   * 获取详细的项目验证结果
   */
  static validateProject(projectPath: string = process.cwd()): {
    valid: boolean;
    version?: string;
    missingFiles: string[];
    warnings: string[];
    projectInfo?: any;
  } {
    const indicators = this.getProjectIndicators();
    const missingFiles: string[] = [];
    const warnings: string[] = [];
    let projectInfo: any = null;
    let version: string | undefined;
    
    // 检查必需文件
    for (const indicator of indicators) {
      const fullPath = path.join(projectPath, indicator.path);
      
      if (!fs.existsSync(fullPath)) {
        missingFiles.push(indicator.path);
        continue;
      }
      
      if (indicator.isDirectory) {
        const stat = fs.statSync(fullPath);
        if (!stat.isDirectory()) {
          missingFiles.push(`${indicator.path} (应该是目录)`);
        }
      }
    }
    
    // 读取项目信息
    try {
      const projectJsonPath = path.join(projectPath, 'project.json');
      if (fs.existsSync(projectJsonPath)) {
        projectInfo = fs.readJSONSync(projectJsonPath);
        version = projectInfo.version;
      }
    } catch (error) {
      warnings.push(`无法读取 project.json: ${error instanceof Error ? error.message : String(error)}`);
    }
    
    // 检查可选但推荐的文件
    const optionalFiles = this.getOptionalFiles();
    for (const file of optionalFiles) {
      const fullPath = path.join(projectPath, file.path);
      if (!fs.existsSync(fullPath)) {
        warnings.push(`建议添加 ${file.path}: ${file.description}`);
      }
    }
    
    return {
      valid: missingFiles.length === 0,
      version,
      missingFiles,
      warnings,
      projectInfo
    };
  }
  
  /**
   * 获取 Cocos Creator 版本信息
   */
  static getCocosCreatorVersion(projectPath: string = process.cwd()): string | null {
    try {
      const projectJsonPath = path.join(projectPath, 'project.json');
      if (!fs.existsSync(projectJsonPath)) {
        return null;
      }
      
      const projectJson = fs.readJSONSync(projectJsonPath);
      return projectJson.version || null;
    } catch {
      return null;
    }
  }
  
  /**
   * 检查 Cocos Creator 构建环境
   */
  static validateBuildEnvironment(projectPath: string = process.cwd()): {
    valid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    // 检查构建相关目录
    const buildDirs = ['build', 'temp', 'library'];
    for (const dir of buildDirs) {
      const dirPath = path.join(projectPath, dir);
      if (fs.existsSync(dirPath)) {
        const stat = fs.statSync(dirPath);
        if (!stat.isDirectory()) {
          issues.push(`${dir} 应该是目录而不是文件`);
        }
      }
    }
    
    // 检查资源目录结构
    const assetsPath = path.join(projectPath, 'assets');
    if (fs.existsSync(assetsPath)) {
      const assetsDirs = ['scripts', 'resources', 'scenes'];
      for (const dir of assetsDirs) {
        const dirPath = path.join(assetsPath, dir);
        if (!fs.existsSync(dirPath)) {
          recommendations.push(`建议创建 assets/${dir} 目录`);
        }
      }
    }
    
    // 检查 TypeScript 配置
    const tsConfigPath = path.join(projectPath, 'tsconfig.json');
    if (!fs.existsSync(tsConfigPath)) {
      recommendations.push('建议添加 tsconfig.json 配置文件');
    }
    
    return {
      valid: issues.length === 0,
      issues,
      recommendations
    };
  }
  
  /**
   * 获取项目统计信息
   */
  static getProjectStats(projectPath: string = process.cwd()): {
    assetsCount: number;
    scriptsCount: number;
    scenesCount: number;
    totalSize: number;
  } {
    const stats = {
      assetsCount: 0,
      scriptsCount: 0,
      scenesCount: 0,
      totalSize: 0
    };
    
    try {
      const assetsPath = path.join(projectPath, 'assets');
      if (fs.existsSync(assetsPath)) {
        this.countAssetsRecursive(assetsPath, stats);
      }
    } catch (error) {
      console.warn(`获取项目统计信息失败: ${error instanceof Error ? error.message : String(error)}`);
    }
    
    return stats;
  }
  
  /**
   * 递归统计资源文件
   */
  private static countAssetsRecursive(dirPath: string, stats: any): void {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        this.countAssetsRecursive(itemPath, stats);
      } else {
        stats.assetsCount++;
        stats.totalSize += stat.size;
        
        const ext = path.extname(item).toLowerCase();
        if (['.ts', '.js'].includes(ext)) {
          stats.scriptsCount++;
        } else if (ext === '.scene') {
          stats.scenesCount++;
        }
      }
    }
  }
  
  /**
   * 获取项目必需文件指示器
   */
  private static getProjectIndicators(): Array<{
    path: string;
    description: string;
    isDirectory?: boolean;
  }> {
    return [
      {
        path: 'creator.d.ts',
        description: 'Cocos Creator 类型定义文件'
      },
      {
        path: 'assets',
        description: '资源目录',
        isDirectory: true
      },
      {
        path: 'project.json',
        description: 'Cocos Creator 项目配置文件'
      }
    ];
  }
  
  /**
   * 获取可选文件列表
   */
  private static getOptionalFiles(): Array<{
    path: string;
    description: string;
  }> {
    return [
      {
        path: 'tsconfig.json',
        description: 'TypeScript 配置文件'
      },
      {
        path: 'package.json',
        description: 'Node.js 包配置文件'
      },
      {
        path: '.gitignore',
        description: 'Git 忽略文件配置'
      },
      {
        path: 'README.md',
        description: '项目说明文档'
      }
    ];
  }
  
  /**
   * 显示项目验证结果
   */
  static displayValidationResult(result: {
    valid: boolean;
    version?: string;
    missingFiles: string[];
    warnings: string[];
    projectInfo?: any;
  }): void {
    if (result.valid) {
      console.log(chalk.green('✅ Cocos Creator 项目验证通过'));
      if (result.version) {
        console.log(chalk.gray(`   版本: ${result.version}`));
      }
    } else {
      console.log(chalk.red('❌ Cocos Creator 项目验证失败'));
      if (result.missingFiles.length > 0) {
        console.log(chalk.red('   缺少文件:'));
        result.missingFiles.forEach(file => {
          console.log(chalk.red(`     • ${file}`));
        });
      }
    }
    
    if (result.warnings.length > 0) {
      console.log(chalk.yellow('⚠️  警告:'));
      result.warnings.forEach(warning => {
        console.log(chalk.yellow(`   • ${warning}`));
      });
    }
  }
  
  /**
   * 显示项目统计信息
   */
  static displayProjectStats(stats: {
    assetsCount: number;
    scriptsCount: number;
    scenesCount: number;
    totalSize: number;
  }): void {
    console.log(chalk.blue('📊 项目统计:'));
    console.log(chalk.gray(`   资源文件: ${stats.assetsCount} 个`));
    console.log(chalk.gray(`   脚本文件: ${stats.scriptsCount} 个`));
    console.log(chalk.gray(`   场景文件: ${stats.scenesCount} 个`));
    console.log(chalk.gray(`   总大小: ${(stats.totalSize / 1024 / 1024).toFixed(2)} MB`));
  }
}
