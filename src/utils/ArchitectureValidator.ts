import * as fs from 'fs-extra';
import * as path from 'path';
import chalk from 'chalk';
import { simpleGit } from 'simple-git';
import { ProjectDetector } from './ProjectDetector';

/**
 * 架构验证器 - 验证工具架构的正确性
 * 确保工具独立运行，不会出现"自毁"问题
 */
export class ArchitectureValidator {
  
  /**
   * 验证工具架构的完整性
   * @param projectPath 目标项目路径
   * @returns 验证结果
   */
  static async validateArchitecture(projectPath?: string): Promise<{
    valid: boolean;
    issues: string[];
    warnings: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];

    try {
      // 1. 验证工具与项目的分离性
      const separationResult = this.validateToolProjectSeparation(projectPath);
      if (!separationResult.valid) {
        issues.push(...separationResult.issues);
      }
      warnings.push(...separationResult.warnings);

      // 2. 验证Git操作目标
      const gitResult = await this.validateGitOperationTargets(projectPath);
      if (!gitResult.valid) {
        issues.push(...gitResult.issues);
      }
      warnings.push(...gitResult.warnings);

      // 3. 验证路径解析逻辑
      const pathResult = this.validatePathResolution(projectPath);
      if (!pathResult.valid) {
        issues.push(...pathResult.issues);
      }
      warnings.push(...pathResult.warnings);

      // 4. 验证配置文件访问
      const configResult = this.validateConfigAccess(projectPath);
      if (!configResult.valid) {
        issues.push(...configResult.issues);
      }
      warnings.push(...configResult.warnings);

      // 5. 生成建议
      recommendations.push(...this.generateRecommendations(issues, warnings));

      return {
        valid: issues.length === 0,
        issues,
        warnings,
        recommendations
      };

    } catch (error) {
      issues.push(`架构验证过程中发生错误: ${error instanceof Error ? error.message : String(error)}`);
      return {
        valid: false,
        issues,
        warnings,
        recommendations
      };
    }
  }

  /**
   * 验证工具与项目的分离性
   */
  private static validateToolProjectSeparation(projectPath?: string): {
    valid: boolean;
    issues: string[];
    warnings: string[];
  } {
    const issues: string[] = [];
    const warnings: string[] = [];

    try {
      // 获取工具根目录
      const toolRoot = path.resolve(__dirname, '../..');
      
      // 获取目标项目路径
      const targetProject = projectPath ? path.resolve(projectPath) : ProjectDetector.findProjectRoot();

      // 检查是否在同一目录
      if (toolRoot === targetProject) {
        issues.push('❌ 致命错误: 工具代码与目标项目在同一目录');
        issues.push('   这会导致Git回滚时工具代码被回滚，造成"自毁"问题');
      }

      // 检查是否存在包含关系
      const toolRelativeToProject = path.relative(targetProject, toolRoot);
      const projectRelativeToTool = path.relative(toolRoot, targetProject);

      if (!toolRelativeToProject.startsWith('..') && !path.isAbsolute(toolRelativeToProject)) {
        issues.push('❌ 工具代码位于项目目录内部，存在"自毁"风险');
      }

      if (!projectRelativeToTool.startsWith('..') && !path.isAbsolute(projectRelativeToTool)) {
        warnings.push('⚠️  项目位于工具目录内部，建议分离');
      }

      // 检查工具是否有自己的package.json
      const toolPackagePath = path.join(toolRoot, 'package.json');
      if (!fs.existsSync(toolPackagePath)) {
        warnings.push('工具目录缺少package.json文件');
      }

      return { valid: issues.length === 0, issues, warnings };

    } catch (error) {
      issues.push(`分离性验证失败: ${error instanceof Error ? error.message : String(error)}`);
      return { valid: false, issues, warnings };
    }
  }

  /**
   * 验证Git操作目标
   */
  private static async validateGitOperationTargets(projectPath?: string): Promise<{
    valid: boolean;
    issues: string[];
    warnings: string[];
  }> {
    const issues: string[] = [];
    const warnings: string[] = [];

    try {
      const targetProject = projectPath ? path.resolve(projectPath) : ProjectDetector.findProjectRoot();
      const git = simpleGit(targetProject);

      // 检查是否为Git仓库
      const isRepo = await git.checkIsRepo();
      if (!isRepo) {
        warnings.push('目标项目不是Git仓库，版本管理功能将受限');
        return { valid: true, issues, warnings };
      }

      // 检查Git工作目录
      const gitRoot = await git.revparse(['--show-toplevel']);
      const expectedGitRoot = path.resolve(targetProject);

      if (path.resolve(gitRoot) !== expectedGitRoot) {
        warnings.push(`Git根目录 (${gitRoot}) 与项目根目录 (${expectedGitRoot}) 不一致`);
      }

      // 检查是否有未提交的更改
      const status = await git.status();
      if (status.files.length > 0) {
        warnings.push('项目有未提交的更改，建议先提交或暂存');
      }

      return { valid: issues.length === 0, issues, warnings };

    } catch (error) {
      issues.push(`Git验证失败: ${error instanceof Error ? error.message : String(error)}`);
      return { valid: false, issues, warnings };
    }
  }

  /**
   * 验证路径解析逻辑
   */
  private static validatePathResolution(projectPath?: string): {
    valid: boolean;
    issues: string[];
    warnings: string[];
  } {
    const issues: string[] = [];
    const warnings: string[] = [];

    try {
      // 测试项目检测器
      const detectedPath = ProjectDetector.findProjectRoot(projectPath);
      
      if (projectPath) {
        const expectedPath = path.resolve(projectPath);
        if (detectedPath !== expectedPath) {
          issues.push(`路径解析不一致: 期望 ${expectedPath}, 实际 ${detectedPath}`);
        }
      }

      // 验证项目有效性
      const validation = ProjectDetector.validateProjectRoot(detectedPath);
      if (!validation.valid) {
        issues.push(...validation.issues.map(issue => `项目验证失败: ${issue}`));
      }
      warnings.push(...validation.warnings);

      return { valid: issues.length === 0, issues, warnings };

    } catch (error) {
      issues.push(`路径解析验证失败: ${error instanceof Error ? error.message : String(error)}`);
      return { valid: false, issues, warnings };
    }
  }

  /**
   * 验证配置文件访问
   */
  private static validateConfigAccess(projectPath?: string): {
    valid: boolean;
    issues: string[];
    warnings: string[];
  } {
    const issues: string[] = [];
    const warnings: string[] = [];

    try {
      const targetProject = projectPath ? path.resolve(projectPath) : ProjectDetector.findProjectRoot();
      const configPath = path.join(targetProject, 'version-craft.config.json');

      // 检查配置文件是否存在
      if (!fs.existsSync(configPath)) {
        issues.push('配置文件不存在: version-craft.config.json');
        return { valid: false, issues, warnings };
      }

      // 检查配置文件是否可读
      try {
        const config = fs.readJSONSync(configPath);
        if (!config.project) {
          warnings.push('配置文件缺少project配置节');
        }
        if (!config.build) {
          warnings.push('配置文件缺少build配置节');
        }
      } catch (error) {
        issues.push(`配置文件格式错误: ${error instanceof Error ? error.message : String(error)}`);
      }

      return { valid: issues.length === 0, issues, warnings };

    } catch (error) {
      issues.push(`配置访问验证失败: ${error instanceof Error ? error.message : String(error)}`);
      return { valid: false, issues, warnings };
    }
  }

  /**
   * 生成改进建议
   */
  private static generateRecommendations(issues: string[], warnings: string[]): string[] {
    const recommendations: string[] = [];

    if (issues.some(issue => issue.includes('自毁'))) {
      recommendations.push('🔧 立即将工具移出项目目录，作为独立的全局工具安装');
      recommendations.push('📦 建议使用 npm install -g 或 yarn global add 安装工具');
    }

    if (warnings.some(warning => warning.includes('Git'))) {
      recommendations.push('📝 建议在执行版本管理操作前提交所有更改');
    }

    if (issues.some(issue => issue.includes('配置文件'))) {
      recommendations.push('⚙️  运行 version-craft config init 初始化配置文件');
    }

    if (recommendations.length === 0) {
      recommendations.push('✅ 架构验证通过，工具配置正确');
    }

    return recommendations;
  }

  /**
   * 显示验证结果
   */
  static displayValidationResult(result: {
    valid: boolean;
    issues: string[];
    warnings: string[];
    recommendations: string[];
  }): void {
    console.log(chalk.blue('\n🔍 架构验证结果:\n'));

    if (result.valid) {
      console.log(chalk.green('✅ 架构验证通过'));
    } else {
      console.log(chalk.red('❌ 架构验证失败'));
    }

    if (result.issues.length > 0) {
      console.log(chalk.red('\n🚨 发现的问题:'));
      result.issues.forEach(issue => {
        console.log(chalk.red(`  • ${issue}`));
      });
    }

    if (result.warnings.length > 0) {
      console.log(chalk.yellow('\n⚠️  警告:'));
      result.warnings.forEach(warning => {
        console.log(chalk.yellow(`  • ${warning}`));
      });
    }

    if (result.recommendations.length > 0) {
      console.log(chalk.blue('\n💡 建议:'));
      result.recommendations.forEach(rec => {
        console.log(chalk.blue(`  • ${rec}`));
      });
    }

    console.log('');
  }
}
