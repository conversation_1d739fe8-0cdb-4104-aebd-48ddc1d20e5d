#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { VersionCommand } from './commands/VersionCommand';
import { BuildCommand } from './commands/BuildCommand';
import { DeployCommand } from './commands/DeployCommand';
import { ConfigCommand } from './commands/ConfigCommand';
import { RollbackCommand } from './commands/RollbackCommand';
import { HotUpdateCommand } from './commands/HotUpdateCommand';
import { ProjectDetector } from './utils/ProjectDetector';
import { InitializationHelper } from './utils/InitializationHelper';

const program = new Command();

// 设置程序信息
program
  .name('version-craft')
  .description('LuckyCoin 游戏版本管理工具')
  .version('1.0.0')
  .hook('preAction', async (thisCommand) => {
    try {
      // 验证当前工作目录
      const validation = await ProjectDetector.validateWorkingDirectory();

      if (!validation.valid) {
        ProjectDetector.displayValidationError(validation);

        // 如果需要初始化，提供初始化选项
        if (validation.needsInit) {
          const success = await InitializationHelper.interactiveInit();
          if (!success) {
            process.exit(1);
          }
          // 初始化成功后，重新验证
          const revalidation = await ProjectDetector.validateWorkingDirectory();
          if (!revalidation.valid) {
            ProjectDetector.displayValidationError(revalidation);
            process.exit(1);
          }
        } else {
          process.exit(1);
        }
      }

    } catch (error) {
      console.error(chalk.red('❌ 验证失败:'), error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// 创建命令实例
const versionCommand = new VersionCommand();
const buildCommand = new BuildCommand();
const deployCommand = new DeployCommand();
const configCommand = new ConfigCommand();
const rollbackCommand = new RollbackCommand();
const hotUpdateCommand = new HotUpdateCommand();

// 直接注册所有命令到根程序（扁平化结构）
versionCommand.registerCommands(program);
buildCommand.registerCommands(program);
deployCommand.registerCommands(program);
configCommand.registerCommands(program);
rollbackCommand.registerCommands(program);
hotUpdateCommand.registerCommands(program);

// 显示分组的完整帮助信息
function showGroupedHelp() {
  console.log(chalk.green('🎮 LuckyCoin 游戏版本管理工具 - 完整命令列表'));
  console.log('');

  // 项目初始化
  console.log(chalk.blue('🚀 项目初始化:'));
  console.log(chalk.green('  init                                 ') + chalk.gray('初始化项目配置文件'));
  console.log('');

  // 版本管理
  console.log(chalk.blue('🏷️  版本管理:'));
  console.log(chalk.green('  current, show                        ') + chalk.gray('显示当前版本'));
  console.log(chalk.green('  bump [options] [type]                ') + chalk.gray('升级版本号 (major|minor|patch|prerelease)'));
  console.log(chalk.green('    -p, --prerelease <type>            ') + chalk.gray('预发布类型 (alpha|beta|rc)'));
  console.log(chalk.green('    -t, --tag                          ') + chalk.gray('自动创建 Git 标签'));
  console.log(chalk.green('    -c, --changelog                    ') + chalk.gray('生成变更日志'));
  console.log(chalk.green('  tag [options] [version]              ') + chalk.gray('为指定版本创建 Git 标签'));
  console.log(chalk.green('    -m, --message <message>            ') + chalk.gray('标签信息'));
  console.log(chalk.green('  changelog [options]                  ') + chalk.gray('生成变更日志'));
  console.log(chalk.green('    -f, --from <version>               ') + chalk.gray('起始版本'));
  console.log(chalk.green('  list                                 ') + chalk.gray('列出所有可用版本'));
  console.log('');

  // 构建管理
  console.log(chalk.blue('🔨 构建管理:'));
  console.log(chalk.green('  build-all [options]                  ') + chalk.gray('构建所有平台'));
  console.log(chalk.green('    -c, --clean                        ') + chalk.gray('构建前清理输出目录'));
  console.log(chalk.green('  build [options] <platform>           ') + chalk.gray('构建指定平台 (web-mobile|android|ios|windows|mac)'));
  console.log(chalk.green('    -c, --clean                        ') + chalk.gray('构建前清理输出目录'));
  console.log(chalk.green('  build-web [options]                  ') + chalk.gray('构建 Web Mobile 版本'));
  console.log(chalk.green('    -c, --clean                        ') + chalk.gray('构建前清理输出目录'));
  console.log(chalk.green('  build-android [options]              ') + chalk.gray('构建 Android 版本'));
  console.log(chalk.green('    -c, --clean                        ') + chalk.gray('构建前清理输出目录'));
  console.log(chalk.green('    -s, --sign                         ') + chalk.gray('自动签名 APK'));
  console.log(chalk.green('  build-ios [options]                  ') + chalk.gray('构建 iOS 版本'));
  console.log(chalk.green('    -c, --clean                        ') + chalk.gray('构建前清理输出目录'));
  console.log(chalk.green('  build-clean                          ') + chalk.gray('清理构建输出'));
  console.log(chalk.green('  build-stats                          ') + chalk.gray('查看构建统计信息'));
  console.log('');

  // 热更新管理
  console.log(chalk.blue('🔥 热更新管理:'));
  console.log(chalk.green('  hotupdate-manifest [options]         ') + chalk.gray('生成热更新资源清单'));
  console.log(chalk.green('    -v, --version <version>            ') + chalk.gray('指定版本号'));
  console.log(chalk.green('    --base-url <url>                   ') + chalk.gray('资源基础URL'));
  console.log(chalk.green('    --mandatory                        ') + chalk.gray('标记为强制更新'));
  console.log(chalk.green('  hotupdate-patch <from> <to>          ') + chalk.gray('生成增量更新包'));
  console.log(chalk.green('  hotupdate-tag [options]              ') + chalk.gray('为资源文件添加版本标记'));
  console.log(chalk.green('    --format <format>                  ') + chalk.gray('标记格式 (suffix|query|header)'));
  console.log(chalk.green('  hotupdate-verify <manifest>          ') + chalk.gray('验证资源清单完整性'));
  console.log(chalk.green('  hotupdate-release [options]          ') + chalk.gray('完整的热更新发布流程'));
  console.log(chalk.green('  hotupdate-clean [options]            ') + chalk.gray('清理旧版本资源'));
  console.log('');

  // 部署管理
  console.log(chalk.blue('🚀 部署管理:'));
  console.log(chalk.green('  deploy-staging [options]             ') + chalk.gray('部署到测试环境'));
  console.log(chalk.green('    -p, --platform <platform>         ') + chalk.gray('指定平台'));
  console.log(chalk.green('  deploy-production, deploy-prod [options] ') + chalk.gray('部署到生产环境'));
  console.log(chalk.green('    -p, --platform <platform>         ') + chalk.gray('指定平台'));
  console.log(chalk.green('  deploy <environment> [platform]      ') + chalk.gray('部署到指定环境 (staging|production)'));
  console.log(chalk.green('  deploy-history [options]             ') + chalk.gray('查看部署历史'));
  console.log(chalk.green('    -p, --platform <platform>         ') + chalk.gray('过滤平台'));
  console.log(chalk.green('    -e, --environment <environment>   ') + chalk.gray('过滤环境'));
  console.log(chalk.green('  deploy-status                        ') + chalk.gray('检查部署状态'));
  console.log('');

  // 配置管理
  console.log(chalk.blue('📋 配置管理:'));
  console.log(chalk.green('  config-show [options]                ') + chalk.gray('显示当前配置'));
  console.log(chalk.green('    -e, --env <environment>           ') + chalk.gray('显示指定环境配置'));
  console.log(chalk.green('  config-set <key> <value>             ') + chalk.gray('设置配置项'));
  console.log(chalk.green('  config-env <environment>             ') + chalk.gray('切换环境配置'));
  console.log(chalk.green('  config-validate [options]            ') + chalk.gray('验证配置文件'));
  console.log(chalk.green('    -e, --env <environment>           ') + chalk.gray('验证指定环境配置'));
  console.log(chalk.green('  config-export [file]                 ') + chalk.gray('导出配置文件'));
  console.log(chalk.green('  config-import <file>                 ') + chalk.gray('导入配置文件'));
  console.log('');

  // 回滚管理
  console.log(chalk.blue('↩️  回滚管理:'));
  console.log(chalk.green('  rollback-list [options]              ') + chalk.gray('列出可回滚的版本'));
  console.log(chalk.green('    -l, --limit <number>               ') + chalk.gray('限制显示数量 (默认: 10)'));
  console.log(chalk.green('  rollback-to [options] <version>      ') + chalk.gray('回滚到指定版本'));
  console.log(chalk.green('    -p, --platform <platform>         ') + chalk.gray('指定平台'));
  console.log(chalk.green('    -e, --environment <environment>   ') + chalk.gray('指定环境 (默认: staging)'));
  console.log(chalk.green('    -f, --force                       ') + chalk.gray('强制回滚，跳过确认'));
  console.log(chalk.green('  rollback-last [options]              ') + chalk.gray('回滚到上一个版本'));
  console.log(chalk.green('    -p, --platform <platform>         ') + chalk.gray('指定平台'));
  console.log(chalk.green('    -e, --environment <environment>   ') + chalk.gray('指定环境 (默认: staging)'));
  console.log(chalk.green('  rollback-status                      ') + chalk.gray('检查回滚状态'));
  console.log(chalk.green('  rollback-checkpoint [name]           ') + chalk.gray('创建回滚点'));
  console.log('');

  // 分组帮助
  console.log(chalk.blue('📚 分组帮助:'));
  console.log(chalk.green('  config-help                          ') + chalk.gray('显示配置相关命令帮助'));
  console.log(chalk.green('  version-help                         ') + chalk.gray('显示版本相关命令帮助'));
  console.log(chalk.green('  build-help                           ') + chalk.gray('显示构建相关命令帮助'));
  console.log(chalk.green('  deploy-help                          ') + chalk.gray('显示部署相关命令帮助'));
  console.log(chalk.green('  rollback-help                        ') + chalk.gray('显示回滚相关命令帮助'));
  console.log('');

  // 全局选项
  console.log(chalk.blue('⚙️  全局选项:'));
  console.log(chalk.green('  -V, --version                        ') + chalk.gray('显示版本号'));
  console.log(chalk.green('  -h, --help                           ') + chalk.gray('显示快速帮助'));
  console.log(chalk.green('  --help-all                           ') + chalk.gray('显示此完整帮助'));
  console.log('');

  console.log(chalk.gray('使用 "version-craft <command> --help" 查看具体命令的详细帮助'));
  process.exit(0);
}

// 添加 --help-all 选项显示分组的完整命令列表
program
  .option('--help-all', '显示所有命令的完整列表')
  .action((options) => {
    if (options.helpAll) {
      showGroupedHelp();
    }
  });

// 添加分组帮助命令
program
  .command('config-help')
  .description('显示配置相关命令帮助')
  .helpOption(false) // 禁用默认帮助
  .option('-h, --help', '显示帮助信息')
  .action((options) => {
    console.log(chalk.blue('📋 配置管理命令:'));
    console.log('');
    console.log(chalk.green('  version-craft init                    ') + chalk.gray('# 初始化项目配置文件'));
    console.log(chalk.green('  version-craft config-show [options]   ') + chalk.gray('# 显示当前配置'));
    console.log(chalk.green('  version-craft config-set <key> <value>') + chalk.gray('# 设置配置项'));
    console.log(chalk.green('  version-craft config-env <environment>') + chalk.gray('# 切换环境配置'));
    console.log(chalk.green('  version-craft config-validate         ') + chalk.gray('# 验证配置文件'));
    console.log(chalk.green('  version-craft config-export [file]    ') + chalk.gray('# 导出配置文件'));
    console.log(chalk.green('  version-craft config-import <file>    ') + chalk.gray('# 导入配置文件'));
    console.log('');
    console.log(chalk.gray('使用 "version-craft <command> --help" 查看具体命令的详细帮助'));
  });

program
  .command('version-help')
  .description('显示版本相关命令帮助')
  .helpOption(false) // 禁用默认帮助
  .option('-h, --help', '显示帮助信息')
  .action((options) => {
    console.log(chalk.blue('🏷️  版本管理命令:'));
    console.log('');
    console.log(chalk.green('  version-craft current                 ') + chalk.gray('# 显示当前版本'));
    console.log(chalk.green('  version-craft bump [type]             ') + chalk.gray('# 升级版本号'));
    console.log(chalk.green('  version-craft tag [version]           ') + chalk.gray('# 创建 Git 标签'));
    console.log(chalk.green('  version-craft changelog               ') + chalk.gray('# 生成变更日志'));
    console.log(chalk.green('  version-craft list                    ') + chalk.gray('# 列出所有版本'));
    console.log('');
    console.log(chalk.gray('使用 "version-craft <command> --help" 查看具体命令的详细帮助'));
  });

program
  .command('build-help')
  .description('显示构建相关命令帮助')
  .helpOption(false) // 禁用默认帮助
  .option('-h, --help', '显示帮助信息')
  .action((options) => {
    console.log(chalk.blue('🔨 构建管理命令:'));
    console.log('');
    console.log(chalk.green('  version-craft build-all               ') + chalk.gray('# 构建所有平台'));
    console.log(chalk.green('  version-craft build <platform>        ') + chalk.gray('# 构建指定平台'));
    console.log(chalk.green('  version-craft build-web               ') + chalk.gray('# 构建 Web Mobile 版本'));
    console.log(chalk.green('  version-craft build-android           ') + chalk.gray('# 构建 Android 版本'));
    console.log(chalk.green('  version-craft build-ios               ') + chalk.gray('# 构建 iOS 版本'));
    console.log(chalk.green('  version-craft build-clean             ') + chalk.gray('# 清理构建输出'));
    console.log(chalk.green('  version-craft build-stats             ') + chalk.gray('# 查看构建统计'));
    console.log('');
    console.log(chalk.gray('使用 "version-craft <command> --help" 查看具体命令的详细帮助'));
  });

program
  .command('deploy-help')
  .description('显示部署相关命令帮助')
  .helpOption(false) // 禁用默认帮助
  .option('-h, --help', '显示帮助信息')
  .action((options) => {
    console.log(chalk.blue('🚀 部署管理命令:'));
    console.log('');
    console.log(chalk.green('  version-craft deploy-staging          ') + chalk.gray('# 部署到测试环境'));
    console.log(chalk.green('  version-craft deploy-production       ') + chalk.gray('# 部署到生产环境'));
    console.log(chalk.green('  version-craft deploy <env> [platform] ') + chalk.gray('# 部署到指定环境'));
    console.log(chalk.green('  version-craft deploy-history          ') + chalk.gray('# 查看部署历史'));
    console.log(chalk.green('  version-craft deploy-status           ') + chalk.gray('# 检查部署状态'));
    console.log('');
    console.log(chalk.gray('使用 "version-craft <command> --help" 查看具体命令的详细帮助'));
  });

program
  .command('rollback-help')
  .description('显示回滚相关命令帮助')
  .helpOption(false) // 禁用默认帮助
  .option('-h, --help', '显示帮助信息')
  .action((options) => {
    console.log(chalk.blue('↩️  回滚管理命令:'));
    console.log('');
    console.log(chalk.green('  version-craft rollback-list           ') + chalk.gray('# 列出可回滚版本'));
    console.log(chalk.green('  version-craft rollback-to <version>   ') + chalk.gray('# 回滚到指定版本'));
    console.log(chalk.green('  version-craft rollback-last           ') + chalk.gray('# 回滚到上一版本'));
    console.log(chalk.green('  version-craft rollback-status         ') + chalk.gray('# 检查回滚状态'));
    console.log(chalk.green('  version-craft rollback-checkpoint     ') + chalk.gray('# 创建回滚点'));
    console.log('');
    console.log(chalk.gray('使用 "version-craft <command> --help" 查看具体命令的详细帮助'));
  });

program
  .command('hotupdate-help')
  .description('显示热更新相关命令帮助')
  .helpOption(false) // 禁用默认帮助
  .option('-h, --help', '显示帮助信息')
  .action((options) => {
    console.log(chalk.blue('🔥 热更新管理命令:'));
    console.log('');
    console.log(chalk.green('  version-craft hotupdate-manifest      ') + chalk.gray('# 生成热更新资源清单'));
    console.log(chalk.green('  version-craft hotupdate-patch <from> <to> ') + chalk.gray('# 生成增量更新包'));
    console.log(chalk.green('  version-craft hotupdate-tag           ') + chalk.gray('# 为资源文件添加版本标记'));
    console.log(chalk.green('  version-craft hotupdate-verify <manifest> ') + chalk.gray('# 验证资源清单完整性'));
    console.log(chalk.green('  version-craft hotupdate-release       ') + chalk.gray('# 完整的热更新发布流程'));
    console.log(chalk.green('  version-craft hotupdate-clean         ') + chalk.gray('# 清理旧版本资源'));
    console.log('');
    console.log(chalk.gray('使用 "version-craft <command> --help" 查看具体命令的详细帮助'));
  });

// 错误处理
program.exitOverride((err) => {
  // 如果是帮助信息显示，正常退出
  if (err.code === 'commander.helpDisplayed') {
    process.exit(0);
  }
  // 其他错误正常抛出
  throw err;
});

process.on('uncaughtException', (error) => {
  console.error(chalk.red('未捕获的异常:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error(chalk.red('未处理的 Promise 拒绝:'), reason);
  process.exit(1);
});

// 显示自定义帮助信息的函数
function showCustomHelp() {
  console.log(chalk.green('🎮 LuckyCoin 游戏版本管理工具'));
  console.log('');
  console.log(chalk.blue('📋 快速开始:'));
  console.log('  version-craft init              # 初始化配置文件');
  console.log('  version-craft current           # 显示当前版本');
  console.log('  version-craft bump patch        # 升级补丁版本');
  console.log('  version-craft build web-mobile  # 构建 Web 版本');
  console.log('  version-craft deploy-staging    # 部署到测试环境');
  console.log('');
  console.log(chalk.blue('📚 分组帮助:'));
  console.log('  version-craft config-help       # 配置管理命令帮助');
  console.log('  version-craft version-help      # 版本管理命令帮助');
  console.log('  version-craft build-help        # 构建管理命令帮助');
  console.log('  version-craft deploy-help       # 部署管理命令帮助');
  console.log('  version-craft rollback-help     # 回滚管理命令帮助');
  console.log('  version-craft hotupdate-help    # 热更新管理命令帮助');
  console.log('');
  console.log(chalk.blue('📖 详细帮助:'));
  console.log('  version-craft --help-all        # 查看所有命令列表');
  console.log('  version-craft <command> --help  # 查看具体命令帮助');
  process.exit(0);
}

// 检查是否需要显示自定义帮助
const args = process.argv.slice(2);
if (!args.length) {
  // 无参数时显示自定义帮助
  showCustomHelp();
} else if (args.length === 1 && (args[0] === '-h' || args[0] === '--help')) {
  // 只有 -h 或 --help 参数时显示自定义帮助
  showCustomHelp();
} else if (args.includes('--help-all')) {
  // 包含 --help-all 时显示完整帮助
  showGroupedHelp();
}

// 解析命令行参数
try {
  program.parse(process.argv);
} catch (error: any) {
  // 如果是帮助信息显示，正常退出
  if (error.code === 'commander.helpDisplayed') {
    process.exit(0);
  }
  console.error(chalk.red('命令执行失败:'), error.message || error);
  process.exit(1);
}