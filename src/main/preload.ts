import { contextBridge, ipcRenderer } from 'electron';

// 定义 API 接口
export interface ElectronAPI {
  // 项目管理
  selectProjectDirectory: () => Promise<{ success: boolean; path?: string; error?: string }>;
  getCurrentProject: () => Promise<string | null>;
  getProjectInfo: () => Promise<{ success: boolean; data?: any; error?: string }>;

  // 版本管理
  getCurrentVersion: () => Promise<{ success: boolean; data?: any; error?: string }>;
  getVersionHistory: () => Promise<{ success: boolean; data?: any; error?: string }>;
  bumpVersion: (options: any) => Promise<{ success: boolean; data?: any; error?: string }>;
  rollbackVersion: (targetVersion: string, force?: boolean) => Promise<{ success: boolean; data?: any; error?: string }>;

  // 构建管理
  startBuild: (platform: string, options?: any) => Promise<{ success: boolean; data?: any; error?: string }>;

  // 系统操作
  openExternal: (url: string) => Promise<void>;
  showItemInFolder: (path: string) => Promise<void>;
  quitApp: () => Promise<void>;

  // 事件监听
  onBuildProgress: (callback: (data: any) => void) => void;
  onVersionChanged: (callback: (data: any) => void) => void;
  offBuildProgress: () => void;
  offVersionChanged: () => void;
}

// 暴露安全的 API 到渲染进程
const electronAPI: ElectronAPI = {
  // 项目管理
  selectProjectDirectory: () => ipcRenderer.invoke('select-project-directory'),
  getCurrentProject: () => ipcRenderer.invoke('get-current-project'),
  getProjectInfo: () => ipcRenderer.invoke('get-project-info'),

  // 版本管理
  getCurrentVersion: () => ipcRenderer.invoke('get-current-version'),
  getVersionHistory: () => ipcRenderer.invoke('get-version-history'),
  bumpVersion: (options) => ipcRenderer.invoke('bump-version', options),
  rollbackVersion: (targetVersion, force) => ipcRenderer.invoke('rollback-version', targetVersion, force),

  // 构建管理
  startBuild: (platform, options) => ipcRenderer.invoke('start-build', platform, options),

  // 系统操作
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  showItemInFolder: (path) => ipcRenderer.invoke('show-item-in-folder', path),
  quitApp: () => ipcRenderer.invoke('quit-app'),

  // 事件监听
  onBuildProgress: (callback) => {
    ipcRenderer.on('build-progress', (event, data) => callback(data));
  },
  onVersionChanged: (callback) => {
    ipcRenderer.on('version-changed', (event, data) => callback(data));
  },
  offBuildProgress: () => {
    ipcRenderer.removeAllListeners('build-progress');
  },
  offVersionChanged: () => {
    ipcRenderer.removeAllListeners('version-changed');
  }
};

// 将 API 暴露到全局对象
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 类型声明 (用于 TypeScript)
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
