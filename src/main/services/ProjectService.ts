import { join } from 'path';
import fs from 'fs-extra';

export class ProjectService {
  private projectPath: string;

  constructor(projectPath: string) {
    this.projectPath = projectPath;
  }

  /**
   * 获取项目信息
   */
  async getProjectInfo(): Promise<any> {
    try {
      const info: any = {
        path: this.projectPath,
        name: this.getProjectName(),
        isValidProject: await this.isValidVersionCraftProject()
      };

      // 读取 package.json
      const packagePath = join(this.projectPath, 'package.json');
      if (await fs.pathExists(packagePath)) {
        const packageJson = await fs.readJson(packagePath);
        info.package = {
          name: packageJson.name,
          version: packageJson.version,
          description: packageJson.description,
          author: packageJson.author
        };
      }

      // 读取 version-craft 配置
      const configPath = join(this.projectPath, 'version-craft.config.json');
      if (await fs.pathExists(configPath)) {
        const config = await fs.readJson(configPath);
        info.config = config;
      }

      // 获取项目统计信息
      info.stats = await this.getProjectStats();

      // 获取 Git 信息
      info.git = await this.getGitInfo();

      return info;
    } catch (error) {
      throw new Error(`获取项目信息失败: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * 检查是否为有效的 Version-Craft 项目
   */
  async isValidVersionCraftProject(): Promise<boolean> {
    const configPath = join(this.projectPath, 'version-craft.config.json');
    const packagePath = join(this.projectPath, 'package.json');
    
    return (await fs.pathExists(configPath)) && (await fs.pathExists(packagePath));
  }

  /**
   * 获取项目名称
   */
  private getProjectName(): string {
    return this.projectPath.split(/[/\\]/).pop() || 'Unknown Project';
  }

  /**
   * 获取项目统计信息
   */
  private async getProjectStats(): Promise<any> {
    try {
      const stats = {
        totalFiles: 0,
        totalSize: 0,
        lastModified: new Date(0),
        directories: {
          src: await this.getDirectoryStats(join(this.projectPath, 'src')),
          assets: await this.getDirectoryStats(join(this.projectPath, 'assets')),
          dist: await this.getDirectoryStats(join(this.projectPath, 'dist')),
          logs: await this.getDirectoryStats(join(this.projectPath, 'logs'))
        }
      };

      // 计算总计
      Object.values(stats.directories).forEach((dir: any) => {
        if (dir.exists) {
          stats.totalFiles += dir.fileCount;
          stats.totalSize += dir.size;
          if (dir.lastModified > stats.lastModified) {
            stats.lastModified = dir.lastModified;
          }
        }
      });

      return stats;
    } catch (error) {
      return {
        totalFiles: 0,
        totalSize: 0,
        lastModified: new Date(),
        directories: {}
      };
    }
  }

  /**
   * 获取目录统计信息
   */
  private async getDirectoryStats(dirPath: string): Promise<any> {
    try {
      if (!(await fs.pathExists(dirPath))) {
        return { exists: false };
      }

      const stat = await fs.stat(dirPath);
      if (!stat.isDirectory()) {
        return { exists: false };
      }

      let fileCount = 0;
      let size = 0;
      let lastModified = stat.mtime;

      const files = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const file of files) {
        const filePath = join(dirPath, file.name);
        const fileStat = await fs.stat(filePath);
        
        if (file.isFile()) {
          fileCount++;
          size += fileStat.size;
        }
        
        if (fileStat.mtime > lastModified) {
          lastModified = fileStat.mtime;
        }
      }

      return {
        exists: true,
        fileCount,
        size,
        lastModified,
        path: dirPath
      };
    } catch (error) {
      return { exists: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * 获取 Git 信息
   */
  private async getGitInfo(): Promise<any> {
    try {
      const gitDir = join(this.projectPath, '.git');
      
      if (!(await fs.pathExists(gitDir))) {
        return { hasGit: false };
      }

      // 简化的 Git 信息获取
      // 在实际项目中，可以使用 simple-git 库获取更详细的信息
      return {
        hasGit: true,
        gitDir,
        // 这里可以添加更多 Git 信息
        // 如：当前分支、最后提交、远程仓库等
      };
    } catch (error) {
      return { 
        hasGit: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 更新项目配置
   */
  async updateConfig(config: any): Promise<void> {
    try {
      const configPath = join(this.projectPath, 'version-craft.config.json');
      await fs.writeJson(configPath, config, { spaces: 2 });
    } catch (error) {
      throw new Error(`更新配置失败: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * 获取项目配置
   */
  async getConfig(): Promise<any> {
    try {
      const configPath = join(this.projectPath, 'version-craft.config.json');
      
      if (await fs.pathExists(configPath)) {
        return await fs.readJson(configPath);
      }
      
      return null;
    } catch (error) {
      throw new Error(`读取配置失败: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * 创建默认配置
   */
  async createDefaultConfig(): Promise<any> {
    const defaultConfig = {
      version: "1.0.0",
      build: {
        outputDir: "dist",
        platforms: ["web-mobile"],
        optimization: {
          minify: true,
          compress: true,
          sourcemap: false
        }
      },
      deploy: {
        environments: {
          development: {
            enabled: false
          },
          staging: {
            enabled: false
          },
          production: {
            enabled: false
          }
        }
      }
    };

    await this.updateConfig(defaultConfig);
    return defaultConfig;
  }

  /**
   * 验证项目结构
   */
  async validateProjectStructure(): Promise<{ valid: boolean; issues: string[] }> {
    const issues: string[] = [];
    
    // 检查必需文件
    const requiredFiles = [
      'package.json',
      'version-craft.config.json'
    ];

    for (const file of requiredFiles) {
      const filePath = join(this.projectPath, file);
      if (!(await fs.pathExists(filePath))) {
        issues.push(`缺少必需文件: ${file}`);
      }
    }

    // 检查推荐目录
    const recommendedDirs = ['src', 'assets'];
    for (const dir of recommendedDirs) {
      const dirPath = join(this.projectPath, dir);
      if (!(await fs.pathExists(dirPath))) {
        issues.push(`建议创建目录: ${dir}`);
      }
    }

    return {
      valid: issues.length === 0,
      issues
    };
  }
}
