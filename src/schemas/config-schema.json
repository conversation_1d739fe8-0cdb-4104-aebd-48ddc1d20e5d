{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Game Version Tool Configuration", "type": "object", "required": ["project", "build"], "properties": {"project": {"type": "object", "required": ["name", "type", "version"], "properties": {"name": {"type": "string", "minLength": 1}, "type": {"type": "string", "enum": ["cocos-creator", "unity", "unreal"]}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9.-]+)?$"}, "description": {"type": "string"}}}, "build": {"type": "object", "required": ["platforms", "outputDir"], "properties": {"platforms": {"type": "array", "items": {"type": "string", "enum": ["web-mobile", "android", "ios", "windows", "mac"]}, "minItems": 1}, "outputDir": {"type": "string", "minLength": 1}, "cocosCreator": {"type": "object", "properties": {"projectPath": {"type": "string"}, "builderPath": {"type": ["string", "null"], "description": "Cocos Creator 可执行文件路径，为空时自动检测"}}, "required": ["projectPath"]}, "optimization": {"type": "object", "properties": {"compress": {"type": "boolean"}, "minify": {"type": "boolean"}, "sourcemap": {"type": "boolean"}}}, "excludeFiles": {"type": "array", "items": {"type": "string"}}}}, "deploy": {"type": "object", "properties": {"web": {"type": "object", "properties": {"staging": {"type": "string", "format": "uri"}, "production": {"type": "string", "format": "uri"}, "credentials": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}, "token": {"type": "string"}}}}}, "android": {"type": "object", "properties": {"store": {"type": "string", "enum": ["google-play", "hua<PERSON>", "custom"]}, "keystore": {"type": "string"}, "keystorePassword": {"type": "string"}, "keyAlias": {"type": "string"}, "keyPassword": {"type": "string"}}}, "ios": {"type": "object", "properties": {"store": {"type": "string", "enum": ["app-store", "test-flight", "custom"]}, "certificatePath": {"type": "string"}, "profilePath": {"type": "string"}}}}}, "environments": {"type": "object", "patternProperties": {"^[a-zA-Z0-9_-]+$": {"type": "string"}}}, "git": {"type": "object", "properties": {"autoTag": {"type": "boolean"}, "tagPrefix": {"type": "string"}, "generateChangelog": {"type": "boolean"}, "changelogPath": {"type": "string"}}}, "notification": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "webhook": {"type": "string", "format": "uri"}, "email": {"type": "object", "properties": {"smtp": {"type": "string"}, "from": {"type": "string", "format": "email"}, "to": {"type": "array", "items": {"type": "string", "format": "email"}}}}}}}}