import * as fs from 'fs-extra';
import * as path from 'path';
import execa = require("execa")
import * as archiver from 'archiver';
import { ConfigManager } from '../config/ConfigManager';
import { BuildResult } from '../../types/version';

export class BuildManager {
  private configManager: ConfigManager;
  private projectRoot: string;

  constructor(projectRoot: string = process.cwd()) {
    this.projectRoot = projectRoot;
    this.configManager = new ConfigManager(projectRoot);
  }

  /**
   * 构建指定平台
   */
  async buildPlatform(platform: string): Promise<BuildResult> {
    const startTime = Date.now();
    const config = await this.configManager.loadConfig();
    
    try {
      console.log(`开始构建 ${platform} 平台...`);
      
      let outputPath: string;
      
      switch (platform) {
        case 'web-mobile':
          outputPath = await this.buildWebMobile();
          break;
        case 'android':
          outputPath = await this.buildAndroid();
          break;
        case 'ios':
          outputPath = await this.buildIOS();
          break;
        case 'windows':
          outputPath = await this.buildWindows();
          break;
        case 'mac':
          outputPath = await this.buildMac();
          break;
        default:
          throw new Error(`不支持的平台: ${platform}`);
      }

      const buildTime = Date.now() - startTime;
      const fileSize = await this.getDirectorySize(outputPath);

      // 如果启用了压缩，进行资源压缩
      if (config.build.optimization.compress) {
        await this.compressOutput(outputPath, platform);
      }

      console.log(`${platform} 构建完成，耗时: ${buildTime}ms`);

      return {
        platform,
        success: true,
        outputPath,
        buildTime,
        fileSize
      };

    } catch (error) {
      const buildTime = Date.now() - startTime;
      console.error(`${platform} 构建失败:`, error);

      // 构建失败后清理
      await this.cleanupFailedBuild(platform);

      return {
        platform,
        success: false,
        buildTime,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 构建所有平台
   */
  async buildAllPlatforms(): Promise<BuildResult[]> {
    const config = await this.configManager.loadConfig();
    const platforms = config.build.platforms;

    console.log(`开始构建 ${platforms.length} 个平台: ${platforms.join(', ')}`);

    // 并行构建所有平台
    const buildPromises = platforms.map(platform => this.buildPlatform(platform));
    const results = await Promise.all(buildPromises);

    // 统计构建结果
    const successful = results.filter((r: BuildResult) => r.success).length;
    const failed = results.filter((r: BuildResult) => !r.success).length;

    console.log(`构建完成: ${successful} 成功, ${failed} 失败`);

    return results;
  }

  /**
   * 构建 Web Mobile 平台
   */
  private async buildWebMobile(): Promise<string> {
    const config = await this.configManager.loadConfig();
    const outputDir = path.join(this.projectRoot, config.build.outputDir, 'web-mobile');

    // 确保输出目录存在
    await fs.ensureDir(outputDir);

    // 使用 Cocos Creator 命令行构建
    const cocosCreatorPath = this.getCocosCreatorPath();
    const buildArgs = [
      'build',
      '--project', config.build.cocosCreator?.projectPath || this.projectRoot,
      '--platform', 'web-mobile',
      '--out', outputDir
    ];

    if (config.build.optimization.minify) {
      buildArgs.push('--minify');
    }

    if (!config.build.optimization.sourcemap) {
      buildArgs.push('--no-sourcemap');
    }

    await execa(cocosCreatorPath, buildArgs, {
      cwd: this.projectRoot,
      stdio: 'inherit'
    });

    return outputDir;
  }

  /**
   * 构建 Android 平台
   */
  private async buildAndroid(): Promise<string> {
    const config = await this.configManager.loadConfig();
    const outputDir = path.join(this.projectRoot, config.build.outputDir, 'android');

    await fs.ensureDir(outputDir);

    const cocosCreatorPath = this.getCocosCreatorPath();
    const buildArgs = [
      'build',
      '--project', config.build.cocosCreator?.projectPath || this.projectRoot,
      '--platform', 'android',
      '--out', outputDir
    ];

    await execa(cocosCreatorPath, buildArgs, {
      cwd: this.projectRoot,
      stdio: 'inherit'
    });

    // 如果配置了签名信息，进行 APK 签名
    if (config.deploy.android?.keystore) {
      await this.signAndroidAPK(outputDir, config.deploy.android);
    }

    return outputDir;
  }

  /**
   * 构建 iOS 平台
   */
  private async buildIOS(): Promise<string> {
    const config = await this.configManager.loadConfig();
    const outputDir = path.join(this.projectRoot, config.build.outputDir, 'ios');

    await fs.ensureDir(outputDir);

    const cocosCreatorPath = this.getCocosCreatorPath();
    const buildArgs = [
      'build',
      '--project', config.build.cocosCreator?.projectPath || this.projectRoot,
      '--platform', 'ios',
      '--out', outputDir
    ];

    await execa(cocosCreatorPath, buildArgs, {
      cwd: this.projectRoot,
      stdio: 'inherit'
    });

    return outputDir;
  }

  /**
   * 构建 Windows 平台
   */
  private async buildWindows(): Promise<string> {
    const config = await this.configManager.loadConfig();
    const outputDir = path.join(this.projectRoot, config.build.outputDir, 'windows');

    await fs.ensureDir(outputDir);

    const cocosCreatorPath = this.getCocosCreatorPath();
    const buildArgs = [
      'build',
      '--project', config.build.cocosCreator?.projectPath || this.projectRoot,
      '--platform', 'windows',
      '--out', outputDir
    ];

    await execa(cocosCreatorPath, buildArgs, {
      cwd: this.projectRoot,
      stdio: 'inherit'
    });

    return outputDir;
  }

  /**
   * 构建 Mac 平台
   */
  private async buildMac(): Promise<string> {
    const config = await this.configManager.loadConfig();
    const outputDir = path.join(this.projectRoot, config.build.outputDir, 'mac');

    await fs.ensureDir(outputDir);

    const cocosCreatorPath = this.getCocosCreatorPath();
    const buildArgs = [
      'build',
      '--project', config.build.cocosCreator?.projectPath || this.projectRoot,
      '--platform', 'mac',
      '--out', outputDir
    ];

    await execa(cocosCreatorPath, buildArgs, {
      cwd: this.projectRoot,
      stdio: 'inherit'
    });

    return outputDir;
  }

  /**
   * 获取 Cocos Creator 可执行文件路径
   */
  private getCocosCreatorPath(): string {
    const config = this.configManager.getConfig();
    
    if (config.build.cocosCreator?.builderPath) {
      return config.build.cocosCreator.builderPath;
    }

    // 尝试在常见路径中查找 Cocos Creator
    const commonPaths = [
      'cocos',
      'CocosCreator',
      '/Applications/CocosCreator.app/Contents/MacOS/CocosCreator',
      'D:\\ProgramData\\cocos\\editors\\Creator\\3.8.6\\CocosCreator.exe'
    ];

    for (const execPath of commonPaths) {
      try {
        execa.sync(execPath, ['--version']);
        return execPath;
      } catch {
        continue;
      }
    }

    throw new Error('未找到 Cocos Creator 可执行文件，请在配置中指定 builderPath');
  }

  /**
   * Android APK 签名
   */
  private async signAndroidAPK(outputDir: string, androidConfig: any): Promise<void> {
    const apkPath = path.join(outputDir, 'app-release-unsigned.apk');
    const signedApkPath = path.join(outputDir, 'app-release-signed.apk');

    if (!await fs.pathExists(apkPath)) {
      console.warn('未找到未签名的 APK 文件，跳过签名步骤');
      return;
    }

    const jarsignerArgs = [
      '-verbose',
      '-sigalg', 'SHA1withRSA',
      '-digestalg', 'SHA1',
      '-keystore', androidConfig.keystore,
      '-storepass', androidConfig.keystorePassword,
      '-keypass', androidConfig.keyPassword,
      apkPath,
      androidConfig.keyAlias
    ];

    await execa('jarsigner', jarsignerArgs);

    // 使用 zipalign 对齐 APK
    try {
      await execa('zipalign', ['-v', '4', apkPath, signedApkPath]);
      await fs.remove(apkPath);
      console.log('APK 签名和对齐完成');
    } catch (error) {
      console.warn('zipalign 失败，但 APK 已签名:', error);
    }
  }

  /**
   * 压缩构建输出
   */
  private async compressOutput(outputPath: string, platform: string): Promise<void> {
    const compressedPath = `${outputPath}.zip`;
    
    return new Promise<void>((resolve, reject) => {
      const output = fs.createWriteStream(compressedPath);
      const archive = archiver.create('zip', { zlib: { level: 9 } });

      output.on('close', () => {
        console.log(`${platform} 构建结果已压缩: ${compressedPath}`);
        resolve();
      });

      archive.on('error', reject);
      archive.pipe(output);
      archive.directory(outputPath, false);
      archive.finalize();
    });
  }

  /**
   * 获取目录大小
   */
  private async getDirectorySize(dirPath: string): Promise<number> {
    if (!await fs.pathExists(dirPath)) {
      return 0;
    }

    let totalSize = 0;
    const files = await fs.readdir(dirPath);

    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = await fs.stat(filePath);

      if (stats.isDirectory()) {
        totalSize += await this.getDirectorySize(filePath);
      } else {
        totalSize += stats.size;
      }
    }

    return totalSize;
  }

  /**
   * 清理构建输出
   */
  async cleanBuildOutput(): Promise<void> {
    const config = await this.configManager.loadConfig();
    const outputDir = path.join(this.projectRoot, config.build.outputDir);

    if (await fs.pathExists(outputDir)) {
      await fs.remove(outputDir);
      console.log('构建输出已清理');
    }
  }

  /**
   * 获取构建统计信息
   */
  async getBuildStats(): Promise<any> {
    const config = await this.configManager.loadConfig();
    const outputDir = path.join(this.projectRoot, config.build.outputDir);

    if (!await fs.pathExists(outputDir)) {
      return { platforms: [], totalSize: 0 };
    }

    const platforms = [];
    let totalSize = 0;

    for (const platform of config.build.platforms) {
      const platformDir = path.join(outputDir, platform);
      if (await fs.pathExists(platformDir)) {
        const size = await this.getDirectorySize(platformDir);
        platforms.push({ platform, size });
        totalSize += size;
      }
    }

    return { platforms, totalSize };
  }

  /**
   * 清理失败的构建
   */
  private async cleanupFailedBuild(platform: string): Promise<void> {
    try {
      const config = await this.configManager.loadConfig();
      const buildDir = config.build.outputDir;
      const platformBuildDir = path.join(buildDir, platform);

      // 删除失败的构建输出
      if (await fs.pathExists(platformBuildDir)) {
        await fs.remove(platformBuildDir);
        console.log(`🧹 已清理失败的构建输出: ${platformBuildDir}`);
      }

      // 清理临时文件
      const tempDirs = ['temp', '.tmp'];
      for (const tempDir of tempDirs) {
        if (await fs.pathExists(tempDir)) {
          await fs.remove(tempDir);
          console.log(`🧹 已清理临时目录: ${tempDir}`);
        }
      }

    } catch (error) {
      console.warn('清理失败的构建时出错:', error);
    }
  }
}