import * as fs from 'fs-extra';
import * as path from 'path';
import * as crypto from 'crypto';
import { glob } from 'glob';
import { Logger } from '../../utils/Logger';
import { ConfigManager } from '../config/ConfigManager';

/**
 * 资源信息接口
 */
export interface ResourceInfo {
    path: string;           // 相对路径
    size: number;          // 文件大小
    checksum: string;      // MD5校验和
    version: string;       // 版本号
    priority: number;      // 优先级 (1-10, 数字越小优先级越高)
    type: 'script' | 'asset' | 'config' | 'texture' | 'audio' | 'other';
    compressed?: boolean;   // 是否压缩
    url?: string;          // 下载URL
}

/**
 * 资源清单接口
 */
export interface ResourceManifest {
    version: string;
    buildNumber: number;
    releaseDate: string;
    description: string;
    mandatory: boolean;
    totalSize: number;
    resources: ResourceInfo[];
    packageUrl?: string;
    remoteManifestUrl?: string;
    remoteVersionUrl?: string;
}

/**
 * 增量更新包接口
 */
export interface IncrementalUpdate {
    fromVersion: string;
    toVersion: string;
    updateType: 'patch' | 'full';
    addedFiles: ResourceInfo[];
    modifiedFiles: ResourceInfo[];
    deletedFiles: string[];
    totalSize: number;
    packagePath: string;
}

/**
 * 热更新管理器
 * 负责生成资源清单、计算校验和、创建增量更新包
 */
export class HotUpdateManager {
    private logger: Logger;
    private configManager: ConfigManager;
    private projectRoot: string;

    constructor() {
        this.logger = Logger.getInstance();
        this.configManager = new ConfigManager();
        this.projectRoot = process.cwd();
    }

    /**
     * 生成资源清单
     */
    async generateResourceManifest(version: string, options: {
        outputPath?: string;
        includePatterns?: string[];
        excludePatterns?: string[];
        baseUrl?: string;
        mandatory?: boolean;
        description?: string;
    } = {}): Promise<ResourceManifest> {
        try {
            const {
                outputPath = path.join(this.projectRoot, 'dist', 'manifest.json'),
                includePatterns = ['assets/**/*', 'src/**/*'],
                excludePatterns = ['**/*.meta', '**/node_modules/**', '**/.git/**'],
                baseUrl = '',
                mandatory = false,
                description = `版本 ${version} 资源清单`
            } = options;

            this.logger.info('开始生成资源清单', { version, outputPath });

            // 1. 扫描资源文件
            const resourceFiles = await this.scanResourceFiles(includePatterns, excludePatterns);
            this.logger.info(`扫描到 ${resourceFiles.length} 个资源文件`);

            // 2. 计算文件信息
            const resources: ResourceInfo[] = [];
            let totalSize = 0;

            for (const filePath of resourceFiles) {
                const resourceInfo = await this.generateResourceInfo(filePath, version, baseUrl);
                resources.push(resourceInfo);
                totalSize += resourceInfo.size;
            }

            // 3. 按优先级排序
            resources.sort((a, b) => a.priority - b.priority);

            // 4. 生成清单
            const manifest: ResourceManifest = {
                version,
                buildNumber: await this.getBuildNumber(),
                releaseDate: new Date().toISOString(),
                description,
                mandatory,
                totalSize,
                resources,
                packageUrl: baseUrl ? `${baseUrl}/packages/${version}.zip` : undefined,
                remoteManifestUrl: baseUrl ? `${baseUrl}/manifest.json` : undefined,
                remoteVersionUrl: baseUrl ? `${baseUrl}/version.json` : undefined
            };

            // 5. 保存清单文件
            await fs.ensureDir(path.dirname(outputPath));
            await fs.writeJSON(outputPath, manifest, { spaces: 2 });

            // 6. 生成简化版本信息文件
            const versionInfo = {
                version: manifest.version,
                buildNumber: manifest.buildNumber,
                releaseDate: manifest.releaseDate,
                description: manifest.description,
                mandatory: manifest.mandatory,
                totalSize: manifest.totalSize,
                resourceCount: manifest.resources.length,
                packageUrl: manifest.packageUrl
            };

            const versionPath = path.join(path.dirname(outputPath), 'version.json');
            await fs.writeJSON(versionPath, versionInfo, { spaces: 2 });

            this.logger.info('资源清单生成完成', {
                version,
                resourceCount: resources.length,
                totalSize: `${(totalSize / 1024 / 1024).toFixed(2)}MB`,
                outputPath
            });

            return manifest;

        } catch (error) {
            this.logger.error('生成资源清单失败', error);
            throw error;
        }
    }

    /**
     * 生成增量更新包
     */
    async generateIncrementalUpdate(
        fromVersion: string,
        toVersion: string,
        options: {
            outputDir?: string;
            baseUrl?: string;
        } = {}
    ): Promise<IncrementalUpdate> {
        try {
            const {
                outputDir = path.join(this.projectRoot, 'dist', 'updates'),
                baseUrl = ''
            } = options;

            this.logger.info('开始生成增量更新包', { fromVersion, toVersion });

            // 1. 加载版本清单
            const oldManifest = await this.loadManifest(fromVersion);
            const newManifest = await this.loadManifest(toVersion);

            if (!oldManifest || !newManifest) {
                throw new Error('无法加载版本清单文件');
            }

            // 2. 比较资源差异
            const { addedFiles, modifiedFiles, deletedFiles } = await this.compareManifests(oldManifest, newManifest);

            // 3. 计算更新类型和大小
            const updateType = this.determineUpdateType(addedFiles, modifiedFiles, deletedFiles, oldManifest);
            const totalSize = [...addedFiles, ...modifiedFiles].reduce((sum, file) => sum + file.size, 0);

            // 4. 创建增量包
            const packageName = `${fromVersion}_to_${toVersion}.zip`;
            const packagePath = path.join(outputDir, packageName);
            
            await this.createUpdatePackage([...addedFiles, ...modifiedFiles], packagePath);

            // 5. 生成增量更新信息
            const incrementalUpdate: IncrementalUpdate = {
                fromVersion,
                toVersion,
                updateType,
                addedFiles,
                modifiedFiles,
                deletedFiles,
                totalSize,
                packagePath
            };

            // 6. 保存增量更新信息
            const updateInfoPath = path.join(outputDir, `${fromVersion}_to_${toVersion}.json`);
            await fs.writeJSON(updateInfoPath, incrementalUpdate, { spaces: 2 });

            this.logger.info('增量更新包生成完成', {
                fromVersion,
                toVersion,
                updateType,
                addedCount: addedFiles.length,
                modifiedCount: modifiedFiles.length,
                deletedCount: deletedFiles.length,
                totalSize: `${(totalSize / 1024 / 1024).toFixed(2)}MB`,
                packagePath
            });

            return incrementalUpdate;

        } catch (error) {
            this.logger.error('生成增量更新包失败', error);
            throw error;
        }
    }

    /**
     * 为资源文件添加版本标记
     */
    async addVersionTags(version: string, options: {
        resourcePaths?: string[];
        outputDir?: string;
        tagFormat?: 'suffix' | 'query' | 'header';
    } = {}): Promise<Map<string, string>> {
        try {
            const {
                resourcePaths = [],
                outputDir = path.join(this.projectRoot, 'dist', 'versioned'),
                tagFormat = 'suffix'
            } = options;

            this.logger.info('开始添加版本标记', { version, tagFormat });

            const versionedFiles = new Map<string, string>();

            // 如果没有指定路径，扫描所有资源
            const filesToProcess = resourcePaths.length > 0 
                ? resourcePaths 
                : await this.scanResourceFiles(['assets/**/*'], ['**/*.meta']);

            await fs.ensureDir(outputDir);

            for (const filePath of filesToProcess) {
                const absolutePath = path.resolve(this.projectRoot, filePath);
                
                if (!await fs.pathExists(absolutePath)) {
                    this.logger.warn('文件不存在，跳过', { filePath });
                    continue;
                }

                const versionedPath = await this.addVersionTag(absolutePath, version, outputDir, tagFormat);
                versionedFiles.set(filePath, versionedPath);
            }

            this.logger.info('版本标记添加完成', {
                version,
                processedCount: versionedFiles.size
            });

            return versionedFiles;

        } catch (error) {
            this.logger.error('添加版本标记失败', error);
            throw error;
        }
    }

    /**
     * 扫描资源文件
     */
    private async scanResourceFiles(includePatterns: string[], excludePatterns: string[]): Promise<string[]> {
        const allFiles: string[] = [];

        for (const pattern of includePatterns) {
            const files = await glob(pattern, {
                cwd: this.projectRoot,
                ignore: excludePatterns,
                nodir: true
            });
            allFiles.push(...files);
        }

        // 去重并排序
        return [...new Set(allFiles)].sort();
    }

    /**
     * 生成单个资源文件信息
     */
    private async generateResourceInfo(filePath: string, version: string, baseUrl: string): Promise<ResourceInfo> {
        const absolutePath = path.resolve(this.projectRoot, filePath);
        const stats = await fs.stat(absolutePath);
        const checksum = await this.calculateFileChecksum(absolutePath);
        
        return {
            path: filePath.replace(/\\/g, '/'), // 统一使用正斜杠
            size: stats.size,
            checksum,
            version,
            priority: this.getFilePriority(filePath),
            type: this.getFileType(filePath),
            compressed: this.shouldCompress(filePath),
            url: baseUrl ? `${baseUrl}/${filePath.replace(/\\/g, '/')}?v=${version}` : undefined
        };
    }

    /**
     * 计算文件校验和
     */
    private async calculateFileChecksum(filePath: string): Promise<string> {
        const hash = crypto.createHash('md5');
        const stream = fs.createReadStream(filePath);
        
        return new Promise((resolve, reject) => {
            stream.on('data', (data) => hash.update(data));
            stream.on('end', () => resolve(hash.digest('hex')));
            stream.on('error', reject);
        });
    }

    /**
     * 获取文件优先级
     */
    private getFilePriority(filePath: string): number {
        const ext = path.extname(filePath).toLowerCase();
        const fileName = path.basename(filePath).toLowerCase();
        
        // 配置文件最高优先级
        if (fileName.includes('config') || ext === '.json') return 1;
        
        // 脚本文件高优先级
        if (ext === '.js' || ext === '.ts') return 2;
        
        // UI资源中等优先级
        if (filePath.includes('ui') && (ext === '.png' || ext === '.jpg')) return 3;
        
        // 音频文件低优先级
        if (ext === '.mp3' || ext === '.wav' || ext === '.ogg') return 8;
        
        // 其他资源
        return 5;
    }

    /**
     * 获取文件类型
     */
    private getFileType(filePath: string): ResourceInfo['type'] {
        const ext = path.extname(filePath).toLowerCase();
        
        if (ext === '.js' || ext === '.ts') return 'script';
        if (ext === '.json') return 'config';
        if (ext === '.png' || ext === '.jpg' || ext === '.jpeg') return 'texture';
        if (ext === '.mp3' || ext === '.wav' || ext === '.ogg') return 'audio';
        if (ext === '.prefab' || ext === '.scene' || ext === '.anim') return 'asset';
        
        return 'other';
    }

    /**
     * 判断文件是否应该压缩
     */
    private shouldCompress(filePath: string): boolean {
        const ext = path.extname(filePath).toLowerCase();
        const compressibleTypes = ['.js', '.ts', '.json', '.css', '.html', '.xml'];
        return compressibleTypes.includes(ext);
    }

    /**
     * 获取构建号
     */
    private async getBuildNumber(): Promise<number> {
        try {
            const config = await this.configManager.loadConfig();
            // 使用时间戳作为构建号，如果配置中有buildNumber则使用配置的
            return (config.project as any).buildNumber || Date.now();
        } catch {
            return Date.now();
        }
    }

    /**
     * 加载清单文件
     */
    private async loadManifest(version: string): Promise<ResourceManifest | null> {
        try {
            const manifestPath = path.join(this.projectRoot, 'dist', `manifest_${version}.json`);
            if (await fs.pathExists(manifestPath)) {
                return await fs.readJSON(manifestPath);
            }
            return null;
        } catch {
            return null;
        }
    }

    /**
     * 比较两个清单的差异
     */
    private async compareManifests(oldManifest: ResourceManifest, newManifest: ResourceManifest): Promise<{
        addedFiles: ResourceInfo[];
        modifiedFiles: ResourceInfo[];
        deletedFiles: string[];
    }> {
        const oldFiles = new Map(oldManifest.resources.map(r => [r.path, r]));
        const newFiles = new Map(newManifest.resources.map(r => [r.path, r]));

        const addedFiles: ResourceInfo[] = [];
        const modifiedFiles: ResourceInfo[] = [];
        const deletedFiles: string[] = [];

        // 查找新增和修改的文件
        for (const [path, newFile] of newFiles) {
            const oldFile = oldFiles.get(path);
            if (!oldFile) {
                addedFiles.push(newFile);
            } else if (oldFile.checksum !== newFile.checksum) {
                modifiedFiles.push(newFile);
            }
        }

        // 查找删除的文件
        for (const [path] of oldFiles) {
            if (!newFiles.has(path)) {
                deletedFiles.push(path);
            }
        }

        return { addedFiles, modifiedFiles, deletedFiles };
    }

    /**
     * 确定更新类型
     */
    private determineUpdateType(
        addedFiles: ResourceInfo[],
        modifiedFiles: ResourceInfo[],
        deletedFiles: string[],
        oldManifest: ResourceManifest
    ): 'patch' | 'full' {
        const totalChanges = addedFiles.length + modifiedFiles.length + deletedFiles.length;
        const changeRatio = totalChanges / oldManifest.resources.length;
        
        // 如果变更超过30%，认为是完整更新
        return changeRatio > 0.3 ? 'full' : 'patch';
    }

    /**
     * 创建更新包
     */
    private async createUpdatePackage(files: ResourceInfo[], packagePath: string): Promise<void> {
        // 这里可以使用zip库来创建压缩包
        // 为了简化，这里只是创建一个文件列表
        const packageInfo = {
            files: files.map(f => ({
                path: f.path,
                size: f.size,
                checksum: f.checksum
            })),
            createdAt: new Date().toISOString()
        };

        await fs.ensureDir(path.dirname(packagePath));
        await fs.writeJSON(packagePath.replace('.zip', '.json'), packageInfo, { spaces: 2 });
    }

    /**
     * 为单个文件添加版本标记
     */
    private async addVersionTag(
        filePath: string,
        version: string,
        outputDir: string,
        tagFormat: 'suffix' | 'query' | 'header'
    ): Promise<string> {
        const relativePath = path.relative(this.projectRoot, filePath);
        const parsedPath = path.parse(relativePath);
        
        let versionedPath: string;
        
        switch (tagFormat) {
            case 'suffix':
                versionedPath = path.join(
                    outputDir,
                    parsedPath.dir,
                    `${parsedPath.name}.${version}${parsedPath.ext}`
                );
                break;
            case 'query':
                versionedPath = path.join(outputDir, relativePath);
                break;
            case 'header':
            default:
                versionedPath = path.join(outputDir, relativePath);
                break;
        }

        await fs.ensureDir(path.dirname(versionedPath));
        await fs.copy(filePath, versionedPath);

        return versionedPath;
    }
}
