import * as fs from 'fs-extra';
import * as path from 'path';
import axios from 'axios';
import execa = require("execa")
import { ConfigManager } from '../config/ConfigManager';
import { DeployResult } from '../../types/version';
import { Logger } from '../../utils/Logger';

export class DeployManager {
  private configManager: ConfigManager;
  private projectRoot: string;
  private logger: Logger;

  constructor() {
    // 直接使用当前工作目录
    this.projectRoot = process.cwd();

    this.configManager = new ConfigManager();
    this.logger = Logger.getInstance();
  }

  /**
   * 部署到指定环境
   */
  async deployToEnvironment(environment: 'staging' | 'production', platform?: string): Promise<DeployResult[]> {
    const config = await this.configManager.loadConfig();
    const platforms = platform ? [platform] : config.build.platforms;

    console.log(`开始部署到 ${environment} 环境，平台: ${platforms.join(', ')}`);

    const deployPromises = platforms.map((p: string) => this.deployPlatform(p, environment));
    const results = await Promise.all(deployPromises);

    // 发送部署通知
    if (config.notification?.enabled) {
      await this.sendDeployNotification(results, environment);
    }

    return results;
  }

  /**
   * 部署单个平台
   */
  private async deployPlatform(platform: string, environment: string): Promise<DeployResult> {
    const startTime = Date.now();
    
    try {
      console.log(`部署 ${platform} 到 ${environment}...`);
      
      let url: string | undefined;
      
      switch (platform) {
        case 'web-mobile':
          url = await this.deployWeb(environment);
          break;
        case 'android':
          await this.deployAndroid(environment);
          break;
        case 'ios':
          await this.deployIOS(environment);
          break;
        default:
          throw new Error(`暂不支持 ${platform} 平台的自动部署`);
      }

      const deployTime = Date.now() - startTime;
      console.log(`${platform} 部署完成，耗时: ${deployTime}ms`);

      // 简单的部署验证
      const verificationResult = await this.verifyDeployment(platform, environment);
      if (!verificationResult) {
        console.log(`⚠️  部署验证失败，但部署过程成功`);
      }

      return {
        platform,
        environment,
        success: true,
        url,
        deployTime
      };

    } catch (error) {
      const deployTime = Date.now() - startTime;
      console.error(`${platform} 部署失败:`, error);

      return {
        platform,
        environment,
        success: false,
        deployTime,
        error: error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error)
      };
    }
  }

  /**
   * 部署 Web 版本
   */
  private async deployWeb(environment: string): Promise<string> {
    const config = await this.configManager.loadConfig();
    const webConfig = config.deploy.web;
    
    if (!webConfig) {
      throw new Error('Web 部署配置未找到');
    }

    const targetUrl = environment === 'production' ? webConfig.production : webConfig.staging;
    if (!targetUrl) {
      throw new Error(`${environment} 环境的部署 URL 未配置`);
    }

    const outputDir = path.join(this.projectRoot, config.build.outputDir, 'web-mobile');
    
    if (!await fs.pathExists(outputDir)) {
      throw new Error(`构建输出不存在: ${outputDir}`);
    }

    // 根据 URL 类型选择部署方式
    if (targetUrl.startsWith('ftp://') || targetUrl.startsWith('sftp://')) {
      await this.deployToFTP(outputDir, targetUrl, webConfig.credentials);
    } else if (targetUrl.includes('vercel') || targetUrl.includes('netlify')) {
      await this.deployToStaticHost(outputDir, targetUrl);
    } else {
      await this.deployToCustomServer(outputDir, targetUrl, webConfig.credentials);
    }

    return targetUrl;
  }

  /**
   * 部署 Android 版本
   */
  private async deployAndroid(environment: string): Promise<void> {
    const config = await this.configManager.loadConfig();
    const androidConfig = config.deploy.android;
    
    if (!androidConfig) {
      throw new Error('Android 部署配置未找到');
    }

    const outputDir = path.join(this.projectRoot, config.build.outputDir, 'android');
    const apkPath = path.join(outputDir, 'app-release-signed.apk');
    
    if (!await fs.pathExists(apkPath)) {
      throw new Error(`签名 APK 不存在: ${apkPath}`);
    }

    switch (androidConfig.store) {
      case 'google-play':
        await this.deployToGooglePlay(apkPath, environment);
        break;
      case 'huawei':
        await this.deployToHuaweiStore(apkPath, environment);
        break;
      case 'custom':
        await this.deployToCustomAndroidStore(apkPath, environment);
        break;
      default:
        throw new Error(`不支持的 Android 商店: ${androidConfig.store}`);
    }
  }

  /**
   * 部署 iOS 版本
   */
  private async deployIOS(environment: string): Promise<void> {
    const config = await this.configManager.loadConfig();
    const iosConfig = config.deploy.ios;
    
    if (!iosConfig) {
      throw new Error('iOS 部署配置未找到');
    }

    const outputDir = path.join(this.projectRoot, config.build.outputDir, 'ios');
    
    if (!await fs.pathExists(outputDir)) {
      throw new Error(`iOS 构建输出不存在: ${outputDir}`);
    }

    switch (iosConfig.store) {
      case 'app-store':
        await this.deployToAppStore(outputDir, environment);
        break;
      case 'test-flight':
        await this.deployToTestFlight(outputDir, environment);
        break;
      default:
        throw new Error(`不支持的 iOS 商店: ${iosConfig.store}`);
    }
  }

  /**
   * FTP/SFTP 部署
   */
  private async deployToFTP(sourceDir: string, targetUrl: string, credentials?: any): Promise<void> {
    // 这里可以集成 FTP 客户端库，如 basic-ftp
    console.log(`部署到 FTP: ${targetUrl}`);
    
    // 示例实现（需要根据实际情况调整）
    const ftpArgs = [
      'put',
      '-r',
      sourceDir,
      targetUrl
    ];

    if (credentials?.username) {
      ftpArgs.push('--user', credentials.username);
    }

    await execa('lftp', ftpArgs);
  }

  /**
   * 静态托管服务部署
   */
  private async deployToStaticHost(sourceDir: string, targetUrl: string): Promise<void> {
    console.log(`部署到静态托管服务: ${targetUrl}`);
    
    if (targetUrl.includes('vercel')) {
      await execa('vercel', ['--prod'], { cwd: sourceDir });
    } else if (targetUrl.includes('netlify')) {
      await execa('netlify', ['deploy', '--prod', '--dir', sourceDir]);
    }
  }

  /**
   * 自定义服务器部署
   */
  private async deployToCustomServer(sourceDir: string, targetUrl: string, credentials?: any): Promise<void> {
    console.log(`部署到自定义服务器: ${targetUrl}`);
    
    // 使用 rsync 进行部署
    const rsyncArgs = [
      '-avz',
      '--delete',
      `${sourceDir}/`,
      targetUrl
    ];

    await execa('rsync', rsyncArgs);
  }

  /**
   * Google Play 部署
   */
  private async deployToGooglePlay(apkPath: string, environment: string): Promise<void> {
    console.log(`部署到 Google Play (${environment})`);
    
    // 这里需要集成 Google Play Developer API
    // 可以使用 googleapis 包或者 fastlane
    
    if (environment === 'staging') {
      // 上传到内部测试轨道
      await execa('fastlane', ['android', 'internal', `apk:${apkPath}`]);
    } else {
      // 上传到生产轨道
      await execa('fastlane', ['android', 'production', `apk:${apkPath}`]);
    }
  }

  /**
   * 华为应用市场部署
   */
  private async deployToHuaweiStore(apkPath: string, environment: string): Promise<void> {
    console.log(`部署到华为应用市场 (${environment})`);
    
    // 华为应用市场 API 集成
    throw new Error('华为应用市场部署功能尚未实现');
  }

  /**
   * 自定义 Android 商店部署
   */
  private async deployToCustomAndroidStore(apkPath: string, environment: string): Promise<void> {
    console.log(`部署到自定义 Android 商店 (${environment})`);
    
    // 自定义部署逻辑
    throw new Error('自定义 Android 商店部署功能尚未实现');
  }

  /**
   * App Store 部署
   */
  private async deployToAppStore(outputDir: string, environment: string): Promise<void> {
    console.log(`部署到 App Store (${environment})`);
    
    // 使用 Xcode 命令行工具或 fastlane
    if (environment === 'staging') {
      await execa('fastlane', ['ios', 'testflight'], { cwd: outputDir });
    } else {
      await execa('fastlane', ['ios', 'appstore'], { cwd: outputDir });
    }
  }

  /**
   * TestFlight 部署
   */
  private async deployToTestFlight(outputDir: string, environment: string): Promise<void> {
    console.log(`部署到 TestFlight (${environment})`);
    
    await execa('fastlane', ['ios', 'testflight'], { cwd: outputDir });
  }

  /**
   * 发送部署通知
   */
  private async sendDeployNotification(results: DeployResult[], environment: string): Promise<void> {
    const config = await this.configManager.loadConfig();
    const notification = config.notification;

    if (!notification?.enabled) {
      return;
    }

    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    const message = {
      environment,
      timestamp: new Date().toISOString(),
      summary: {
        total: results.length,
        successful: successful.length,
        failed: failed.length
      },
      results: results.map(r => ({
        platform: r.platform,
        success: r.success,
        url: r.url,
        error: r.error
      }))
    };

    // Webhook 通知
    if (notification.webhook) {
      try {
        await axios.post(notification.webhook, message);
        console.log('Webhook 通知已发送');
      } catch (error) {
        console.error('Webhook 通知发送失败:', error);
      }
    }

    // 邮件通知
    if (notification.email) {
      try {
        await this.sendEmailNotification(message, notification.email);
        console.log('邮件通知已发送');
      } catch (error) {
        console.error('邮件通知发送失败:', error);
      }
    }
  }

  /**
   * 发送邮件通知
   */
  private async sendEmailNotification(message: any, emailConfig: any): Promise<void> {
    // 这里可以集成邮件发送库，如 nodemailer
    console.log('发送邮件通知:', message);
    
    // 示例实现（需要根据实际情况调整）
    // const nodemailer = require('nodemailer');
    // const transporter = nodemailer.createTransporter({ ... });
    // await transporter.sendMail({ ... });
  }

  /**
   * 获取部署历史
   */
  async getDeploymentHistory(platform?: string, environment?: string): Promise<any[]> {
    // 从部署日志或数据库中获取历史记录
    console.log('获取部署历史');

    return [];
  }

  /**
   * 简单的部署验证
   */
  private async verifyDeployment(platform: string, environment: string): Promise<boolean> {
    try {
      const config = await this.configManager.loadConfig();
      const buildDir = config.build.outputDir;
      const platformBuildDir = path.join(buildDir, platform);

      // 检查构建产物是否存在
      const buildExists = await fs.pathExists(platformBuildDir);
      if (!buildExists) {
        console.log(`❌ 构建产物不存在: ${platformBuildDir}`);
        return false;
      }

      // 检查关键文件是否存在
      const indexFile = path.join(platformBuildDir, 'index.html');
      if (platform === 'web-mobile' && !await fs.pathExists(indexFile)) {
        console.log(`❌ 关键文件不存在: ${indexFile}`);
        return false;
      }

      console.log(`✅ 部署验证通过: ${platform} -> ${environment}`);
      return true;

    } catch (error) {
      console.log(`❌ 部署验证失败:`, error);
      return false;
    }
  }
}