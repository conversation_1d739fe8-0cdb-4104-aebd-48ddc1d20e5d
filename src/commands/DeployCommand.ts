import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import ora from 'ora';
import { DeployManager } from '../modules/deploy/DeployManager';
import { ConfigManager } from '../modules/config/ConfigManager';

export class DeployCommand {
  private deployManager: DeployManager;
  private configManager: ConfigManager;

  constructor() {
    this.deployManager = new DeployManager();
    this.configManager = new ConfigManager();
  }

  /**
   * 注册所有部署相关命令到主程序
   */
  registerCommands(program: Command): void {
    // 部署到测试环境
    program
      .command('deploy-staging')
      .description('部署到测试环境')
      .option('-p, --platform <platform>', '指定平台')
      .action(async (options) => {
        await this.deployToEnvironment('staging', options);
      });

    // 部署到生产环境
    program
      .command('deploy-production')
      .alias('deploy-prod')
      .description('部署到生产环境')
      .option('-p, --platform <platform>', '指定平台')
      .action(async (options) => {
        await this.deployToEnvironment('production', options);
      });

    // 部署指定平台到指定环境
    program
      .command('deploy <environment> [platform]')
      .description('部署到指定环境 (staging|production)')
      .action(async (environment: string, platform?: string) => {
        if (platform) {
          await this.deployPlatform(platform, environment);
        } else {
          await this.deployToEnvironment(environment as 'staging' | 'production');
        }
      });

    // 查看部署历史
    program
      .command('deploy-history')
      .description('查看部署历史')
      .option('-p, --platform <platform>', '过滤平台')
      .option('-e, --environment <environment>', '过滤环境')
      .action(async (options) => {
        await this.showDeployHistory(options);
      });

    // 检查部署状态
    program
      .command('deploy-status')
      .description('检查部署状态')
      .action(async () => {
        await this.checkDeployStatus();
      });
  }

  private async deployToEnvironment(environment: 'staging' | 'production', options?: any): Promise<void> {
    try {
      const config = await this.configManager.loadConfig();
      
      console.log(chalk.blue(`🚀 部署到 ${environment} 环境`));

      // 生产环境部署需要确认
      if (environment === 'production') {
        const { confirm } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'confirm',
            message: chalk.yellow('确定要部署到生产环境吗？这将影响正式用户！'),
            default: false
          }
        ]);

        if (!confirm) {
          console.log(chalk.gray('部署已取消'));
          return;
        }
      }

      // 选择要部署的平台
      let platforms: string[];
      if (options?.platform) {
        platforms = [options.platform];
      } else {
        const { selectedPlatforms } = await inquirer.prompt([
          {
            type: 'checkbox',
            name: 'selectedPlatforms',
            message: '选择要部署的平台:',
            choices: config.build.platforms.map((platform: string) => ({
              name: platform,
              value: platform,
              checked: true
            }))
          }
        ]);
        platforms = selectedPlatforms;
      }

      if (platforms.length === 0) {
        console.log(chalk.yellow('未选择任何平台'));
        return;
      }

      const spinner = ora(`部署到 ${environment}...`).start();
      const results = await this.deployManager.deployToEnvironment(environment, platforms[0]);

      spinner.stop();

      // 显示部署结果
      console.log(chalk.blue('\n📊 部署结果:'));
      results.forEach(result => {
        if (result.success) {
          console.log(chalk.green(`  ✓ ${result.platform} - 成功 (${result.deployTime}ms)`));
          if (result.url) {
            console.log(chalk.gray(`    URL: ${result.url}`));
          }
        } else {
          console.log(chalk.red(`  ✗ ${result.platform} - 失败`));
          if (result.error) {
            console.log(chalk.red(`    错误: ${result.error}`));
          }
        }
      });

      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      console.log(chalk.green(`\n成功: ${successful}`), chalk.red(`失败: ${failed}`));

      if (successful > 0) {
        console.log(chalk.green(`\n🎉 部署完成！`));
      }

    } catch (error) {
      console.error(chalk.red('部署失败:'), error);
    }
  }

  private async deployPlatform(platform: string, environment: string): Promise<void> {
    try {
      console.log(chalk.blue(`🚀 部署 ${platform} 到 ${environment} 环境`));

      const spinner = ora(`部署 ${platform}...`).start();
      const results = await this.deployManager.deployToEnvironment(environment as any, platform);

      if (results.length > 0 && results[0].success) {
        spinner.succeed(chalk.green(`${platform} 部署成功`));
        console.log(chalk.gray(`部署时间: ${results[0].deployTime}ms`));
        if (results[0].url) {
          console.log(chalk.gray(`访问地址: ${results[0].url}`));
        }
      } else {
        spinner.fail(chalk.red(`${platform} 部署失败`));
        if (results.length > 0 && results[0].error) {
          console.error(chalk.red('错误详情:'), results[0].error);
        }
      }

    } catch (error) {
      console.error(chalk.red('部署失败:'), error);
    }
  }

  private async showDeployHistory(options?: any): Promise<void> {
    try {
      const spinner = ora('获取部署历史...').start();
      const history = await this.deployManager.getDeploymentHistory(
        options?.platform,
        options?.environment
      );
      spinner.stop();

      if (history.length === 0) {
        console.log(chalk.yellow('暂无部署历史'));
        return;
      }

      console.log(chalk.blue('📚 部署历史:'));
      
      history.forEach((record: any, index: number) => {
        console.log(chalk.gray(`${index + 1}. ${record.platform} → ${record.environment}`));
        console.log(chalk.gray(`   版本: ${record.version}`));
        console.log(chalk.gray(`   时间: ${record.timestamp}`));
        console.log(chalk.gray(`   状态: ${record.success ? '成功' : '失败'}`));
        if (record.url) {
          console.log(chalk.gray(`   地址: ${record.url}`));
        }
        console.log('');
      });

    } catch (error) {
      console.error(chalk.red('获取部署历史失败:'), error);
    }
  }

  private async checkDeployStatus(): Promise<void> {
    try {
      const config = await this.configManager.loadConfig();
      const spinner = ora('检查部署状态...').start();

      spinner.stop();
      console.log(chalk.blue('🔍 部署状态检查:'));

      // 检查各平台的部署状态
      for (const platform of config.build.platforms) {
        console.log(chalk.green(`\n${platform}:`));
        
        // 检查测试环境
        if (config.deploy.web?.staging) {
          const status = await this.checkUrlStatus(config.deploy.web.staging);
          console.log(chalk.gray(`  测试环境: ${status ? '🟢 在线' : '🔴 离线'}`));
        }

        // 检查生产环境
        if (config.deploy.web?.production) {
          const status = await this.checkUrlStatus(config.deploy.web.production);
          console.log(chalk.gray(`  生产环境: ${status ? '🟢 在线' : '🔴 离线'}`));
        }
      }

    } catch (error) {
      console.error(chalk.red('检查部署状态失败:'), error);
    }
  }

  private async checkUrlStatus(url: string): Promise<boolean> {
    try {
      const axios = require('axios');
      const response = await axios.head(url, { timeout: 5000 });
      return response.status >= 200 && response.status < 400;
    } catch {
      return false;
    }
  }
}