import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import ora from 'ora';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as semver from 'semver';
import { simpleGit, SimpleGit } from 'simple-git';
import { VersionManager } from '../modules/version/VersionManager';
import { BuildManager } from '../modules/build/BuildManager';
import { DeployManager } from '../modules/deploy/DeployManager';
import { ConfigManager } from '../modules/config/ConfigManager';
import { RollbackInfo } from '../types/version';

export class RollbackCommand {
  private versionManager: VersionManager;
  private buildManager: BuildManager;
  private deployManager: DeployManager;
  private configManager: ConfigManager;
  private git: SimpleGit;

  constructor() {
    this.versionManager = new VersionManager();
    this.buildManager = new BuildManager();
    this.deployManager = new DeployManager();
    this.configManager = new ConfigManager();
    // Git操作指向当前工作目录
    this.git = simpleGit(process.cwd());
  }

  /**
   * 注册所有回滚相关命令到主程序
   */
  registerCommands(program: Command): void {
    // 列出可回滚版本
    program
      .command('rollback-list')
      .description('列出可回滚的版本')
      .option('-l, --limit <number>', '限制显示数量', '10')
      .action(async (options) => {
        await this.listRollbackVersions(options);
      });

    // 回滚到指定版本
    program
      .command('rollback-to <version>')
      .description('回滚到指定版本')
      .option('-p, --platform <platform>', '指定平台')
      .option('-e, --environment <environment>', '指定环境', 'staging')
      .option('-f, --force', '强制回滚，跳过确认')
      .option('--skip-build', '跳过构建步骤')
      .option('--skip-deploy', '跳过部署步骤')
      .action(async (version: string, options) => {
        await this.rollbackToVersion(version, options);
      });

    // 快速回滚到上一个版本
    program
      .command('rollback-last')
      .description('回滚到上一个版本')
      .option('-p, --platform <platform>', '指定平台')
      .option('-e, --environment <environment>', '指定环境', 'staging')
      .action(async (options) => {
        await this.rollbackToLast(options);
      });

    // 检查回滚状态
    program
      .command('rollback-status')
      .description('检查回滚状态')
      .action(async () => {
        await this.checkRollbackStatus();
      });

    // 创建回滚点
    program
      .command('rollback-checkpoint [name]')
      .description('创建回滚点')
      .action(async (name?: string) => {
        await this.createCheckpoint(name);
      });
  }

  private async listRollbackVersions(options?: any): Promise<void> {
    try {
      const spinner = ora('获取可回滚版本...').start();
      const versions = await this.versionManager.getAvailableVersions();
      const currentVersion = await this.versionManager.getCurrentVersion();
      spinner.stop();

      if (versions.length === 0) {
        console.log(chalk.yellow('暂无可回滚版本'));
        return;
      }

      const limit = parseInt(options?.limit || '10');
      const limitedVersions = versions.slice(0, limit);

      console.log(chalk.blue('📚 可回滚版本:'));
      console.log('');

      for (const version of limitedVersions) {
        const rollbackInfo = await this.getRollbackInfo(version);
        
        if (version === currentVersion) {
          console.log(chalk.green(`  ✓ ${version} (当前版本)`));
        } else {
          console.log(chalk.gray(`    ${version}`));
        }
        
        console.log(chalk.gray(`      日期: ${rollbackInfo.date}`));
        console.log(chalk.gray(`      构建: ${rollbackInfo.buildExists ? '✓ 可用' : '✗ 不可用'}`));
        console.log(chalk.gray(`      提交: ${rollbackInfo.commitHash.substring(0, 8)}`));
        console.log('');
      }

      if (versions.length > limit) {
        console.log(chalk.gray(`... 还有 ${versions.length - limit} 个版本 (使用 --limit 查看更多)`));
      }

    } catch (error) {
      console.error(chalk.red('获取版本列表失败:'), error);
    }
  }

  private async rollbackToVersion(version: string, options?: any): Promise<void> {
    try {
      console.log(chalk.blue(`🔄 回滚到版本: ${version}`));

      // 检查版本是否存在
      const versions = await this.versionManager.getAvailableVersions();
      if (!versions.includes(version)) {
        console.log(chalk.red(`版本 ${version} 不存在`));
        return;
      }

      const currentVersion = await this.versionManager.getCurrentVersion();
      if (version === currentVersion) {
        console.log(chalk.yellow('已经是当前版本，无需回滚'));
        return;
      }

      // 获取回滚信息
      const rollbackInfo = await this.getRollbackInfo(version);

      // 显示回滚信息
      console.log(chalk.gray(`目标版本: ${version}`));
      console.log(chalk.gray(`创建日期: ${rollbackInfo.date}`));
      console.log(chalk.gray(`构建状态: ${rollbackInfo.buildExists ? '可用' : '不可用'}`));

      // 确认回滚
      if (!options?.force) {
        const { confirm } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'confirm',
            message: chalk.yellow(`确定要从 ${currentVersion} 回滚到 ${version} 吗？`),
            default: false
          }
        ]);

        if (!confirm) {
          console.log(chalk.gray('回滚已取消'));
          return;
        }
      }

      // 执行回滚
      await this.executeRollback(version, options);

    } catch (error) {
      console.error(chalk.red('回滚失败:'), error);
    }
  }

  private async rollbackToLast(options?: any): Promise<void> {
    try {
      const versions = await this.versionManager.getAvailableVersions();
      const currentVersion = await this.versionManager.getCurrentVersion();

      // 找到上一个版本
      const currentIndex = versions.indexOf(currentVersion);
      if (currentIndex === -1 || currentIndex === versions.length - 1) {
        console.log(chalk.yellow('没有可回滚的上一个版本'));
        return;
      }

      const lastVersion = versions[currentIndex + 1];
      console.log(chalk.blue(`🔄 回滚到上一个版本: ${lastVersion}`));

      await this.rollbackToVersion(lastVersion, { ...options, force: false });

    } catch (error) {
      console.error(chalk.red('回滚失败:'), error);
    }
  }

  private async checkRollbackStatus(): Promise<void> {
    try {
      const spinner = ora('检查回滚状态...').start();
      
      const currentVersion = await this.versionManager.getCurrentVersion();
      const versions = await this.versionManager.getAvailableVersions();
      const rollbackInfo = await this.getRollbackInfo(currentVersion);

      spinner.stop();

      console.log(chalk.blue('🔍 回滚状态检查:'));
      console.log('');
      console.log(chalk.gray(`当前版本: ${currentVersion}`));
      console.log(chalk.gray(`版本日期: ${rollbackInfo.date}`));
      console.log(chalk.gray(`构建状态: ${rollbackInfo.buildExists ? '✓ 可用' : '✗ 不可用'}`));
      console.log(chalk.gray(`可回滚版本数: ${versions.length - 1}`));
      console.log('');

      // 检查最近的回滚操作
      const deployHistory = await this.deployManager.getDeploymentHistory();
      if (deployHistory.length > 0) {
        const lastDeploy = deployHistory[0];
        console.log(chalk.gray(`最后部署: ${lastDeploy.version} (${lastDeploy.timestamp})`));
      }

    } catch (error) {
      console.error(chalk.red('检查回滚状态失败:'), error);
    }
  }

  private async createCheckpoint(name?: string): Promise<void> {
    try {
      const currentVersion = await this.versionManager.getCurrentVersion();
      const checkpointName = name || `checkpoint-${Date.now()}`;

      console.log(chalk.blue(`📍 创建回滚点: ${checkpointName}`));

      // 创建 Git 标签作为回滚点
      const tagMessage = `Rollback checkpoint: ${checkpointName}`;
      const spinner = ora('创建回滚点...').start();

      await this.versionManager.createTag(`${currentVersion}-${checkpointName}`, tagMessage);
      
      spinner.succeed(chalk.green(`回滚点 ${checkpointName} 创建成功`));
      console.log(chalk.gray(`版本: ${currentVersion}`));
      console.log(chalk.gray(`标签: ${currentVersion}-${checkpointName}`));

    } catch (error) {
      console.error(chalk.red('创建回滚点失败:'), error);
    }
  }

  private async executeRollback(version: string, options?: any): Promise<void> {
    // 检查 Git 工作区状态
    const status = await this.git.status();
    if (status.files.length > 0) {
      console.log(chalk.yellow('⚠️  检测到未提交的更改，正在自动处理...'));

      // 显示未提交的文件
      console.log(chalk.gray('   未提交的文件:'));
      status.files.forEach(file => {
        console.log(chalk.gray(`     • ${file.path} (${file.working_dir})`));
      });

      // 自动暂存更改
      console.log(chalk.blue('📦 正在暂存更改...'));
      await this.git.stash(['push', '-m', `回滚前自动暂存 - ${new Date().toISOString()}`]);
      console.log(chalk.green('✅ 更改已暂存'));
    }

    const steps = [
      { name: '更新版本号', action: () => this.updateVersionTo(version), required: true },
      { name: '重新构建', action: () => this.rebuildVersion(version, options?.platform), required: false, skip: options?.skipBuild },
      { name: '重新部署', action: () => this.redeployVersion(version, options), required: false, skip: options?.skipDeploy }
    ];

    for (const step of steps) {
      if (step.skip) {
        console.log(chalk.yellow(`⏭️  跳过: ${step.name}`));
        continue;
      }

      const spinner = ora(step.name).start();
      try {
        await step.action();
        spinner.succeed(chalk.green(`${step.name} 完成`));
      } catch (error) {
        spinner.fail(chalk.red(`${step.name} 失败`));

        if (step.required) {
          throw error;
        } else {
          console.log(chalk.yellow(`⚠️  ${step.name} 失败，但继续执行后续步骤`));
          console.log(chalk.gray(`   错误: ${error instanceof Error ? error.message : String(error)}`));
        }
      }
    }

    console.log(chalk.green(`\n🎉 回滚到版本 ${version} 完成！`));

    // 提示恢复暂存的更改
    if (status.files.length > 0) {
      console.log(chalk.blue('\n📦 提示: 有更改被自动暂存'));
      console.log(chalk.gray('   使用以下命令恢复: git stash pop'));
    }
  }

  /**
   * 更新版本到指定版本
   * 新方案：版本号递增 + 回滚标记，确保不影响alpha等预发布标识
   */
  private async updateVersionTo(version: string): Promise<void> {
    try {
      const currentVersion = await this.versionManager.getCurrentVersion();

      // 检查目标版本是否存在
      const tagName = version.startsWith('v') ? version : `v${version}`;
      const tagExists = await this.checkGitTagExists(version);
      if (!tagExists) {
        throw new Error(`目标版本不存在: ${version}`);
      }

      // 新方案：版本号递增，代码状态回滚
      const nextVersion = await this.calculateNextRollbackVersion(currentVersion, version);

      console.log(chalk.yellow(`📋 回滚方案:`));
      console.log(chalk.yellow(`   版本号: ${currentVersion} → ${nextVersion} (递增)`));
      console.log(chalk.yellow(`   代码状态: 回滚到 ${version}`));

      /*const { confirmRollback } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirmRollback',
        message: `确认执行回滚吗？`,
        default: false
      }]);

      if (!confirmRollback) {
        console.log(chalk.red('回滚操作已取消'));
        return;
      }*/

      // 1. Git checkout到目标版本的代码状态
      await this.git.checkout([tagName]);
      console.log(chalk.green(`✅ 代码已切换到: ${tagName}`));

      // 2. 但版本号设置为递增的版本
      await this.updateAllVersionFiles(nextVersion);

      // 3. 创建新的Git标签
      await this.versionManager.createTag(nextVersion, `Rollback to ${version} code state`);

      console.log(chalk.green(`✅ 回滚完成:`));
      console.log(chalk.green(`   版本号: ${nextVersion}`));
      console.log(chalk.green(`   代码状态: ${version}`));
      console.log(chalk.green(`   Git标签: v${nextVersion}`));

    } catch (error) {
      console.error(chalk.red('回滚失败:'), error);
      throw error;
    }
  }

  private async rebuildVersion(version: string, platform?: string): Promise<void> {
    try {
      console.log(chalk.blue(`🔨 开始重新构建 (版本: ${version})`));

      if (platform) {
        console.log(chalk.gray(`构建平台: ${platform}`));
        await this.buildManager.buildPlatform(platform);
        console.log(chalk.green(`✅ ${platform} 构建完成`));
      } else {
        console.log(chalk.gray('构建所有配置的平台...'));
        await this.buildManager.buildAllPlatforms();
        console.log(chalk.green('✅ 所有平台构建完成'));
      }
    } catch (error) {
      // 检查是否是 Cocos Creator 不可用的错误
      const errorMessage = error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('ENOENT') ||
          errorMessage.includes('command not found') ||
          errorMessage.includes('CocosCreator') ||
          errorMessage.includes('spawn') ||
          errorMessage.includes('not found')) {
        throw new Error('Cocos Creator 不可用，请检查安装路径或在配置文件中设置 builderPath');
      }

      throw new Error(`构建失败: ${errorMessage}`);
    }
  }

  private async redeployVersion(version: string, options?: any): Promise<void> {
    const environment = options?.environment || 'staging';
    const platform = options?.platform;

    await this.deployManager.deployToEnvironment(environment, platform);
  }

  private async getRollbackInfo(version: string): Promise<RollbackInfo> {
    // 这里应该实现获取版本详细信息的逻辑
    // 包括日期、提交哈希、构建状态等
    return {
      version,
      date: new Date().toISOString().split('T')[0],
      commitHash: 'abcd1234567890abcd1234567890abcd12345678',
      buildExists: true
    };
  }

  /**
   * 计算下一个回滚版本号
   * 确保版本号递增，同时保护alpha、beta、rc等预发布标识
   */
  private async calculateNextRollbackVersion(currentVersion: string, targetVersion: string): Promise<string> {
    const currentParsed = semver.parse(currentVersion);
    if (!currentParsed) {
      throw new Error(`当前版本格式无效: ${currentVersion}`);
    }

    let nextVersion: string;

    if (currentParsed.prerelease.length > 0) {
      // 当前是预发布版本，保持预发布类型，只递增序号
      // 例如: 1.4.3-alpha.2 → 1.4.3-alpha.3-rollback-to-1.3.0
      const prereleaseType = currentParsed.prerelease[0] as string;
      const prereleaseNumber = (currentParsed.prerelease[1] as number) || 0;
      const baseVersion = `${currentParsed.major}.${currentParsed.minor}.${currentParsed.patch}`;

      nextVersion = `${baseVersion}-${prereleaseType}.${prereleaseNumber + 1}-rollback-to-${targetVersion}`;
    } else {
      // 当前是正式版本，递增patch版本
      // 例如: 1.4.3 → 1.4.4-rollback-to-1.3.0
      const patchIncremented = semver.inc(currentVersion, 'patch');
      nextVersion = `${patchIncremented}-rollback-to-${targetVersion}`;
    }

    return nextVersion;
  }

  /**
   * 检查Git标签是否存在
   */
  private async checkGitTagExists(version: string): Promise<boolean> {
    try {
      const tags = await this.git.tags();
      const tagName = version.startsWith('v') ? version : `v${version}`;
      return tags.all.includes(tagName);
    } catch (error) {
      console.warn(chalk.yellow('检查Git标签失败:', error));
      return false;
    }
  }

  /**
   * 更新所有版本相关文件
   * 简化实现，直接更新版本号
   */
  private async updateAllVersionFiles(version: string): Promise<void> {
    const filesToUpdate = [
      {
        path: 'version-craft.config.json',
        updater: async () => {
          const config = await this.configManager.loadConfig();
          config.project.version = version;
          // 直接写入，不使用复杂的saveConfig
          const configPath = path.join(process.cwd(), 'version-craft.config.json');
          await fs.writeJSON(configPath, config, { spaces: 2 });
        }
      },
      {
        path: 'package.json',
        updater: async () => {
          const packageJsonPath = path.join(process.cwd(), 'package.json');
          if (await fs.pathExists(packageJsonPath)) {
            const packageJson = await fs.readJSON(packageJsonPath);
            packageJson.version = version;
            await fs.writeJSON(packageJsonPath, packageJson, { spaces: 2 });
          }
        }
      },
      {
        path: 'web/package.json',
        updater: async () => {
          const webPackageJsonPath = path.join(process.cwd(), 'web/package.json');
          if (await fs.pathExists(webPackageJsonPath)) {
            const webPackageJson = await fs.readJSON(webPackageJsonPath);
            webPackageJson.version = version;
            await fs.writeJSON(webPackageJsonPath, webPackageJson, { spaces: 2 });
          }
        }
      }
    ];

    for (const fileInfo of filesToUpdate) {
      try {
        await fileInfo.updater();
        console.log(chalk.green(`✅ ${fileInfo.path} 版本已更新: ${version}`));
      } catch (error) {
        console.warn(chalk.yellow(`⚠️  更新 ${fileInfo.path} 失败:`, error));
      }
    }
  }
}