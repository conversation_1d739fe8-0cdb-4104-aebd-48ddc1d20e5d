import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import ora from 'ora';
import { BuildManager } from '../modules/build/BuildManager';
import { ConfigManager } from '../modules/config/ConfigManager';

export class BuildCommand {
  private buildManager: BuildManager;
  private configManager: ConfigManager;

  constructor() {
    this.buildManager = new BuildManager();
    this.configManager = new ConfigManager();
  }

  /**
   * 注册所有构建相关命令到主程序
   */
  registerCommands(program: Command): void {
    // 构建所有平台
    program
      .command('build-all')
      .description('构建所有平台')
      .option('-c, --clean', '构建前清理输出目录')
      .action(async (options) => {
        await this.buildAll(options);
      });

    // 构建指定平台
    program
      .command('build <platform>')
      .description('构建指定平台 (web-mobile|android|ios|windows|mac)')
      .option('-c, --clean', '构建前清理输出目录')
      .action(async (platform: string, options) => {
        await this.buildPlatform(platform, options);
      });

    // 构建 Web 版本 - 快捷命令
    program
      .command('build-web')
      .description('构建 Web Mobile 版本')
      .option('-c, --clean', '构建前清理输出目录')
      .action(async (options) => {
        await this.buildPlatform('web-mobile', options);
      });

    // 构建 Android 版本 - 快捷命令
    program
      .command('build-android')
      .description('构建 Android 版本')
      .option('-c, --clean', '构建前清理输出目录')
      .option('-s, --sign', '自动签名 APK')
      .action(async (options) => {
        await this.buildPlatform('android', options);
      });

    // 构建 iOS 版本 - 快捷命令
    program
      .command('build-ios')
      .description('构建 iOS 版本')
      .option('-c, --clean', '构建前清理输出目录')
      .action(async (options) => {
        await this.buildPlatform('ios', options);
      });

    // 清理构建输出
    program
      .command('build-clean')
      .description('清理构建输出')
      .action(async () => {
        await this.cleanBuild();
      });

    // 查看构建统计
    program
      .command('build-stats')
      .description('查看构建统计信息')
      .action(async () => {
        await this.showBuildStats();
      });
  }

  private async buildAll(options?: any): Promise<void> {
    try {
      const config = await this.configManager.loadConfig();
      console.log(chalk.blue(`🔨 开始构建所有平台: ${config.build.platforms.join(', ')}`));

      // 清理构建输出
      if (options?.clean) {
        const cleanSpinner = ora('清理构建输出...').start();
        await this.buildManager.cleanBuildOutput();
        cleanSpinner.succeed(chalk.green('构建输出已清理'));
      }

      // 显示构建进度
      const spinner = ora('构建中...').start();
      
      const startTime = Date.now();
      const results = await this.buildManager.buildAllPlatforms();
      const totalTime = Date.now() - startTime;

      spinner.stop();

      // 显示构建结果
      console.log(chalk.blue('\n📊 构建结果:'));
      results.forEach(result => {
        if (result.success) {
          console.log(chalk.green(`  ✓ ${result.platform} - 成功 (${result.buildTime}ms)`));
          if (result.fileSize) {
            const sizeInMB = (result.fileSize / 1024 / 1024).toFixed(2);
            console.log(chalk.gray(`    大小: ${sizeInMB}MB`));
          }
        } else {
          console.log(chalk.red(`  ✗ ${result.platform} - 失败`));
          if (result.error) {
            console.log(chalk.red(`    错误: ${result.error}`));
          }
        }
      });

      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      console.log(chalk.blue(`\n总耗时: ${totalTime}ms`));
      console.log(chalk.green(`成功: ${successful}`), chalk.red(`失败: ${failed}`));

    } catch (error) {
      console.error(chalk.red('构建失败:'), error);
    }
  }

  private async buildPlatform(platform: string, options?: any): Promise<void> {
    try {
      const config = await this.configManager.loadConfig();
      
      // 检查平台是否在配置中
      if (!config.build.platforms.includes(platform as any)) {
        console.log(chalk.yellow(`平台 ${platform} 未在配置中启用`));
        
        const { enable } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'enable',
            message: `是否要构建 ${platform} 平台？`,
            default: true
          }
        ]);

        if (!enable) {
          console.log(chalk.gray('构建已取消'));
          return;
        }
      }

      console.log(chalk.blue(`🔨 构建 ${platform} 平台...`));

      // 清理构建输出
      if (options?.clean) {
        const cleanSpinner = ora('清理构建输出...').start();
        await this.buildManager.cleanBuildOutput();
        cleanSpinner.succeed(chalk.green('构建输出已清理'));
      }

      const spinner = ora(`构建 ${platform}...`).start();
      const result = await this.buildManager.buildPlatform(platform);

      if (result.success) {
        spinner.succeed(chalk.green(`${platform} 构建成功`));
        console.log(chalk.gray(`构建时间: ${result.buildTime}ms`));
        console.log(chalk.gray(`输出路径: ${result.outputPath}`));
        
        if (result.fileSize) {
          const sizeInMB = (result.fileSize / 1024 / 1024).toFixed(2);
          console.log(chalk.gray(`文件大小: ${sizeInMB}MB`));
        }
      } else {
        spinner.fail(chalk.red(`${platform} 构建失败`));
        if (result.error) {
          console.error(chalk.red('错误详情:'), result.error);
        }
      }

    } catch (error) {
      console.error(chalk.red('构建失败:'), error);
    }
  }

  private async cleanBuild(): Promise<void> {
    try {
      const { confirm } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirm',
          message: '确定要清理所有构建输出吗？',
          default: false
        }
      ]);

      if (!confirm) {
        console.log(chalk.gray('清理已取消'));
        return;
      }

      const spinner = ora('清理构建输出...').start();
      await this.buildManager.cleanBuildOutput();
      spinner.succeed(chalk.green('构建输出已清理'));

    } catch (error) {
      console.error(chalk.red('清理失败:'), error);
    }
  }

  private async showBuildStats(): Promise<void> {
    try {
      const spinner = ora('获取构建统计...').start();
      const stats = await this.buildManager.getBuildStats();
      spinner.stop();

      if (stats.platforms.length === 0) {
        console.log(chalk.yellow('暂无构建输出'));
        return;
      }

      console.log(chalk.blue('📊 构建统计:'));
      
      stats.platforms.forEach((platform: any) => {
        const sizeInMB = (platform.size / 1024 / 1024).toFixed(2);
        console.log(chalk.green(`  ${platform.platform}: ${sizeInMB}MB`));
      });

      const totalSizeInMB = (stats.totalSize / 1024 / 1024).toFixed(2);
      console.log(chalk.blue(`\n总大小: ${totalSizeInMB}MB`));

    } catch (error) {
      console.error(chalk.red('获取统计信息失败:'), error);
    }
  }
}