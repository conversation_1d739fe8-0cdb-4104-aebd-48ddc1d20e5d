import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import ora from 'ora';
import { VersionManager } from '../modules/version/VersionManager';
import { ConfigManager } from '../modules/config/ConfigManager';
import { VersionType, PrereleaseType } from '../types/version';
import { ProjectDetector } from '../utils/ProjectDetector';
import { contextManager } from '../utils/ContextManager';
import * as path from 'path';

export class VersionCommand {
  private versionManager: VersionManager;
  private configManager: ConfigManager;
  private projectRoot: string;

  constructor(projectRoot?: string) {
    // 智能项目路径解析
    if (projectRoot) {
      this.projectRoot = path.resolve(projectRoot);
    } else if (contextManager.isInitialized()) {
      this.projectRoot = contextManager.getProjectRoot();
    } else {
      this.projectRoot = ProjectDetector.findProjectRoot();
    }

    this.versionManager = new VersionManager(this.projectRoot);
    this.configManager = new ConfigManager(this.projectRoot);
  }

  /**
   * 注册所有版本管理相关命令到主程序
   */
  registerCommands(program: Command): void {
    // 显示当前版本
    program
      .command('current')
      .alias('show')
      .description('显示当前版本')
      .action(async () => {
        await this.showCurrentVersion();
      });

    // 升级版本
    program
      .command('bump [type]')
      .description('升级版本号 (major|minor|patch|prerelease)')
      .option('-p, --prerelease <type>', '预发布类型 (alpha|beta|rc)')
      .option('-t, --tag', '自动创建 Git 标签')
      .option('-c, --changelog', '生成变更日志')
      .action(async (type: VersionType, options) => {
        await this.bumpVersion(type, options);
      });

    // 创建标签
    program
      .command('tag [version]')
      .description('为指定版本创建 Git 标签')
      .option('-m, --message <message>', '标签信息')
      .action(async (version: string, options) => {
        await this.createTag(version, options);
      });

    // 生成变更日志
    program
      .command('changelog')
      .description('生成变更日志')
      .option('-f, --from <version>', '起始版本')
      .action(async (options) => {
        await this.generateChangelog(options);
      });

    // 列出所有版本
    program
      .command('list')
      .description('列出所有可用版本')
      .action(async () => {
        await this.listVersions();
      });

    // 从预发布版本发布正式版本
    program
      .command('release')
      .description('从预发布版本发布正式版本')
      .option('-t, --tag', '自动创建 Git 标签')
      .option('-c, --changelog', '生成变更日志')
      .action(async (options) => {
        await this.releaseFromPrerelease(options);
      });
  }



  private async showCurrentVersion(): Promise<void> {
    try {
      const spinner = ora('获取当前版本...').start();
      const version = await this.versionManager.getCurrentVersion();
      spinner.stop();

      console.log(chalk.green('当前版本:'), chalk.bold(version));
    } catch (error) {
      console.error(chalk.red('获取版本失败:'), error);
    }
  }

  private async bumpVersion(type?: VersionType, options?: any): Promise<void> {
    try {
      // 如果没有指定类型，提示用户选择
      if (!type) {
        const { versionType } = await inquirer.prompt([
          {
            type: 'list',
            name: 'versionType',
            message: '选择版本升级类型:',
            choices: [
              { name: '补丁版本 (patch) - 修复bug', value: 'patch' },
              { name: '次版本 (minor) - 新功能', value: 'minor' },
              { name: '主版本 (major) - 重大变更', value: 'major' },
              { name: '预发布 (prerelease) - 测试版本', value: 'prerelease' }
            ]
          }
        ]);
        type = versionType;
      }

      let prerelease: PrereleaseType | undefined;
      if (type === 'prerelease') {
        if (options?.prerelease) {
          prerelease = options.prerelease;
        } else {
          const { prereleaseType } = await inquirer.prompt([
            {
              type: 'list',
              name: 'prereleaseType',
              message: '选择预发布类型:',
              choices: [
                { name: 'Alpha (alpha) - 内测版本', value: 'alpha' },
                { name: 'Beta (beta) - 公测版本', value: 'beta' },
                { name: 'Release Candidate (rc) - 候选版本', value: 'rc' }
              ]
            }
          ]);
          prerelease = prereleaseType;
        }
      }

      const spinner = ora('升级版本号...').start();
      // 确保 type 不为 undefined
      if (!type) {
        spinner.fail('版本类型未指定');
        throw new Error('版本类型不能为空');
      }
      const versionInfo = await this.versionManager.bumpVersion(type, prerelease);
      spinner.succeed(chalk.green(`版本已升级: ${versionInfo.current} → ${versionInfo.next}`));

      // 创建 Git 标签
      if (options?.tag) {
        const tagSpinner = ora('创建 Git 标签...').start();
        try {
          await this.versionManager.createTag(versionInfo.next);
          tagSpinner.succeed(chalk.green('Git 标签创建成功'));
        } catch (error) {
          tagSpinner.fail(chalk.red('Git 标签创建失败'));
          console.error(error);
        }
      }

      // 生成变更日志
      if (options?.changelog) {
        const changelogSpinner = ora('生成变更日志...').start();
        try {
          await this.versionManager.generateChangelog();
          changelogSpinner.succeed(chalk.green('变更日志生成成功'));
        } catch (error) {
          changelogSpinner.fail(chalk.red('变更日志生成失败'));
          console.error(error);
        }
      }

    } catch (error) {
      console.error(chalk.red('版本升级失败:'), error);
    }
  }

  private async createTag(version?: string, options?: any): Promise<void> {
    try {
      if (!version) {
        version = await this.versionManager.getCurrentVersion();
      }

      const message = options?.message || `Release version ${version}`;
      
      const spinner = ora(`创建标签 ${version}...`).start();
      await this.versionManager.createTag(version, message);
      spinner.succeed(chalk.green(`标签 ${version} 创建成功`));

    } catch (error) {
      console.error(chalk.red('标签创建失败:'), error);
    }
  }

  private async generateChangelog(options?: any): Promise<void> {
    try {
      const spinner = ora('生成变更日志...').start();
      const changelog = await this.versionManager.generateChangelog(options?.from);
      spinner.succeed(chalk.green('变更日志生成成功'));

      console.log(chalk.blue('\n变更日志预览:'));
      console.log(chalk.gray(`## [${changelog.version}] - ${changelog.date}`));
      
      if (changelog.changes.added.length > 0) {
        console.log(chalk.green('\n### 新增'));
        changelog.changes.added.forEach((change: string) => {
          console.log(chalk.gray(`- ${change}`));
        });
      }

      if (changelog.changes.fixed.length > 0) {
        console.log(chalk.blue('\n### 修复'));
        changelog.changes.fixed.forEach((change: string) => {
          console.log(chalk.gray(`- ${change}`));
        });
      }

    } catch (error) {
      console.error(chalk.red('变更日志生成失败:'), error);
    }
  }

  private async listVersions(): Promise<void> {
    try {
      const spinner = ora('获取版本列表...').start();
      const versions = await this.versionManager.getAvailableVersions();
      const currentVersion = await this.versionManager.getCurrentVersion();
      spinner.stop();

      if (versions.length === 0) {
        console.log(chalk.yellow('暂无可用版本'));
        return;
      }

      console.log(chalk.blue('可用版本:'));
      versions.forEach(version => {
        if (version === currentVersion) {
          console.log(chalk.green(`  ✓ ${version} (当前版本)`));
        } else {
          console.log(chalk.gray(`    ${version}`));
        }
      });

    } catch (error) {
      console.error(chalk.red('获取版本列表失败:'), error);
    }
  }

  private async releaseFromPrerelease(options?: any): Promise<void> {
    try {
      const currentVersion = await this.versionManager.getCurrentVersion();

      // 检查当前版本是否为预发布版本
      if (!currentVersion.includes('-')) {
        console.log(chalk.yellow('当前版本不是预发布版本，无需发布'));
        return;
      }

      console.log(chalk.blue(`准备从预发布版本 ${currentVersion} 发布正式版本`));

      const { confirm } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirm',
        message: '确认发布正式版本吗？',
        default: true
      }]);

      if (!confirm) {
        console.log(chalk.yellow('发布已取消'));
        return;
      }

      const spinner = ora('发布正式版本...').start();
      const versionInfo = await this.versionManager.releaseFromPrerelease();
      spinner.succeed(chalk.green(`正式版本已发布: ${versionInfo.current} → ${versionInfo.next}`));

      // 创建 Git 标签
      if (options?.tag) {
        const tagSpinner = ora('创建 Git 标签...').start();
        try {
          await this.versionManager.createTag(versionInfo.next);
          tagSpinner.succeed(chalk.green('Git 标签创建成功'));
        } catch (error) {
          tagSpinner.fail(chalk.red('Git 标签创建失败'));
          console.error(error);
        }
      }

      // 生成变更日志
      if (options?.changelog) {
        const changelogSpinner = ora('生成变更日志...').start();
        try {
          await this.versionManager.generateChangelog();
          changelogSpinner.succeed(chalk.green('变更日志生成成功'));
        } catch (error) {
          changelogSpinner.fail(chalk.red('变更日志生成失败'));
          console.error(error);
        }
      }

    } catch (error) {
      console.error(chalk.red('发布正式版本失败:'), error);
    }
  }
}