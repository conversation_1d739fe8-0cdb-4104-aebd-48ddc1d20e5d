export interface GameVersionConfig {
  project: {
    name: string;
    type: 'cocos-creator' | 'unity' | 'unreal';
    version: string;
    description?: string;
  };
  
  build: {
    platforms: Array<'web-mobile' | 'android' | 'ios' | 'windows' | 'mac'>;
    outputDir: string;
    cocosCreator?: {
      projectPath: string;
      builderPath?: string;
    };
    optimization: {
      compress: boolean;
      minify: boolean;
      sourcemap: boolean;
    };
    excludeFiles?: string[];
  };
  
  deploy: {
    web?: {
      staging?: string;
      production?: string;
      credentials?: {
        username?: string;
        password?: string;
        token?: string;
      };
    };
    android?: {
      store: 'google-play' | 'huawei' | 'custom';
      keystore?: string;
      keystorePassword?: string;
      keyAlias?: string;
      keyPassword?: string;
    };
    ios?: {
      store: 'app-store' | 'test-flight' | 'custom';
      certificatePath?: string;
      profilePath?: string;
    };
  };
  
  environments: {
    [key: string]: string;
  };
  
  git: {
    autoTag: boolean;
    tagPrefix: string;
    generateChangelog: boolean;
    changelogPath: string;
  };
  
  notification?: {
    enabled: boolean;
    webhook?: string;
    email?: {
      smtp: string;
      from: string;
      to: string[];
    };
  };
}