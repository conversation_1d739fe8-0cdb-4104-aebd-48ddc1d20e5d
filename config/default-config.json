{"project": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "cocos-creator", "version": "0.1.0", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "build": {"platforms": ["web-mobile"], "outputDir": "./dist", "cocosCreator": {"projectPath": ".", "builderPath": null}, "optimization": {"compress": true, "minify": true, "sourcemap": false}, "excludeFiles": ["*.log", "node_modules/**", ".git/**", "temp/**", "library/**"]}, "deploy": {"web": {"staging": "https://staging-luckycoin.example.com", "production": "https://luckycoin.example.com"}, "android": {"store": "google-play", "keystore": "./android/release.keystore"}}, "environments": {"dev": "./assets/resources/config", "test": "./config/test", "prod": "./config/prod"}, "git": {"autoTag": true, "tagPrefix": "v", "generateChangelog": true, "changelogPath": "./CHANGELOG.md"}, "notification": {"enabled": false, "webhook": null, "email": null}}