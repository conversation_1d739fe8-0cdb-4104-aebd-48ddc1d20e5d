{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/renderer/*"],
      "@main/*": ["src/main/*"],
      "@shared/*": ["src/shared/*"]
    },

    /* Type definitions */
    "types": ["vite/client", "node", "electron"]
  },
  "include": [
    "src/renderer/**/*.ts",
    "src/renderer/**/*.d.ts",
    "src/renderer/**/*.tsx",
    "src/renderer/**/*.vue",
    "src/shared/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "release"
  ]
}
