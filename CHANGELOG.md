# 变更日志

所有重要的变更都将记录在此文件中。

## [0.2.0] - 2025-08-24

### 新增
- 新增在线奖励，v1.0.122
- 完善场地到期的逻辑；新增称号；v1.0.121
- 新增欢迎界面；新增主线任务的手指引导动画；v1.0.114
- 新增主线任务UI，v1.0.111.
- 新增排行榜，v1.0.110.
- 新增新手指引；调整菜单栏ui；新增排行榜ui;
- 设置界面调整，新增重置确认框。
- 新增雇员。
- 新增雇员及动画
- 新增loading场景，并命名为main，原main场景改名为game。为导入第三方框架oopsFrameWork做准备。
- 新增场地使用有效期倒计时。
- 员工增加使用中背景和广告背景；取消商品排序；新增广告按钮直接赠送；
- 新增主界面底部菜单按钮红点。
- 新增-设置页面。
- 新增-漂浮文字；调整角色动画锚点；增加角色和硬币的显示层级切换；
- 新增-任务达成-碎片介绍页面。
- 新增-任务达成；技能背景随等级变换；
- 新增技能详情页。
- 新增-高效工作。
- 新增-临时增值 功能。
- 新增主动技能详情页。
- 新增硬币增值功能、按钮大小调整。
- 新增场景切换功能。
- 新增马和兔子。
- 新增牛雇员
- 新增银币转金币功能。
- 新增铜币转银技能

### 变更
- 修改为真实信息
- web init
- 更新ui，v1.0.123
- 记录玩家开始游戏的时间。
- 增加赚钱目标tips的逻辑。
- 更改命名
- 资源改名，去掉helper字样。
- 更改预制体名称
- 更改图集名称
- 增加员工进场、离场动画；版本号 v1.0.120
- 任务奖励调整，降低钻石奖励数值。增加新手引导时，菜单按钮的点击限制。
- v1.0.119
- 增加简单版的离线收益逻辑。
- 增加赚钱任务类型。
- 新手步骤调整；v1.0.117
- 更新手指和欢迎界面，调整所有界面透明黑底的点击关闭层级。v1.0.116
- 新手指引前几个步骤加上黑色半透明遮罩底。v1.0.115
- v1.0.114
- 增加音效及背景音乐。
- 版本号：v1.0.112
- 主线任务完成.
- 硬币热区调整。
- v1.0.105
- 更改版本号。
- 更改版本号。
- 数值调整。
- 设置页面版本号样式调整。
- 硬币图集调整为保持原尺寸。
- 重置游戏。
- 硬币图片改为保持原始尺寸，不做缩放；限制硬币数量，达到购买数量上限，升级后不可再购买；
- loading进度条九宫袼处理。
- 音量条增加记录。
- 牛图集
- 拆分雇员动画图集。
- 动画文件里如果设置position，会影响动画节点。
- 。
- 屏蔽原动画节点
- 雇员动画调整
- 增加loading界面。
- 雇员增加有效期倒计时。
- 调整目录结构
- 技能释放完再cd
- 广告获得所有碎片。
- 调整角色idle动画。
- 增加“立即生产”技能。
- 还原雇员尺寸，增加雇员移动时左右镜像，限制硬币为20个。
- 调整四个菜单的页面逻辑；四种商品共用一个shopitem。
- 底部菜单拼页面
- init
- init init
- init
- Initial commit
- Initial commit

### 修复
- 修复员工和场地使用倒计时的逻辑问题。
- 修复详情页按钮不能点击的bug。
- 修复重置游戏后，员工倒计时异常的问题。
- 修复新手记录失败的问题、v1.0.109.
- 修复立即生产技能失效的问题；版本号：v1.0.108.
- 修复重置游戏的bug，max上限改用购买数量判断；版本v1.0.106
- 删除骰子相关代码和资源，修复报错；
- 修复领取碎片后数量异常的问题；硬币往中间靠改为 随机。
- 修复兔子显示异常的问题。
- 商品达到上线显示异常修复。
- 修复商品名称显示异常的问题。
- 修复报错空对象报错.
- 修复商品购买按钮显示异常的问题。
- 修复价格显示不全的问题。
- 修复bug。
- 修复场景设置不生效的问题。
- 修复bug
- 修复报错。
- 修复滚动条bug；
- 修复bug，设置页面增加删除数据按钮。
- 修复禁用按钮不生效的问题。
- 修复bug。
- 修复增加铜币、转铜为银、点银成金的bug。

### 移除
- 删除旧版图片。
- 删除骰子代码。
- 删除原版的部分音效。
- 替换硬币UI；删除旧资源。

