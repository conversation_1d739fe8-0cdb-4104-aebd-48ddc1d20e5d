# 🎮 LuckyCoin 游戏版本管理工具

> 一个现代化的、功能完整的游戏版本管理、构建和部署解决方案

[![npm version](https://badge.fury.io/js/version-craft.svg)](https://badge.fury.io/js/version-craft)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-4.9+-blue.svg)](https://www.typescriptlang.org/)

## ✨ 特性亮点

🚀 **一站式解决方案** - 版本管理、构建、部署全流程自动化  
🎯 **多平台支持** - Web、Android、iOS、Windows、Mac 全平台构建  
⚡ **智能化操作** - 语义化版本控制、自动标签管理、智能回滚  
🔧 **灵活配置** - JSON Schema 验证、多环境配置、热更新支持  
📊 **可视化反馈** - 彩色输出、进度显示、详细统计信息  
🛡️ **安全可靠** - 完整的错误处理、操作确认、数据备份

## 📁 项目结构

```
version-craft/
├── packages/
│   └── core/              # 🔧 核心功能包 - 共享的业务逻辑
├── cli/                   # 💻 CLI 应用 - 命令行工具
├── gui/                   # 🖥️ GUI 应用 - 桌面应用
├── src/                   # 📦 原始源码（待迁移）
└── package.json           # 📋 Monorepo 配置
```

### 架构说明

- **`packages/core`** - 核心功能包，包含所有业务逻辑，可被 CLI 和 GUI 共享使用
- **`cli/`** - 命令行应用，依赖 `@version-craft/core`
- **`gui/`** - 桌面 GUI 应用，依赖 `@version-craft/core`
- **依赖关系**: `cli` ← `packages/core` → `gui`

## 🚀 快速开始

### 安装

```bash
# 在项目目录中创建全局链接,开发环境推荐（当前使用）
npm link

# 在项目目录中
npm link

# 发布包
npm publish

# 用户安装
npm install -g version-craft

# 打包
npm pack

# 安装生成的 .tgz 文件
npm install -g version-craft-1.0.0.tgz

# 从git安装
npm install -g git+https://github.com/your-org/version-craft.git

# 取消链接
npm unlink -g version-craft

# 或者如果是通过 npm install -g 安装的
npm uninstall -g version-craft

# 全局安装（推荐）
npm install -g version-craft

# 或项目本地安装
npm install --save-dev version-craft

# 在独立的工具目录中
npm install -g .

# 然后在任何项目中使用
cd /path/to/any/project
version-craft bump patch
```

### 初始化项目

```bash
# 初始化配置文件
version-craft init

# 查看帮助
version-craft -h
```

### 基本工作流

```bash
# 1. 查看当前版本
version-craft current

# 2. 升级版本
version-craft bump patch

# 3. 构建项目
version-craft build web-mobile

# 4. 部署到测试环境
version-craft deploy-staging
```

## 📖 完整命令参考

### 🏷️ 版本管理

```bash
# 查看当前版本
version-craft current

# 升级版本
version-craft bump patch              # 修复版本 (1.0.0 → 1.0.1)
version-craft bump minor              # 功能版本 (1.0.0 → 1.1.0)  
version-craft bump major              # 重大版本 (1.0.0 → 2.0.0)
version-craft bump prerelease -p alpha  # 预发布版本 (1.0.0 → 1.0.1-alpha.0)

# 标签管理
version-craft tag v1.0.0 -m "稳定版本发布"
version-craft changelog -f v0.9.0    # 生成变更日志
version-craft list                    # 列出所有版本
```

### 🔨 构建管理

```bash
# 构建所有平台
version-craft build-all

# 构建指定平台
version-craft build web-mobile        # 构建 Web 版本
version-craft build android -s        # 构建并签名 Android APK
version-craft build ios               # 构建 iOS 版本

# 快捷构建命令
version-craft build-web               # 快速构建 Web
version-craft build-android           # 快速构建 Android
version-craft build-ios               # 快速构建 iOS

# 构建管理
version-craft build-clean             # 清理构建输出
version-craft build-stats             # 查看构建统计
```

### 🚀 部署管理

```bash
# 环境部署
version-craft deploy-staging          # 部署到测试环境
version-craft deploy-production       # 部署到生产环境
version-craft deploy staging web-mobile  # 部署指定平台到指定环境

# 部署监控
version-craft deploy-status           # 检查部署状态
version-craft deploy-history          # 查看部署历史
version-craft deploy-history -p android  # 过滤特定平台
```

### 📋 配置管理

```bash
# 配置查看和设置
version-craft config-show             # 显示当前配置
version-craft config-show -e production  # 显示生产环境配置
version-craft config-set project.name "MyGame"  # 设置配置项
version-craft config-env production   # 切换到生产环境

# 配置验证和管理
version-craft config-validate         # 验证配置文件
version-craft config-export backup.json  # 导出配置
version-craft config-import backup.json  # 导入配置
```

### ↩️ 回滚管理

```bash
# 回滚操作
version-craft rollback-list           # 列出可回滚版本
version-craft rollback-to v1.2.0     # 回滚到指定版本
version-craft rollback-last           # 回滚到上一版本
version-craft rollback-status         # 检查回滚状态
version-craft rollback-checkpoint "stable"  # 创建回滚点
```

## 🎯 帮助系统

我们提供了多层次的帮助系统，让您快速找到需要的信息：

```bash
# 快速帮助 - 显示常用命令和快速开始
version-craft
version-craft -h
version-craft --help

# 分组帮助 - 按功能分类显示命令
version-craft config-help     # 配置管理命令帮助
version-craft version-help    # 版本管理命令帮助  
version-craft build-help      # 构建管理命令帮助
version-craft deploy-help     # 部署管理命令帮助
version-craft rollback-help   # 回滚管理命令帮助

# 完整帮助 - 显示所有命令和选项
version-craft --help-all

# 具体命令帮助
version-craft build --help    # 查看 build 命令的详细帮助
```

## 📋 配置文件

项目根目录的 `version-craft.config.json` 文件包含所有配置：

```json
{
  "project": {
    "name": "LuckyCoin",
    "type": "cocos-creator",
    "version": "1.0.0",
    "description": "LuckyCoin 金币收集游戏"
  },
  "build": {
    "platforms": ["web-mobile", "android", "ios"],
    "outputDir": "./dist",
    "optimization": {
      "compress": true,
      "minify": true,
      "sourcemap": false
    },
    "android": {
      "keystore": "./android.keystore",
      "keystorePassword": "your-password",
      "keyAlias": "your-alias"
    }
  },
  "deploy": {
    "staging": {
      "web": {
        "type": "ftp",
        "host": "staging.example.com",
        "path": "/var/www/html"
      },
      "android": {
        "type": "internal-testing"
      }
    },
    "production": {
      "web": {
        "type": "static-hosting",
        "provider": "vercel"
      },
      "android": {
        "type": "google-play",
        "track": "production"
      }
    }
  },
  "notification": {
    "enabled": true,
    "webhook": "https://hooks.slack.com/your-webhook"
  }
}
```

## 🎯 快速开始指南

### 1. 初始化新项目

```bash
# 创建配置文件
version-craft init

# 根据提示选择：
# - 项目名称: LuckyCoin
# - 项目类型: cocos-creator  
# - 初始版本: 0.1.0
# - 构建平台: web-mobile, android
```

### 2. 配置构建环境

```bash
# 设置 Cocos Creator 路径（如果需要）
version-craft config-set build.cocosCreator.builderPath "/Applications/CocosCreator.app"

# 配置 Android 签名（如果需要）
version-craft config-set build.android.keystore "./android.keystore"
```

### 3. 开发工作流

```bash
# 开发完成后，升级版本
version-craft bump patch

# 构建所有平台
version-craft build-all

# 部署到测试环境
version-craft deploy-staging

# 测试通过后，部署到生产环境
version-craft deploy-production
```

## 🔧 高级功能

### 自动化脚本

在 `package.json` 中添加快捷脚本：

```json
{
  "scripts": {
    "release:patch": "version-craft bump patch && version-craft build-all && version-craft deploy-staging",
    "release:minor": "version-craft bump minor && version-craft build-all && version-craft deploy-staging", 
    "release:major": "version-craft bump major && version-craft build-all && version-craft deploy-production",
    "quick-deploy": "version-craft build-web && version-craft deploy-staging -p web-mobile"
  }
}
```

### 环境变量配置

支持通过环境变量覆盖配置：

```bash
# 设置构建输出目录
export GAME_VERSION_OUTPUT_DIR="./custom-dist"

# 设置部署环境
export GAME_VERSION_DEPLOY_ENV="staging"

# 运行命令
version-craft build-all
```

### Git Hooks 集成

在 `.git/hooks/pre-commit` 中添加：

```bash
#!/bin/sh
# 构建前验证配置
version-craft config-validate

# 检查版本一致性
version-craft current
```

## 🔍 故障排除

### 常见问题

**Q: 构建失败，提示找不到 Cocos Creator**
```bash
# 检查 Cocos Creator 路径
version-craft config-show | grep cocosCreator

# 设置正确路径
version-craft config-set build.cocosCreator.builderPath "/path/to/cocos"
```

**Q: Android 构建失败，签名错误**
```bash
# 检查签名配置
version-craft config-show | grep android

# 重新配置签名
version-craft config-set build.android.keystore "./your.keystore"
version-craft config-set build.android.keystorePassword "your-password"
```

**Q: 部署失败，连接超时**
```bash
# 检查部署状态
version-craft deploy-status

# 查看详细日志
version-craft deploy-history -e staging
```

### 调试模式

启用详细日志输出：

```bash
# 设置调试模式
export DEBUG=version-craft:*

# 运行命令查看详细日志
version-craft build web-mobile
```

## 📚 API 参考

### 编程接口

```typescript
import { GameVersionTool } from 'version-craft';

const tool = new GameVersionTool({
  configPath: './version-craft.config.json',
  logLevel: 'info'
});

// 初始化
await tool.initialize();

// 版本管理
const currentVersion = await tool.getCurrentVersion();
const newVersion = await tool.bumpVersion('patch', { createTag: true });

// 构建管理
const buildResult = await tool.buildPlatform('web-mobile', { clean: true });
const allResults = await tool.buildAllPlatforms();

// 部署管理
const deployResult = await tool.deployToEnvironment('staging', { platform: 'web-mobile' });

// 配置管理
const config = await tool.getConfig();
await tool.setConfig('project.name', 'NewName');

// 回滚管理
const versions = await tool.getAvailableVersions();
await tool.rollbackToVersion('v1.0.0');
```

### 事件监听

```typescript
// 监听构建事件
tool.on('build:start', (platform) => {
  console.log(`开始构建 ${platform}`);
});

tool.on('build:complete', (result) => {
  console.log(`构建完成: ${result.platform}, 耗时: ${result.buildTime}ms`);
});

// 监听部署事件
tool.on('deploy:start', (environment, platform) => {
  console.log(`开始部署 ${platform} 到 ${environment}`);
});

tool.on('deploy:complete', (result) => {
  console.log(`部署完成: ${result.platform} -> ${result.environment}`);
});
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 开发环境设置

```bash
# 克隆项目
git clone https://github.com/your-org/version-craft.git
cd version-craft

# 安装依赖
npm install

# 开发模式
npm run dev

# 运行测试
npm test

# 构建项目
npm run build
```

### 提交规范

我们使用 [Conventional Commits](https://conventionalcommits.org/) 规范：

```bash
# 功能添加
git commit -m "feat: 添加 iOS 构建支持"

# 问题修复  
git commit -m "fix: 修复 Android 签名问题"

# 文档更新
git commit -m "docs: 更新 API 文档"
```

### 发布流程

```bash
# 创建功能分支
git checkout -b feature/new-feature

# 开发完成后提交
git commit -m "feat: 新功能描述"

# 推送并创建 Pull Request
git push origin feature/new-feature
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

感谢所有贡献者和以下开源项目：

- [Commander.js](https://github.com/tj/commander.js) - CLI 框架
- [Inquirer.js](https://github.com/SBoudrias/Inquirer.js) - 交互式命令行
- [Chalk](https://github.com/chalk/chalk) - 终端颜色输出
- [Semver](https://github.com/npm/node-semver) - 语义化版本控制

---

<div align="center">

**🎮 让游戏发布变得简单而可靠 🚀**

[报告问题](https://github.com/your-org/version-craft/issues) • 
[功能请求](https://github.com/your-org/version-craft/issues) • 
[贡献代码](https://github.com/your-org/version-craft/pulls)

</div>
