{"project": {"name": "test-cocos-project", "type": "cocos-creator", "version": "0.3.4", "description": "完整测试的Cocos Creator项目"}, "build": {"platforms": ["web-mobile"], "outputDir": "./dist", "cocosCreator": {"projectPath": ".", "builderPath": ""}, "optimization": {"compress": true, "minify": true, "sourcemap": false}, "excludeFiles": ["*.log", "node_modules/**", ".git/**", "temp/**", "library/**"]}, "deploy": {"web": {"staging": "https://staging.example.com", "production": "https://production.example.com"}}, "environments": {"dev": "./config/dev", "test": "./config/test", "prod": "./config/prod"}, "git": {"autoTag": true, "tagPrefix": "v", "generateChangelog": true, "changelogPath": "./CHANGELOG.md"}, "notification": {"enabled": false}}