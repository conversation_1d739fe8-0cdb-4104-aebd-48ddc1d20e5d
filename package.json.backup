{"name": "version-craft", "version": "1.0.0", "description": "Version management tool for LuckyCoin game project", "main": "dist/index.js", "bin": {"version-craft": "dist/cli.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "typecheck": "tsc --noEmit", "prepare": "npm run build", "pretest": "npm run typecheck"}, "keywords": ["version-management", "cocos-creator", "game-development", "build-tool", "deployment"], "author": "LuckyCoin Team", "license": "MIT", "dependencies": {"ajv": "^8.12.0", "ajv-formats": "^2.1.1", "archiver": "^6.0.1", "axios": "^1.5.0", "chalk": "^4.1.2", "commander": "^9.5.0", "dotenv": "^16.3.1", "execa": "^5.1.1", "fs-extra": "^11.1.1", "glob": "^10.3.4", "inquirer": "^8.2.6", "link": "^2.1.1", "ora": "^5.4.1", "semver": "^7.5.4", "simple-git": "^3.20.0", "yaml": "^2.3.2"}, "devDependencies": {"@types/archiver": "^6.0.0", "@types/fs-extra": "^11.0.2", "@types/inquirer": "^9.0.3", "@types/jest": "^29.5.5", "@types/node": "^20.8.0", "@types/semver": "^7.5.3", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "engines": {"node": ">=14.0.0"}, "creator": {"version": "3.8.6"}, "uuid": "c7c8bf8b-b91e-45ca-8169-76895e4becad"}