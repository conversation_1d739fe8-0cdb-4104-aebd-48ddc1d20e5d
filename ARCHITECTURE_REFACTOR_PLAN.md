# Version-Craft 架构重构计划

## 🎯 **重构目标**

将 version-craft 从复杂的路径管理架构简化为基于 `process.cwd()` 的简洁模型，专注支持 Cocos Creator 项目，实现真正的工具独立性。

## 📋 **核心原则**

1. **简洁性优先**: 基于 `process.cwd()` 的简单模型
2. **用户体验**: 符合 git、npm 等工具的使用习惯
3. **安全性**: 严格的 Git 根目录验证
4. **专业性**: 专注 Cocos Creator 项目支持

## 🔍 **当前架构问题分析**

### **过度复杂的问题**
- ❌ 复杂的 ContextManager 和 ProjectDetector
- ❌ 向上查找项目根目录的逻辑
- ❌ 复杂的路径解析和验证
- ❌ 不必要的全局状态管理

### **用户体验问题**
- ❌ 需要 `-p` 参数指定项目路径
- ❌ 不符合现代 CLI 工具使用习惯
- ❌ 复杂的初始化流程

## 🚀 **目标架构**

### **简化后的工作流**
```bash
# 用户操作
cd /path/to/cocos-project
version-craft bump patch

# 工具逻辑
process.cwd() → Git根目录验证 → Cocos项目检测 → 执行命令
```

### **核心检测逻辑**
```typescript
async function validateWorkingDirectory(): Promise<void> {
  const cwd = process.cwd();
  
  // 1. 检查是否为工具目录
  if (isVersionCraftToolDirectory(cwd)) {
    throw new Error('不能在 version-craft 工具目录中执行命令');
  }
  
  // 2. 检查是否为 Git 仓库根目录
  const gitRoot = await getGitRoot(cwd);
  if (gitRoot !== cwd) {
    throw new Error(`请在 Git 仓库根目录执行命令\nGit 根目录: ${gitRoot}`);
  }
  
  // 3. 检查是否为 Cocos Creator 项目
  if (!isCocosCreatorProject(cwd)) {
    throw new Error('当前目录不是 Cocos Creator 项目');
  }
  
  // 4. 检查是否被 version-craft 管理
  if (!isVersionCraftManaged(cwd)) {
    await promptInitialization(cwd);
    return;
  }
}
```

## 📝 **详细实施计划**

### **阶段一: 核心检测器重构**

#### **1.1 创建简化的项目检测器**
- 文件: `src/utils/ProjectDetector.ts`
- 功能: 
  - Git 根目录验证
  - Cocos Creator 项目检测
  - version-craft 管理状态检测
  - 工具目录自我保护

#### **1.2 创建 Cocos Creator 检测器**
- 文件: `src/utils/CocosCreatorDetector.ts`
- 功能:
  - 检测 `creator.d.ts`
  - 检测 `assets/` 目录
  - 检测 `project.json`
  - 验证项目结构

#### **1.3 创建初始化助手**
- 文件: `src/utils/InitializationHelper.ts`
- 功能:
  - 友好的初始化提示
  - 自动生成配置文件
  - 项目信息收集

### **阶段二: Manager 类简化**

#### **2.1 简化 ConfigManager**
```typescript
// 移除复杂的路径解析逻辑
constructor() {
  this.projectRoot = process.cwd(); // 直接使用当前目录
  this.configPath = path.join(this.projectRoot, 'version-craft.config.json');
}
```

#### **2.2 简化 VersionManager**
```typescript
// 移除复杂的项目路径参数
constructor() {
  this.projectRoot = process.cwd();
  this.git = simpleGit(this.projectRoot);
}
```

#### **2.3 简化 BuildManager**
```typescript
// 专注 Cocos Creator 构建
constructor() {
  this.projectRoot = process.cwd();
  // 验证 Cocos Creator 环境
  this.validateCocosEnvironment();
}
```

### **阶段三: Command 类重构**

#### **3.1 移除构造函数参数**
```typescript
// 所有 Command 类
export class VersionCommand {
  constructor() {
    // 不再接受 projectRoot 参数
    this.versionManager = new VersionManager();
  }
}
```

#### **3.2 添加统一的前置检查**
```typescript
// 每个命令执行前的检查
async executeWithValidation(action: () => Promise<void>): Promise<void> {
  await validateWorkingDirectory();
  await action();
}
```

### **阶段四: CLI 入口简化**

#### **4.1 移除复杂的全局选项**
```typescript
// 移除 -p, --project 选项
// 移除 ContextManager 初始化
// 移除复杂的 hook 逻辑
```

#### **4.2 添加统一的前置验证**
```typescript
program.hook('preAction', async () => {
  await validateWorkingDirectory();
});
```

### **阶段五: 错误处理优化**

#### **5.1 友好的错误提示**
```typescript
// 不在 Git 根目录
❌ 请在 Git 仓库根目录执行命令
📍 Git 根目录: /path/to/project
💡 执行: cd /path/to/project && version-craft bump patch

// 不是 Cocos Creator 项目
❌ 当前目录不是 Cocos Creator 项目
💡 version-craft 专为 Cocos Creator 项目设计
💡 请确保目录包含: creator.d.ts, assets/, project.json

// 未被 version-craft 管理
❌ 项目未被 version-craft 管理
🤔 是否初始化 version-craft 管理？ [Y/n]
```

## 🗂️ **文件变更清单**

### **新增文件**
- `src/utils/ProjectDetector.ts` - 简化的项目检测器
- `src/utils/CocosCreatorDetector.ts` - Cocos Creator 检测器
- `src/utils/InitializationHelper.ts` - 初始化助手

### **重构文件**
- `src/cli.ts` - 简化 CLI 入口
- `src/modules/config/ConfigManager.ts` - 移除复杂路径逻辑
- `src/modules/version/VersionManager.ts` - 简化构造函数
- `src/modules/build/BuildManager.ts` - 简化构造函数
- `src/modules/deploy/DeployManager.ts` - 简化构造函数
- `src/commands/*.ts` - 所有命令类简化

### **删除文件**
- `src/utils/ContextManager.ts` - 不再需要
- `src/utils/ProjectDetector.ts` - 替换为简化版本
- `src/utils/ArchitectureValidator.ts` - 不再需要

## ⚡ **实施步骤**

### **步骤 1: 创建新的检测器**
1. 创建 `ProjectDetector.ts`
2. 创建 `CocosCreatorDetector.ts`
3. 创建 `InitializationHelper.ts`

### **步骤 2: 重构 Manager 类**
1. 简化 ConfigManager 构造函数
2. 简化 VersionManager 构造函数
3. 简化 BuildManager 构造函数
4. 简化 DeployManager 构造函数

### **步骤 3: 重构 Command 类**
1. 移除所有 Command 类的构造函数参数
2. 添加统一的验证逻辑

### **步骤 4: 简化 CLI 入口**
1. 移除复杂的全局选项
2. 添加统一的前置验证
3. 简化帮助信息

### **步骤 5: 清理和测试**
1. 删除不需要的文件
2. 更新类型定义
3. 编译测试
4. 功能测试

## 🎯 **预期效果**

### **用户体验提升**
- ✅ 符合直觉的使用方式: `cd project && version-craft bump patch`
- ✅ 清晰的错误提示和引导
- ✅ 简化的初始化流程

### **代码质量提升**
- ✅ 代码量减少 40%+
- ✅ 复杂度大幅降低
- ✅ 维护成本降低

### **架构健壮性**
- ✅ 真正的工具独立性
- ✅ 专业的 Cocos Creator 支持
- ✅ 安全的操作验证

## 📋 **验收标准**

### **功能验收**
- [ ] 在 Cocos Creator 项目根目录可以正常执行所有命令
- [ ] 在非 Git 根目录会给出清晰的错误提示
- [ ] 在非 Cocos Creator 项目会给出清晰的错误提示
- [ ] 未管理的项目会提示初始化
- [ ] 在工具目录执行会被阻止

### **编译验收**
- [ ] TypeScript 编译无错误
- [ ] 所有类型定义正确
- [ ] 构建产物正常

### **测试验收**
- [ ] 版本管理功能正常
- [ ] 构建功能正常
- [ ] 部署功能正常
- [ ] 配置管理功能正常
- [ ] 回滚功能正常
- [ ] 热更新功能正常

## 🚀 **开始实施**

准备好开始实施这个重构计划。每个阶段完成后会进行编译测试和功能验证，确保改造过程的安全性和正确性。
