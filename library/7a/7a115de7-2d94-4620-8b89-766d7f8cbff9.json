{"__type__": "cc.EffectAsset", "_name": "internal/editor/terrain-image-brush", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "techniques": [{"name": "transparent", "passes": [{"blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "program": "internal/editor/terrain-image-brush|terrain-brush-vs:vert|terrain-brush-fs:frag", "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"BrushPos": {"value": [0, 0, 0, 1], "type": 16}, "BrushParams": {"value": [2.5, 2.5, 0, 0], "type": 16}, "BrushImage": {"value": "grey", "type": 28}, "BrushDepthOffset": {"value": [0.05], "type": 13}}}]}], "shaders": [{"blocks": [{"name": "Constant", "members": [{"name": "BrushDepthOffset", "type": 13, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "TexCoords", "members": [{"name": "BrushPos", "type": 16, "count": 1}, {"name": "BrushParams", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 1}], "samplerTextures": [{"name": "BrushImage", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}], "varyings": [{"name": "wposition", "type": 16, "count": 1, "defines": [], "stageFlags": 17, "location": 0}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [{"tags": {"builtin": "local"}, "name": "CCLocal", "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": [], "stageFlags": 1}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constant", "members": [{"name": "BrushDepthOffset", "type": 13, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "TexCoords", "members": [{"name": "BrushPos", "type": 16, "count": 1}, {"name": "BrushParams", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 1}], "samplerTextures": [{"name": "BrushImage", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 849981845, "glsl4": {"vert": "\nprecision mediump float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(set = 2, binding = 0) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nlayout(location = 0) in vec3 a_position;\nlayout(location = 0) out vec4 wposition;\nlayout(set = 1, binding = 0) uniform Constant {\n    float BrushDepthOffset;\n};\nvec4 vert () {\n  vec3 worldPos;\n  worldPos.x = cc_matWorld[3][0] + a_position.x;\n  worldPos.y = cc_matWorld[3][1] + a_position.y;\n  worldPos.z = cc_matWorld[3][2] + a_position.z;\n  worldPos.y += BrushDepthOffset;\n  vec4 pos = vec4(worldPos, 1);\n  pos = cc_matViewProj * pos;\n  wposition = vec4(worldPos, 1);\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nlayout(location = 0) in vec4 wposition;\nlayout(set = 1, binding = 1) uniform TexCoords {\n  vec4 BrushPos;\n  vec4 BrushParams;\n};\nlayout(set = 1, binding = 2) uniform sampler2D BrushImage;\nvec4 frag () {\n  float Radius = BrushParams.x;\n  float Falloff = BrushParams.y;\n  float Rotation = BrushParams.z;\n  float DeltaU = BrushPos.x - wposition.x;\n  float DeltaV = BrushPos.z - wposition.z;\n  if (Rotation != 0.0) {\n    float sine = sin(Rotation);\n    float cosine = cos(Rotation);\n    float _11 = cosine, _21 = sine;\n    float _12 = -sine, _22 = cosine;\n    float tmpx = DeltaU * _11 + DeltaV * _21;\n    float tmpz = DeltaU * _12 + DeltaV * _22;\n    DeltaU = tmpx;\n    DeltaV = tmpz;\n  }\n  float k = 0.0;\n  if (abs(DeltaU) < Radius && abs(DeltaV) < Radius) {\n    float u = DeltaU / Radius * 0.5 + 0.5;\n    float v = DeltaV / Radius * 0.5 + 0.5;\n    k = texture(BrushImage, vec2(u, v)).r;\n  }\n  vec4 color = vec4(0, 0, 0, 0);\n  color.rgb = vec3(100, 100, 135) / 255.0;\n  color.a = 0.85 * k;\n  return CCFragOutput(color);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl3": {"vert": "\nprecision mediump float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(std140) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nin vec3 a_position;\nout vec4 wposition;\nlayout(std140) uniform Constant {\n    float BrushDepthOffset;\n};\nvec4 vert () {\n  vec3 worldPos;\n  worldPos.x = cc_matWorld[3][0] + a_position.x;\n  worldPos.y = cc_matWorld[3][1] + a_position.y;\n  worldPos.z = cc_matWorld[3][2] + a_position.z;\n  worldPos.y += BrushDepthOffset;\n  vec4 pos = vec4(worldPos, 1);\n  pos = cc_matViewProj * pos;\n  wposition = vec4(worldPos, 1);\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nin vec4 wposition;\nlayout(std140) uniform TexCoords {\n  vec4 BrushPos;\n  vec4 BrushParams;\n};\nuniform sampler2D BrushImage;\nvec4 frag () {\n  float Radius = BrushParams.x;\n  float Falloff = BrushParams.y;\n  float Rotation = BrushParams.z;\n  float DeltaU = BrushPos.x - wposition.x;\n  float DeltaV = BrushPos.z - wposition.z;\n  if (Rotation != 0.0) {\n    float sine = sin(Rotation);\n    float cosine = cos(Rotation);\n    float _11 = cosine, _21 = sine;\n    float _12 = -sine, _22 = cosine;\n    float tmpx = DeltaU * _11 + DeltaV * _21;\n    float tmpz = DeltaU * _12 + DeltaV * _22;\n    DeltaU = tmpx;\n    DeltaV = tmpz;\n  }\n  float k = 0.0;\n  if (abs(DeltaU) < Radius && abs(DeltaV) < Radius) {\n    float u = DeltaU / Radius * 0.5 + 0.5;\n    float v = DeltaV / Radius * 0.5 + 0.5;\n    k = texture(BrushImage, vec2(u, v)).r;\n  }\n  vec4 color = vec4(0, 0, 0, 0);\n  color.rgb = vec3(100, 100, 135) / 255.0;\n  color.a = 0.85 * k;\n  return CCFragOutput(color);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision mediump float;\nuniform highp mat4 cc_matViewProj;\nuniform highp mat4 cc_matWorld;\nattribute vec3 a_position;\nvarying vec4 wposition;\n   uniform float BrushDepthOffset;\nvec4 vert () {\n  vec3 worldPos;\n  worldPos.x = cc_matWorld[3][0] + a_position.x;\n  worldPos.y = cc_matWorld[3][1] + a_position.y;\n  worldPos.z = cc_matWorld[3][2] + a_position.z;\n  worldPos.y += BrushDepthOffset;\n  vec4 pos = vec4(worldPos, 1);\n  pos = cc_matViewProj * pos;\n  wposition = vec4(worldPos, 1);\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nvarying vec4 wposition;\n   uniform vec4 BrushPos;\n   uniform vec4 BrushParams;\nuniform sampler2D BrushImage;\nvec4 frag () {\n  float Radius = BrushParams.x;\n  float Falloff = BrushParams.y;\n  float Rotation = BrushParams.z;\n  float DeltaU = BrushPos.x - wposition.x;\n  float DeltaV = BrushPos.z - wposition.z;\n  if (Rotation != 0.0) {\n    float sine = sin(Rotation);\n    float cosine = cos(Rotation);\n    float _11 = cosine, _21 = sine;\n    float _12 = -sine, _22 = cosine;\n    float tmpx = DeltaU * _11 + DeltaV * _21;\n    float tmpz = DeltaU * _12 + DeltaV * _22;\n    DeltaU = tmpx;\n    DeltaV = tmpz;\n  }\n  float k = 0.0;\n  if (abs(DeltaU) < Radius && abs(DeltaV) < Radius) {\n    float u = DeltaU / Radius * 0.5 + 0.5;\n    float v = DeltaV / Radius * 0.5 + 0.5;\n    k = texture2D(BrushImage, vec2(u, v)).r;\n  }\n  vec4 color = vec4(0, 0, 0, 0);\n  color.rgb = vec3(100, 100, 135) / 255.0;\n  color.a = 0.85 * k;\n  return CCFragOutput(color);\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 57, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 44}}, "defines": [], "name": "internal/editor/terrain-image-brush|terrain-brush-vs:vert|terrain-brush-fs:frag"}], "combinations": [], "hideInEditor": true}