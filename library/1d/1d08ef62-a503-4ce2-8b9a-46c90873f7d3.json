{"__type__": "cc.EffectAsset", "_name": "legacy/terrain", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "techniques": [{"name": "opaque", "passes": [{"program": "legacy/terrain|terrain-vs|terrain-fs", "properties": {"UVScale": {"value": [1, 1, 1, 1], "type": 16}, "metallic": {"value": [0, 0, 0, 0], "type": 16}, "roughness": {"value": [1, 1, 1, 1], "type": 16}, "weightMap": {"value": "black", "type": 28}, "detailMap0": {"value": "grey", "type": 28}, "detailMap1": {"value": "grey", "type": 28}, "detailMap2": {"value": "grey", "type": 28}, "detailMap3": {"value": "grey", "type": 28}, "normalMap0": {"value": "normal", "type": 28}, "normalMap1": {"value": "normal", "type": 28}, "normalMap2": {"value": "normal", "type": 28}, "normalMap3": {"value": "normal", "type": 28}}}, {"phase": "forward-add", "propertyIndex": 0, "embeddedMacros": {"CC_FORWARD_ADD": true}, "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 1, "blendSrcAlpha": 0, "blendDstAlpha": 1}]}, "program": "legacy/terrain|terrain-vs|terrain-fs", "depthStencilState": {"depthFunc": 2, "depthTest": true, "depthWrite": false}, "properties": {"UVScale": {"value": [1, 1, 1, 1], "type": 16}, "metallic": {"value": [0, 0, 0, 0], "type": 16}, "roughness": {"value": [1, 1, 1, 1], "type": 16}, "weightMap": {"value": "black", "type": 28}, "detailMap0": {"value": "grey", "type": 28}, "detailMap1": {"value": "grey", "type": 28}, "detailMap2": {"value": "grey", "type": 28}, "detailMap3": {"value": "grey", "type": 28}, "normalMap0": {"value": "normal", "type": 28}, "normalMap1": {"value": "normal", "type": 28}, "normalMap2": {"value": "normal", "type": 28}, "normalMap3": {"value": "normal", "type": 28}}}, {"phase": "shadow-add", "propertyIndex": 0, "rasterizerState": {"cullMode": 2}, "program": "legacy/terrain|shadow-caster-vs:vert|shadow-caster-fs:frag"}, {"phase": "deferred-forward", "propertyIndex": 0, "program": "legacy/terrain|terrain-vs|terrain-fs"}]}], "shaders": [{"blocks": [{"name": "TexCoords", "members": [{"name": "UVScale", "type": 16, "count": 1}, {"name": "lightMapUVParam", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "PbrParams", "members": [{"name": "metallic", "type": 16, "count": 1}, {"name": "roughness", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 1}], "samplerTextures": [{"name": "weightMap", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}, {"name": "detailMap0", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 3}, {"name": "detailMap1", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 4}, {"name": "detailMap2", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 5}, {"name": "detailMap3", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 6}, {"name": "normalMap0", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 7}, {"name": "normalMap1", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 8}, {"name": "normalMap2", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 9}, {"name": "normalMap3", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 10}], "samplers": [], "textures": [], "buffers": [{"name": "b_ccLightsBuffer", "memoryAccess": 1, "defines": ["CC_FORWARD_ADD", "CC_ENABLE_CLUSTERED_LIGHT_CULLING"], "stageFlags": 16, "binding": 11}, {"name": "b_clusterLightIndicesBuffer", "memoryAccess": 1, "defines": ["CC_FORWARD_ADD", "CC_ENABLE_CLUSTERED_LIGHT_CULLING"], "stageFlags": 16, "binding": 12}, {"name": "b_clusterLightGridBuffer", "memoryAccess": 1, "defines": ["CC_FORWARD_ADD", "CC_ENABLE_CLUSTERED_LIGHT_CULLING"], "stageFlags": 16, "binding": 13}], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_normal", "defines": [], "format": 32, "location": 1}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 2}], "varyings": [{"name": "v_fog_factor", "type": 13, "count": 1, "defines": ["!CC_USE_ACCURATE_FOG"], "stageFlags": 17, "location": 0}, {"name": "v_shadowPos", "type": 16, "count": 1, "defines": [], "stageFlags": 17, "location": 1}, {"name": "v_shadowBias", "type": 14, "count": 1, "defines": ["CC_RECEIVE_SHADOW"], "stageFlags": 17, "location": 2}, {"name": "v_position", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 3}, {"name": "v_normal", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 4}, {"name": "uvw", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 5}, {"name": "uv0", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 6}, {"name": "uv1", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 7}, {"name": "uv2", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 8}, {"name": "uv3", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 9}, {"name": "luv", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 10}, {"name": "diffuse", "type": 15, "count": 1, "defines": [], "stageFlags": 17, "location": 11}, {"name": "v_sh_linear_const_r", "type": 16, "count": 1, "defines": ["CC_USE_LIGHT_PROBE", "USE_INSTANCING"], "stageFlags": 16, "location": 12}, {"name": "v_sh_linear_const_g", "type": 16, "count": 1, "defines": ["CC_USE_LIGHT_PROBE", "USE_INSTANCING"], "stageFlags": 16, "location": 13}, {"name": "v_sh_linear_const_b", "type": 16, "count": 1, "defines": ["CC_USE_LIGHT_PROBE", "USE_INSTANCING"], "stageFlags": 16, "location": 14}, {"name": "v_luv", "type": 15, "count": 1, "defines": ["CC_USE_LIGHTMAP", "!CC_FORWARD_ADD"], "stageFlags": 16, "location": 15}], "fragColors": [{"name": "fragColorX", "typename": "vec4", "type": 16, "count": 1, "defines": ["CC_FORWARD_ADD"], "stageFlags": 16, "location": 0}, {"name": "albedoOut", "typename": "vec4", "type": 16, "count": 1, "defines": ["CC_FORWARD_ADD", "CC_PIPELINE_TYPE"], "stageFlags": 16, "location": 1}, {"name": "emissiveOut", "typename": "vec4", "type": 16, "count": 1, "defines": ["CC_FORWARD_ADD", "CC_PIPELINE_TYPE"], "stageFlags": 16, "location": 2}, {"name": "normalOut", "typename": "vec4", "type": 16, "count": 1, "defines": ["CC_FORWARD_ADD", "CC_PIPELINE_TYPE"], "stageFlags": 16, "location": 3}], "descriptors": [{"rate": 0, "blocks": [{"tags": {"builtin": "local"}, "name": "CCLocal", "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "local"}, "name": "CCSH", "members": [{"name": "cc_sh_linear_const_r", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_g", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_b", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_r", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_g", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_b", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_a", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_LIGHT_PROBE", "!USE_INSTANCING"], "stageFlags": 16}, {"tags": {"builtin": "local"}, "name": "CCForwardLight", "members": [{"name": "cc_lightPos", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}, {"name": "cc_lightColor", "typename": "vec4", "type": 16, "count": 0, "isArray": true}, {"name": "cc_lightSizeRangeAngle", "typename": "vec4", "type": 16, "count": 0, "isArray": true}, {"name": "cc_lightDir", "typename": "vec4", "type": 16, "count": 0, "isArray": true}, {"name": "cc_lightBoundingSizeVS", "typename": "vec4", "type": 16, "count": 0, "isArray": true}], "defines": ["CC_FORWARD_ADD", "CC_ENABLE_CLUSTERED_LIGHT_CULLING"], "stageFlags": 16}], "samplerTextures": [{"tags": {"builtin": "local"}, "name": "cc_reflectionProbeCubemap", "typename": "samplerCube", "type": 31, "count": 1, "defines": ["CC_USE_REFLECTION_PROBE"], "stageFlags": 16, "sampleType": 0}, {"tags": {"builtin": "local"}, "name": "cc_reflectionProbePlanarMap", "typename": "sampler2D", "type": 28, "count": 1, "defines": ["CC_USE_REFLECTION_PROBE"], "stageFlags": 16, "sampleType": 0}, {"tags": {"builtin": "local"}, "name": "cc_reflectionProbeDataMap", "typename": "sampler2D", "type": 28, "count": 1, "defines": ["CC_USE_REFLECTION_PROBE"], "stageFlags": 16, "sampleType": 0}, {"tags": {"builtin": "local"}, "name": "cc_lightingMap", "typename": "sampler2D", "type": 28, "count": 1, "defines": ["CC_USE_LIGHTMAP", "!CC_FORWARD_ADD"], "stageFlags": 16, "sampleType": 0}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "TexCoords", "members": [{"name": "UVScale", "type": 16, "count": 1}, {"name": "lightMapUVParam", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "PbrParams", "members": [{"name": "metallic", "type": 16, "count": 1}, {"name": "roughness", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 1}], "samplerTextures": [{"name": "weightMap", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}, {"name": "detailMap0", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 3}, {"name": "detailMap1", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 4}, {"name": "detailMap2", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 5}, {"name": "detailMap3", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 6}, {"name": "normalMap0", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 7}, {"name": "normalMap1", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 8}, {"name": "normalMap2", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 9}, {"name": "normalMap3", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 10}], "samplers": [], "textures": [], "buffers": [{"name": "b_ccLightsBuffer", "memoryAccess": 1, "defines": ["CC_FORWARD_ADD", "CC_ENABLE_CLUSTERED_LIGHT_CULLING"], "stageFlags": 16, "binding": 11}, {"name": "b_clusterLightIndicesBuffer", "memoryAccess": 1, "defines": ["CC_FORWARD_ADD", "CC_ENABLE_CLUSTERED_LIGHT_CULLING"], "stageFlags": 16, "binding": 12}, {"name": "b_clusterLightGridBuffer", "memoryAccess": 1, "defines": ["CC_FORWARD_ADD", "CC_ENABLE_CLUSTERED_LIGHT_CULLING"], "stageFlags": 16, "binding": 13}], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCShadow", "members": [{"name": "cc_matLightView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matLightViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_shadowInvProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowNFLSInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowWHPBInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowLPNNInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowColor", "typename": "vec4", "type": 16, "count": 1, "precision": "lowp "}, {"name": "cc_planarNDInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCSM", "members": [{"name": "cc_csmViewDir0", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmViewDir1", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmViewDir2", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmAtlas", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_matCSMViewProj", "typename": "mat4", "type": 25, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmProjDepthInfo", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmProjInfo", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmSplitsInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_SUPPORT_CASCADED_SHADOW_MAP"], "stageFlags": 17}], "samplerTextures": [{"tags": {"builtin": "global"}, "name": "cc_shadowMap", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "defines": ["CC_RECEIVE_SHADOW"], "stageFlags": 17, "sampleType": 0}, {"tags": {"builtin": "global"}, "name": "cc_spotShadowMap", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "defines": ["CC_RECEIVE_SHADOW"], "stageFlags": 17, "sampleType": 0}, {"tags": {"builtin": "global"}, "name": "cc_environment", "typename": "samplerCube", "type": 31, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0}, {"tags": {"builtin": "global"}, "name": "cc_diffuseMap", "typename": "samplerCube", "type": 31, "count": 1, "defines": ["CC_USE_IBL", "CC_USE_DIFFUSEMAP"], "stageFlags": 16, "sampleType": 0}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 1013815347, "glsl4": {"vert": "\nprecision mediump float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(set = 2, binding = 0) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\n#if !CC_USE_ACCURATE_FOG\nlayout(location = 0) out mediump float v_fog_factor;\n#endif\nvoid CC_TRANSFER_FOG(vec4 pos) {\n#if !CC_USE_ACCURATE_FOG\n    CC_TRANSFER_FOG_BASE(pos, v_fog_factor);\n#endif\n}\nlayout(location = 1) out highp vec4 v_shadowPos;\nlayout(set = 0, binding = 2) uniform CCShadow {\n  highp mat4 cc_matLightView;\n  highp mat4 cc_matLightViewProj;\n  highp vec4 cc_shadowInvProjDepthInfo;\n  highp vec4 cc_shadowProjDepthInfo;\n  highp vec4 cc_shadowProjInfo;\n  mediump vec4 cc_shadowNFLSInfo;\n  mediump vec4 cc_shadowWHPBInfo;\n  mediump vec4 cc_shadowLPNNInfo;\n  lowp vec4 cc_shadowColor;\n  mediump vec4 cc_planarNDInfo;\n};\n#if CC_SUPPORT_CASCADED_SHADOW_MAP\n  layout(set = 0, binding = 3) uniform CCCSM {\n    highp vec4 cc_csmViewDir0[4];\n    highp vec4 cc_csmViewDir1[4];\n    highp vec4 cc_csmViewDir2[4];\n    highp vec4 cc_csmAtlas[4];\n    highp mat4 cc_matCSMViewProj[4];\n    highp vec4 cc_csmProjDepthInfo[4];\n    highp vec4 cc_csmProjInfo[4];\n    highp vec4 cc_csmSplitsInfo;\n  };\n#endif\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\n#if CC_RECEIVE_SHADOW\n  layout(set = 0, binding = 4) uniform highp sampler2D cc_shadowMap;\n  layout(set = 0, binding = 6) uniform highp sampler2D cc_spotShadowMap;\n    #define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\n  #if CC_SUPPORT_CASCADED_SHADOW_MAP\n  #else\n  #endif\n#endif\n#if CC_RECEIVE_SHADOW\n#endif\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec3 a_normal;\nlayout(location = 2) in vec2 a_texCoord;\n#if CC_RECEIVE_SHADOW\n  layout(location = 2) out vec2 v_shadowBias;\n#endif\nlayout(location = 3) out highp vec3 v_position;\nlayout(location = 4) out mediump vec3 v_normal;\nlayout(location = 5) out mediump vec2 uvw;\nlayout(location = 6) out mediump vec2 uv0;\nlayout(location = 7) out mediump vec2 uv1;\nlayout(location = 8) out mediump vec2 uv2;\nlayout(location = 9) out mediump vec2 uv3;\nlayout(location = 10) out mediump vec3 luv;\nlayout(location = 11) out mediump vec3 diffuse;\nlayout(set = 1, binding = 0) uniform TexCoords {\n  vec4 UVScale;\n  vec4 lightMapUVParam;\n};\nvoid main () {\n  vec3 worldPos;\n  worldPos.x = cc_matWorld[3][0] + a_position.x;\n  worldPos.y = cc_matWorld[3][1] + a_position.y;\n  worldPos.z = cc_matWorld[3][2] + a_position.z;\n  vec4 pos = vec4(worldPos, 1.0);\n  pos = cc_matViewProj * pos;\n  uvw = a_texCoord;\n  uv0 = a_position.xz * UVScale.x;\n  uv1 = a_position.xz * UVScale.y;\n  uv2 = a_position.xz * UVScale.z;\n  uv3 = a_position.xz * UVScale.w;\n  #if CC_USE_LIGHTMAP\n    luv.xy = cc_lightingMapUVParam.xy + a_texCoord * cc_lightingMapUVParam.z;\n    luv.z = cc_lightingMapUVParam.w;\n  #endif\n  v_position = worldPos;\n  v_normal = a_normal;\n  CC_TRANSFER_FOG(vec4(worldPos, 1.0));\n  #if CC_RECEIVE_SHADOW\n    v_shadowBias = vec2(0.0, 0.0);\n  #endif\n  v_shadowPos = cc_matLightViewProj * vec4(worldPos, 1.0);\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nvec3 SRGBToLinear (vec3 gamma) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return gamma;\n    }\n  #endif\n#endif\n  return gamma * gamma;\n}\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nlayout(set = 0, binding = 2) uniform CCShadow {\n  highp mat4 cc_matLightView;\n  highp mat4 cc_matLightViewProj;\n  highp vec4 cc_shadowInvProjDepthInfo;\n  highp vec4 cc_shadowProjDepthInfo;\n  highp vec4 cc_shadowProjInfo;\n  mediump vec4 cc_shadowNFLSInfo;\n  mediump vec4 cc_shadowWHPBInfo;\n  mediump vec4 cc_shadowLPNNInfo;\n  lowp vec4 cc_shadowColor;\n  mediump vec4 cc_planarNDInfo;\n};\n#if CC_SUPPORT_CASCADED_SHADOW_MAP\n  layout(set = 0, binding = 3) uniform CCCSM {\n    highp vec4 cc_csmViewDir0[4];\n    highp vec4 cc_csmViewDir1[4];\n    highp vec4 cc_csmViewDir2[4];\n    highp vec4 cc_csmAtlas[4];\n    highp mat4 cc_matCSMViewProj[4];\n    highp vec4 cc_csmProjDepthInfo[4];\n    highp vec4 cc_csmProjInfo[4];\n    highp vec4 cc_csmSplitsInfo;\n  };\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nvec2 GetPlanarReflectScreenUV(vec3 worldPos, mat4 matVirtualCameraViewProj, float flipNDCSign, vec3 viewDir, vec3 reflectDir)\n{\n  vec4 clipPos = matVirtualCameraViewProj * vec4(worldPos, 1.0);\n  vec2 screenUV = clipPos.xy / clipPos.w * 0.5 + 0.5;\n  screenUV = vec2(1.0 - screenUV.x, screenUV.y);\n  screenUV = flipNDCSign == 1.0 ? vec2(screenUV.x, 1.0 - screenUV.y) : screenUV;\n  return screenUV;\n}\nfloat GetLinearDepthFromViewSpace(vec3 viewPos, float near, float far) {\n  float dist = length(viewPos);\n  return (dist - near) / (far - near);\n}\nvec3 RotationVecFromAxisY(vec3 v, float cosTheta, float sinTheta)\n{\n    vec3 result;\n    result.x = dot(v, vec3(cosTheta, 0.0, -sinTheta));\n    result.y = v.y;\n    result.z = dot(v, vec3(sinTheta, 0.0,  cosTheta));\n    return result;\n}\nvec3 RotationVecFromAxisY(vec3 v, float rotateAngleArc)\n{\n  return RotationVecFromAxisY(v, cos(rotateAngleArc), sin(rotateAngleArc));\n}\nfloat CCGetLinearDepth(vec3 worldPos, float viewSpaceBias) {\n\tvec4 viewPos = cc_matLightView * vec4(worldPos.xyz, 1.0);\n  viewPos.z += viewSpaceBias;\n\treturn GetLinearDepthFromViewSpace(viewPos.xyz, cc_shadowNFLSInfo.x, cc_shadowNFLSInfo.y);\n}\nfloat CCGetLinearDepth(vec3 worldPos) {\n\treturn CCGetLinearDepth(worldPos, 0.0);\n}\n#if CC_RECEIVE_SHADOW\n  layout(set = 0, binding = 4) uniform highp sampler2D cc_shadowMap;\n  layout(set = 0, binding = 6) uniform highp sampler2D cc_spotShadowMap;\n    #define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\n    highp float unpackHighpData (float mainPart, float modPart) {\n      highp float data = mainPart;\n      return data + modPart;\n    }\n    void packHighpData (out float mainPart, out float modPart, highp float data) {\n      mainPart = fract(data);\n      modPart = data - mainPart;\n    }\n    highp float unpackHighpData (float mainPart, float modPart, const float modValue) {\n      highp float data = mainPart * modValue;\n      return data + modPart * modValue;\n    }\n    void packHighpData (out float mainPart, out float modPart, highp float data, const float modValue) {\n      highp float divide = data / modValue;\n      mainPart = floor(divide);\n      modPart = (data - mainPart * modValue) / modValue;\n    }\n    highp vec2 unpackHighpData (vec2 mainPart, vec2 modPart) {\n      highp vec2 data = mainPart;\n      return data + modPart;\n    }\n    void packHighpData (out vec2 mainPart, out vec2 modPart, highp vec2 data) {\n      mainPart = fract(data);\n      modPart = data - mainPart;\n    }\n    highp vec2 unpackHighpData (vec2 mainPart, vec2 modPart, const float modValue) {\n      highp vec2 data = mainPart * modValue;\n      return data + modPart * modValue;\n    }\n    void packHighpData (out vec2 mainPart, out vec2 modPart, highp vec2 data, const float modValue) {\n      highp vec2 divide = data / modValue;\n      mainPart = floor(divide);\n      modPart = (data - mainPart * modValue) / modValue;\n    }\n    highp vec3 unpackHighpData (vec3 mainPart, vec3 modPart) {\n      highp vec3 data = mainPart;\n      return data + modPart;\n    }\n    void packHighpData (out vec3 mainPart, out vec3 modPart, highp vec3 data) {\n      mainPart = fract(data);\n      modPart = data - mainPart;\n    }\n    highp vec3 unpackHighpData (vec3 mainPart, vec3 modPart, const float modValue) {\n      highp vec3 data = mainPart * modValue;\n      return data + modPart * modValue;\n    }\n    void packHighpData (out vec3 mainPart, out vec3 modPart, highp vec3 data, const float modValue) {\n      highp vec3 divide = data / modValue;\n      mainPart = floor(divide);\n      modPart = (data - mainPart * modValue) / modValue;\n    }\n    highp vec4 unpackHighpData (vec4 mainPart, vec4 modPart) {\n      highp vec4 data = mainPart;\n      return data + modPart;\n    }\n    void packHighpData (out vec4 mainPart, out vec4 modPart, highp vec4 data) {\n      mainPart = fract(data);\n      modPart = data - mainPart;\n    }\n    highp vec4 unpackHighpData (vec4 mainPart, vec4 modPart, const float modValue) {\n      highp vec4 data = mainPart * modValue;\n      return data + modPart * modValue;\n    }\n    void packHighpData (out vec4 mainPart, out vec4 modPart, highp vec4 data, const float modValue) {\n      highp vec4 divide = data / modValue;\n      mainPart = floor(divide);\n      modPart = (data - mainPart * modValue) / modValue;\n    }\n  vec4 shadowTexure(highp sampler2D shadowMap, vec2 coord) {\n      #if defined(CC_USE_WGPU)\n          return textureLod(shadowMap, coord, 0.0);\n      #else\n        return texture(shadowMap, coord);\n      #endif\n  }\n  float NativePCFShadowFactorHard (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    #if CC_SHADOWMAP_FORMAT == 1\n      return step(shadowNDCPos.z, dot(shadowTexure(shadowMap, shadowNDCPos.xy), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      return step(shadowNDCPos.z, shadowTexure(shadowMap, shadowNDCPos.xy).x);\n    #endif\n  }\n  float NativePCFShadowFactorSoft (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    vec2 shadowNDCPos_offset = shadowNDCPos.xy + oneTap;\n    float block0, block1, block2, block3;\n    #if CC_SHADOWMAP_FORMAT == 1\n      block0 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos_offset.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      block0 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)).x);\n      block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos.y)).x);\n      block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset.y)).x);\n      block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos_offset.y)).x);\n    #endif\n    float coefX   = mod(shadowNDCPos.x, oneTap.x) * shadowMapResolution.x;\n    float resultX = mix(block0, block1, coefX);\n    float resultY = mix(block2, block3, coefX);\n    float coefY   = mod(shadowNDCPos.y, oneTap.y) * shadowMapResolution.y;\n    return mix(resultX, resultY, coefY);\n  }\n  float NativePCFShadowFactorSoft3X (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    float shadowNDCPos_offset_L = shadowNDCPos.x - oneTap.x;\n    float shadowNDCPos_offset_R = shadowNDCPos.x + oneTap.x;\n    float shadowNDCPos_offset_U = shadowNDCPos.y - oneTap.y;\n    float shadowNDCPos_offset_D = shadowNDCPos.y + oneTap.y;\n    float block0, block1, block2, block3, block4, block5, block6, block7, block8;\n    #if CC_SHADOWMAP_FORMAT == 1\n      block0 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block4 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block5 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block6 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block7 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block8 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      block0 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_U)).x);\n      block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_U)).x);\n      block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_U)).x);\n      block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos.y)).x);\n      block4 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)).x);\n      block5 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos.y)).x);\n      block6 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_D)).x);\n      block7 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_D)).x);\n      block8 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_D)).x);\n    #endif\n    float coefX = mod(shadowNDCPos.x, oneTap.x) * shadowMapResolution.x;\n    float coefY = mod(shadowNDCPos.y, oneTap.y) * shadowMapResolution.y;\n    float shadow = 0.0;\n    float resultX = mix(block0, block1, coefX);\n    float resultY = mix(block3, block4, coefX);\n    shadow += mix(resultX , resultY, coefY);\n    resultX = mix(block1, block2, coefX);\n    resultY = mix(block4, block5, coefX);\n    shadow += mix(resultX , resultY, coefY);\n    resultX = mix(block3, block4, coefX);\n    resultY = mix(block6, block7, coefX);\n    shadow += mix(resultX, resultY, coefY);\n    resultX = mix(block4, block5, coefX);\n    resultY = mix(block7, block8, coefX);\n    shadow += mix(resultX, resultY, coefY);\n    return shadow * 0.25;\n  }\n  float NativePCFShadowFactorSoft5X (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    vec2 twoTap = oneTap * 2.0;\n    vec2 offset1 = shadowNDCPos.xy + vec2(-twoTap.x, -twoTap.y);\n    vec2 offset2 = shadowNDCPos.xy + vec2(-oneTap.x, -twoTap.y);\n    vec2 offset3 = shadowNDCPos.xy + vec2(0.0, -twoTap.y);\n    vec2 offset4 = shadowNDCPos.xy + vec2(oneTap.x, -twoTap.y);\n    vec2 offset5 = shadowNDCPos.xy + vec2(twoTap.x, -twoTap.y);\n    vec2 offset6 = shadowNDCPos.xy + vec2(-twoTap.x, -oneTap.y);\n    vec2 offset7 = shadowNDCPos.xy + vec2(-oneTap.x, -oneTap.y);\n    vec2 offset8 = shadowNDCPos.xy + vec2(0.0, -oneTap.y);\n    vec2 offset9 = shadowNDCPos.xy + vec2(oneTap.x, -oneTap.y);\n    vec2 offset10 = shadowNDCPos.xy + vec2(twoTap.x, -oneTap.y);\n    vec2 offset11 = shadowNDCPos.xy + vec2(-twoTap.x, 0.0);\n    vec2 offset12 = shadowNDCPos.xy + vec2(-oneTap.x, 0.0);\n    vec2 offset13 = shadowNDCPos.xy + vec2(0.0, 0.0);\n    vec2 offset14 = shadowNDCPos.xy + vec2(oneTap.x, 0.0);\n    vec2 offset15 = shadowNDCPos.xy + vec2(twoTap.x, 0.0);\n    vec2 offset16 = shadowNDCPos.xy + vec2(-twoTap.x, oneTap.y);\n    vec2 offset17 = shadowNDCPos.xy + vec2(-oneTap.x, oneTap.y);\n    vec2 offset18 = shadowNDCPos.xy + vec2(0.0, oneTap.y);\n    vec2 offset19 = shadowNDCPos.xy + vec2(oneTap.x, oneTap.y);\n    vec2 offset20 = shadowNDCPos.xy + vec2(twoTap.x, oneTap.y);\n    vec2 offset21 = shadowNDCPos.xy + vec2(-twoTap.x, twoTap.y);\n    vec2 offset22 = shadowNDCPos.xy + vec2(-oneTap.x, twoTap.y);\n    vec2 offset23 = shadowNDCPos.xy + vec2(0.0, twoTap.y);\n    vec2 offset24 = shadowNDCPos.xy + vec2(oneTap.x, twoTap.y);\n    vec2 offset25 = shadowNDCPos.xy + vec2(twoTap.x, twoTap.y);\n    float block1, block2, block3, block4, block5, block6, block7, block8, block9, block10, block11, block12, block13, block14, block15, block16, block17, block18, block19, block20, block21, block22, block23, block24, block25;\n    #if CC_SHADOWMAP_FORMAT == 1\n      block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset1), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset2), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset3), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block4 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset4), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block5 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset5), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block6 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset6), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block7 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset7), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block8 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset8), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block9 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset9), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block10 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset10), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block11 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset11), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block12 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset12), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block13 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset13), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block14 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset14), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block15 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset15), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block16 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset16), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block17 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset17), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block18 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset18), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block19 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset19), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block20 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset20), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block21 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset21), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block22 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset22), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block23 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset23), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block24 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset24), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block25 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset25), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset1).x);\n      block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset2).x);\n      block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset3).x);\n      block4 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset4).x);\n      block5 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset5).x);\n      block6 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset6).x);\n      block7 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset7).x);\n      block8 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset8).x);\n      block9 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset9).x);\n      block10 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset10).x);\n      block11 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset11).x);\n      block12 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset12).x);\n      block13 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset13).x);\n      block14 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset14).x);\n      block15 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset15).x);\n      block16 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset16).x);\n      block17 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset17).x);\n      block18 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset18).x);\n      block19 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset19).x);\n      block20 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset20).x);\n      block21 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset21).x);\n      block22 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset22).x);\n      block23 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset23).x);\n      block24 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset24).x);\n      block25 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset25).x);\n    #endif\n    vec2 coef = fract(shadowNDCPos.xy * shadowMapResolution);\n    vec2 v1X1 = mix(vec2(block1, block6), vec2(block2, block7), coef.xx);\n    vec2 v1X2 = mix(vec2(block2, block7), vec2(block3, block8), coef.xx);\n    vec2 v1X3 = mix(vec2(block3, block8), vec2(block4, block9), coef.xx);\n    vec2 v1X4 = mix(vec2(block4, block9), vec2(block5, block10), coef.xx);\n    float v1 = mix(v1X1.x, v1X1.y, coef.y) + mix(v1X2.x, v1X2.y, coef.y) + mix(v1X3.x, v1X3.y, coef.y) + mix(v1X4.x, v1X4.y, coef.y);\n    vec2 v2X1 = mix(vec2(block6, block11), vec2(block7, block12), coef.xx);\n    vec2 v2X2 = mix(vec2(block7, block12), vec2(block8, block13), coef.xx);\n    vec2 v2X3 = mix(vec2(block8, block13), vec2(block9, block14), coef.xx);\n    vec2 v2X4 = mix(vec2(block9, block14), vec2(block10, block15), coef.xx);\n    float v2 = mix(v2X1.x, v2X1.y, coef.y) + mix(v2X2.x, v2X2.y, coef.y) + mix(v2X3.x, v2X3.y, coef.y) + mix(v2X4.x, v2X4.y, coef.y);\n    vec2 v3X1 = mix(vec2(block11, block16), vec2(block12, block17), coef.xx);\n    vec2 v3X2 = mix(vec2(block12, block17), vec2(block13, block18), coef.xx);\n    vec2 v3X3 = mix(vec2(block13, block18), vec2(block14, block19), coef.xx);\n    vec2 v3X4 = mix(vec2(block14, block19), vec2(block15, block20), coef.xx);\n    float v3 = mix(v3X1.x, v3X1.y, coef.y) + mix(v3X2.x, v3X2.y, coef.y) + mix(v3X3.x, v3X3.y, coef.y) + mix(v3X4.x, v3X4.y, coef.y);\n    vec2 v4X1 = mix(vec2(block16, block21), vec2(block17, block22), coef.xx);\n    vec2 v4X2 = mix(vec2(block17, block22), vec2(block18, block23), coef.xx);\n    vec2 v4X3 = mix(vec2(block18, block23), vec2(block19, block24), coef.xx);\n    vec2 v4X4 = mix(vec2(block19, block24), vec2(block20, block25), coef.xx);\n    float v4 = mix(v4X1.x, v4X1.y, coef.y) + mix(v4X2.x, v4X2.y, coef.y) + mix(v4X3.x, v4X3.y, coef.y) + mix(v4X4.x, v4X4.y, coef.y);\n    float fAvg = (v1 + v2 + v3 + v4) * 0.0625;\n    return fAvg;\n  }\n  bool GetShadowNDCPos(out vec3 shadowNDCPos, vec4 shadowPosWithDepthBias)\n  {\n  \tshadowNDCPos = shadowPosWithDepthBias.xyz / shadowPosWithDepthBias.w * 0.5 + 0.5;\n  \tif (shadowNDCPos.x < 0.0 || shadowNDCPos.x > 1.0 ||\n  \t\tshadowNDCPos.y < 0.0 || shadowNDCPos.y > 1.0 ||\n  \t\tshadowNDCPos.z < 0.0 || shadowNDCPos.z > 1.0) {\n  \t\treturn false;\n  \t}\n  \tshadowNDCPos.xy = cc_cameraPos.w == 1.0 ? vec2(shadowNDCPos.xy.x, 1.0 - shadowNDCPos.xy.y) : shadowNDCPos.xy;\n  \treturn true;\n  }\n  vec4 ApplyShadowDepthBias_FaceNormal(vec4 shadowPos, vec3 worldNormal, float normalBias, vec3 matViewDir0, vec3 matViewDir1, vec3 matViewDir2, vec2 projScaleXY)\n  {\n    vec4 newShadowPos = shadowPos;\n    if (normalBias > EPSILON_LOWP)\n    {\n      vec3 viewNormal = vec3(dot(matViewDir0, worldNormal), dot(matViewDir1, worldNormal), dot(matViewDir2, worldNormal));\n      if (viewNormal.z < 0.1)\n        newShadowPos.xy += viewNormal.xy * projScaleXY * normalBias * clamp(viewNormal.z, 0.001, 0.1);\n    }\n    return newShadowPos;\n  }\n  vec4 ApplyShadowDepthBias_FaceNormal(vec4 shadowPos, vec3 worldNormal, float normalBias, mat4 matLightView, vec2 projScaleXY)\n  {\n  \tvec4 newShadowPos = shadowPos;\n  \tif (normalBias > EPSILON_LOWP)\n  \t{\n  \t\tvec4 viewNormal = matLightView * vec4(worldNormal, 0.0);\n  \t\tif (viewNormal.z < 0.1)\n  \t\t\tnewShadowPos.xy += viewNormal.xy * projScaleXY * normalBias * clamp(viewNormal.z, 0.001, 0.1);\n  \t}\n  \treturn newShadowPos;\n  }\n  float GetViewSpaceDepthFromNDCDepth_Orthgraphic(float NDCDepth, float projScaleZ, float projBiasZ)\n  {\n  \treturn (NDCDepth - projBiasZ) / projScaleZ;\n  }\n  float GetViewSpaceDepthFromNDCDepth_Perspective(float NDCDepth, float homogenousDividW, float invProjScaleZ, float invProjBiasZ)\n  {\n  \treturn NDCDepth * invProjScaleZ + homogenousDividW * invProjBiasZ;\n  }\n  vec4 ApplyShadowDepthBias_Perspective(vec4 shadowPos, float viewspaceDepthBias)\n  {\n  \tvec3 viewSpacePos;\n  \tviewSpacePos.xy = shadowPos.xy * cc_shadowProjInfo.zw;\n  \tviewSpacePos.z = GetViewSpaceDepthFromNDCDepth_Perspective(shadowPos.z, shadowPos.w, cc_shadowInvProjDepthInfo.x, cc_shadowInvProjDepthInfo.y);\n  \tviewSpacePos.xyz += cc_shadowProjDepthInfo.z * normalize(viewSpacePos.xyz) * viewspaceDepthBias;\n  \tvec4 clipSpacePos;\n  \tclipSpacePos.xy = viewSpacePos.xy * cc_shadowProjInfo.xy;\n  \tclipSpacePos.zw = viewSpacePos.z * cc_shadowProjDepthInfo.xz + vec2(cc_shadowProjDepthInfo.y, 0.0);\n  \t#if CC_SHADOWMAP_USE_LINEAR_DEPTH\n  \t\tclipSpacePos.z = GetLinearDepthFromViewSpace(viewSpacePos.xyz, cc_shadowNFLSInfo.x, cc_shadowNFLSInfo.y);\n  \t\tclipSpacePos.z = (clipSpacePos.z * 2.0 - 1.0) * clipSpacePos.w;\n  \t#endif\n  \treturn clipSpacePos;\n  }\n  vec4 ApplyShadowDepthBias_Orthographic(vec4 shadowPos, float viewspaceDepthBias, float projScaleZ, float projBiasZ)\n  {\n  \tfloat coeffA = projScaleZ;\n  \tfloat coeffB = projBiasZ;\n  \tfloat viewSpacePos_z = GetViewSpaceDepthFromNDCDepth_Orthgraphic(shadowPos.z, projScaleZ, projBiasZ);\n  \tviewSpacePos_z += viewspaceDepthBias;\n  \tvec4 result = shadowPos;\n  \tresult.z = viewSpacePos_z * coeffA + coeffB;\n  \treturn result;\n  }\n  vec4 ApplyShadowDepthBias_PerspectiveLinearDepth(vec4 shadowPos, float viewspaceDepthBias, vec3 worldPos)\n  {\n    shadowPos.z = CCGetLinearDepth(worldPos, viewspaceDepthBias) * 2.0 - 1.0;\n    shadowPos.z *= shadowPos.w;\n    return shadowPos;\n  }\n  float CCGetDirLightShadowFactorHard (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorHard(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetDirLightShadowFactorSoft (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetDirLightShadowFactorSoft3X (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft3X(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetDirLightShadowFactorSoft5X (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft5X(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorHard (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorHard(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorSoft (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorSoft3X (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft3X(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorSoft5X (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft5X(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCSpotShadowFactorBase(out vec4 shadowPosWithDepthBias, vec4 shadowPos, vec3 worldPos, vec2 shadowBias)\n  {\n    float pcf = cc_shadowWHPBInfo.z;\n    vec4 pos = vec4(1.0);\n    #if CC_SHADOWMAP_USE_LINEAR_DEPTH\n      pos = ApplyShadowDepthBias_PerspectiveLinearDepth(shadowPos, shadowBias.x, worldPos);\n    #else\n      pos = ApplyShadowDepthBias_Perspective(shadowPos, shadowBias.x);\n    #endif\n    float realtimeShadow = 1.0;\n    if (pcf > 2.9) {\n      realtimeShadow = CCGetSpotLightShadowFactorSoft5X(pos, worldPos);\n    }else if (pcf > 1.9) {\n      realtimeShadow = CCGetSpotLightShadowFactorSoft3X(pos, worldPos);\n    }else if (pcf > 0.9) {\n      realtimeShadow = CCGetSpotLightShadowFactorSoft(pos, worldPos);\n    }else {\n      realtimeShadow = CCGetSpotLightShadowFactorHard(pos, worldPos);\n    }\n    shadowPosWithDepthBias = pos;\n    return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n  }\n  float CCShadowFactorBase(out vec4 shadowPosWithDepthBias, vec4 shadowPos, vec3 N, vec2 shadowBias)\n  {\n    vec4 pos = ApplyShadowDepthBias_FaceNormal(shadowPos, N, shadowBias.y, cc_matLightView, cc_shadowProjInfo.xy);\n    pos = ApplyShadowDepthBias_Orthographic(pos, shadowBias.x, cc_shadowProjDepthInfo.x, cc_shadowProjDepthInfo.y);\n    float realtimeShadow = 1.0;\n    #if CC_DIR_SHADOW_PCF_TYPE == 3\n      realtimeShadow = CCGetDirLightShadowFactorSoft5X(pos);\n    #endif\n    #if CC_DIR_SHADOW_PCF_TYPE == 2\n      realtimeShadow =  CCGetDirLightShadowFactorSoft3X(pos);\n    #endif\n    #if CC_DIR_SHADOW_PCF_TYPE == 1\n      realtimeShadow = CCGetDirLightShadowFactorSoft(pos);\n    #endif\n    #if CC_DIR_SHADOW_PCF_TYPE == 0\n      realtimeShadow = CCGetDirLightShadowFactorHard(pos);\n    #endif\n    shadowPosWithDepthBias = pos;\n    return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n  }\n  #if CC_SUPPORT_CASCADED_SHADOW_MAP\n    bool CCGetCSMLevelWithTransition(out highp float ratio, vec3 clipPos) {\n      highp float maxRange = 1.0 - cc_csmSplitsInfo.x;\n      highp float minRange = cc_csmSplitsInfo.x;\n      highp float thresholdInvert = 1.0 / cc_csmSplitsInfo.x;\n      ratio = 0.0;\n      if (clipPos.x <= minRange) {\n        ratio = clipPos.x * thresholdInvert;\n        return true;\n      }\n      if (clipPos.x >= maxRange) {\n        ratio = 1.0 - (clipPos.x - maxRange) * thresholdInvert;\n        return true;\n      }\n      if (clipPos.y <= minRange) {\n        ratio = clipPos.y  * thresholdInvert;\n        return true;\n      }\n      if (clipPos.y >= maxRange) {\n        ratio = 1.0 - (clipPos.y - maxRange) * thresholdInvert;\n        return true;\n      }\n      return false;\n    }\n    bool CCHasCSMLevel(int level, vec3 worldPos) {\n      highp float layerThreshold = cc_csmViewDir0[0].w;\n      bool hasLevel = false;\n      for (int i = 0; i < 4; i++) {\n        if (i == level) {\n          vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n          if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n              clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n              clipPos.z >= 0.0 && clipPos.z <= 1.0) {\n            hasLevel = true;\n          }\n        }\n      }\n      return hasLevel;\n    }\n    void CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos, int level) {\n      highp float layerThreshold = cc_csmViewDir0[0].w;\n      for (int i = 0; i < 4; i++) {\n        vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n        vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n        if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n            clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n            clipPos.z >= 0.0 && clipPos.z <= 1.0 && i == level) {\n          csmPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          csmPos.xy = csmPos.xy * cc_csmAtlas[i].xy + cc_csmAtlas[i].zw;\n          shadowProjDepthInfo = cc_csmProjDepthInfo[i];\n          shadowProjInfo = cc_csmProjInfo[i];\n          shadowViewDir0 = cc_csmViewDir0[i].xyz;\n          shadowViewDir1 = cc_csmViewDir1[i].xyz;\n          shadowViewDir2 = cc_csmViewDir2[i].xyz;\n        }\n      }\n    }\n    int CCGetCSMLevel(out bool isTransitionArea, out highp float transitionRatio, out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos)\n    {\n      int level = -1;\n      highp float layerThreshold = cc_csmViewDir0[0].w;\n      for (int i = 0; i < 4; i++) {\n        vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n        vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n        if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n            clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n            clipPos.z >= 0.0 && clipPos.z <= 1.0 && level < 0) {\n          #if CC_CASCADED_LAYERS_TRANSITION\n            isTransitionArea = CCGetCSMLevelWithTransition(transitionRatio, clipPos);\n          #endif\n          csmPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          csmPos.xy = csmPos.xy * cc_csmAtlas[i].xy + cc_csmAtlas[i].zw;\n          shadowProjDepthInfo = cc_csmProjDepthInfo[i];\n          shadowProjInfo = cc_csmProjInfo[i];\n          shadowViewDir0 = cc_csmViewDir0[i].xyz;\n          shadowViewDir1 = cc_csmViewDir1[i].xyz;\n          shadowViewDir2 = cc_csmViewDir2[i].xyz;\n          level = i;\n        }\n      }\n      return level;\n    }\n    int CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos)\n    {\n      bool isTransitionArea = false;\n      highp float transitionRatio = 0.0;\n      return CCGetCSMLevel(isTransitionArea, transitionRatio, csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n    }\n    float CCCSMFactorBase(out vec4 csmPos, out vec4 csmPosWithBias, vec3 worldPos, vec3 N, vec2 shadowBias)\n    {\n      bool isTransitionArea = false;\n      highp float ratio = 0.0;\n      csmPos = vec4(1.0);\n      vec4 shadowProjDepthInfo, shadowProjInfo;\n      vec3 shadowViewDir0, shadowViewDir1, shadowViewDir2;\n      int level = -1;\n      #if CC_CASCADED_LAYERS_TRANSITION\n        level = CCGetCSMLevel(isTransitionArea, ratio, csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n      #else\n        level = CCGetCSMLevel(csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n      #endif\n      if (level < 0) { return 1.0; }\n      vec4 pos = ApplyShadowDepthBias_FaceNormal(csmPos, N, shadowBias.y, shadowViewDir0, shadowViewDir1, shadowViewDir2, shadowProjInfo.xy);\n      pos = ApplyShadowDepthBias_Orthographic(pos, shadowBias.x, shadowProjDepthInfo.x, shadowProjDepthInfo.y);\n      csmPosWithBias = pos;\n      float realtimeShadow = 1.0;\n      #if CC_DIR_SHADOW_PCF_TYPE == 3\n        realtimeShadow = CCGetDirLightShadowFactorSoft5X(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 2\n        realtimeShadow = CCGetDirLightShadowFactorSoft3X(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 1\n        realtimeShadow = CCGetDirLightShadowFactorSoft(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 0\n        realtimeShadow = CCGetDirLightShadowFactorHard(pos);\n      #endif\n      #if CC_CASCADED_LAYERS_TRANSITION\n        vec4 nextCSMPos = vec4(1.0);\n        vec4 nextShadowProjDepthInfo, nextShadowProjInfo;\n        vec3 nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2;\n        float nextRealtimeShadow = 1.0;\n        CCGetCSMLevel(nextCSMPos, nextShadowProjDepthInfo, nextShadowProjInfo, nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2, worldPos, level + 1);\n        bool hasNextLevel = CCHasCSMLevel(level + 1, worldPos);\n        if (hasNextLevel && isTransitionArea) {\n          vec4 nexPos = ApplyShadowDepthBias_FaceNormal(nextCSMPos, N, shadowBias.y, nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2, nextShadowProjInfo.xy);\n          nexPos = ApplyShadowDepthBias_Orthographic(nexPos, shadowBias.x, nextShadowProjDepthInfo.x, nextShadowProjDepthInfo.y);\n          #if CC_DIR_SHADOW_PCF_TYPE == 3\n            nextRealtimeShadow = CCGetDirLightShadowFactorSoft5X(nexPos);\n          #endif\n          #if CC_DIR_SHADOW_PCF_TYPE == 2\n            nextRealtimeShadow = CCGetDirLightShadowFactorSoft3X(nexPos);\n          #endif\n          #if CC_DIR_SHADOW_PCF_TYPE == 1\n            nextRealtimeShadow = CCGetDirLightShadowFactorSoft(nexPos);\n          #endif\n          #if CC_DIR_SHADOW_PCF_TYPE == 0\n            nextRealtimeShadow = CCGetDirLightShadowFactorHard(nexPos);\n          #endif\n          return mix(mix(nextRealtimeShadow, realtimeShadow, ratio), 1.0, cc_shadowNFLSInfo.w);\n        }\n        return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n      #else\n        return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n      #endif\n    }\n  #else\n    int CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos) {\n      return -1;\n    }\n    float CCCSMFactorBase(out vec4 csmPos, out vec4 csmPosWithBias, vec3 worldPos, vec3 N, vec2 shadowBias) {\n      csmPos = cc_matLightViewProj * vec4(worldPos, 1.0);\n      return CCShadowFactorBase(csmPosWithBias, csmPos, N, shadowBias);\n    }\n  #endif\n  float CCShadowFactorBase(vec4 shadowPos, vec3 N, vec2 shadowBias) {\n    vec4 shadowPosWithDepthBias;\n    return CCShadowFactorBase(shadowPosWithDepthBias, shadowPos, N, shadowBias);\n  }\n  float CCCSMFactorBase(vec3 worldPos, vec3 N, vec2 shadowBias) {\n    vec4 csmPos, csmPosWithBias;\n    return CCCSMFactorBase(csmPos, csmPosWithBias, worldPos, N, shadowBias);\n  }\n  float CCSpotShadowFactorBase(vec4 shadowPos, vec3 worldPos, vec2 shadowBias)\n  {\n    vec4 shadowPosWithDepthBias;\n    return CCSpotShadowFactorBase(shadowPosWithDepthBias, shadowPos, worldPos, shadowBias);\n  }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\nvec4 packRGBE (vec3 rgb) {\n  highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n  highp float e = 128.0;\n  if (maxComp > 0.0001) {\n    e = log(maxComp) / log(1.1);\n    e = ceil(e);\n    e = clamp(e + 128.0, 0.0, 255.0);\n  }\n  highp float sc = 1.0 / pow(1.1, e - 128.0);\n  vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n  vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n  return vec4(encode_rounded, e) / 255.0;\n}\nvec3 unpackRGBE (vec4 rgbe) {\n  return rgbe.rgb * pow(1.1, rgbe.a * 255.0 - 128.0);\n}\nvec4 fragTextureLod (sampler2D tex, vec2 coord, float lod) {\n    return textureLod(tex, coord, lod);\n}\nvec4 fragTextureLod (samplerCube tex, vec3 coord, float lod) {\n      return textureLod(tex, coord, lod);\n}\nlayout(set = 0, binding = 5) uniform samplerCube cc_environment;\nvec3 CalculateReflectDirection(vec3 N, vec3 V, float NoV)\n{\n  float sideSign = NoV < 0.0 ? -1.0 : 1.0;\n  N *= sideSign;\n  return reflect(-V, N);\n}\nvec3 CalculatePlanarReflectPositionOnPlane(vec3 N, vec3 V, vec3 worldPos, vec4 plane, vec3 cameraPos, float probeReflectedDepth)\n{\n  float distPixelToPlane = -dot(plane, vec4(worldPos, 1.0));\n  plane.w += distPixelToPlane;\n  float distCameraToPlane = abs(-dot(plane, vec4(cameraPos, 1.0)));\n  vec3 planeN = plane.xyz;\n  vec3 virtualCameraPos = cameraPos - 2.0 * distCameraToPlane * planeN;\n  vec3 bumpedR = normalize(reflect(-V, N));\n  vec3 reflectedPointPos = worldPos + probeReflectedDepth * bumpedR;\n  vec3 virtualCameraToReflectedPoint = normalize(reflectedPointPos - virtualCameraPos);\n  float y = distCameraToPlane / max(EPSILON_LOWP, dot(planeN, virtualCameraToReflectedPoint));\n  return virtualCameraPos + y * virtualCameraToReflectedPoint;\n}\nvec4 CalculateBoxProjectedDirection(vec3 R, vec3 worldPos, vec3 cubeCenterPos, vec3 cubeBoxHalfSize)\n{\n  vec3 W = worldPos - cubeCenterPos;\n  vec3 projectedLength = (sign(R) * cubeBoxHalfSize - W) / (R + vec3(EPSILON));\n  float len = min(min(projectedLength.x, projectedLength.y), projectedLength.z);\n  vec3 P = W + len * R;\n  float weight = len < 0.0 ? 0.0 : 1.0;\n  return vec4(P, weight);\n}\n#if CC_USE_IBL\n  #if CC_USE_DIFFUSEMAP\n    layout(set = 0, binding = 7) uniform samplerCube cc_diffuseMap;\n  #endif\n#endif\n#if CC_USE_REFLECTION_PROBE\n  layout(set = 2, binding = 13) uniform samplerCube cc_reflectionProbeCubemap;\n  layout(set = 2, binding = 14) uniform sampler2D cc_reflectionProbePlanarMap;\n  layout(set = 2, binding = 15) uniform sampler2D cc_reflectionProbeDataMap;\n  layout(set = 2, binding = 0) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n  vec4 GetTexData(sampler2D dataMap, float dataMapWidth, float x, float uv_y)\n  {\n    return vec4(\n        decode32(texture(dataMap, vec2(((x + 0.5)/dataMapWidth), uv_y))),\n        decode32(texture(dataMap, vec2(((x + 1.5)/dataMapWidth), uv_y))),\n        decode32(texture(dataMap, vec2(((x + 2.5)/dataMapWidth), uv_y))),\n        decode32(texture(dataMap, vec2(((x + 3.5)/dataMapWidth), uv_y)))\n      );\n  }\n  void GetPlanarReflectionProbeData(out vec4 plane, out float planarReflectionDepthScale, out float mipCount, float probeId)\n  {\n      #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData1 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 0.0, uv_y);\n        vec4 texData2 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 4.0, uv_y);\n        plane.xyz = texData1.xyz;\n        plane.w = texData2.x;\n        planarReflectionDepthScale = texData2.y;\n        mipCount = texData2.z;\n      #else\n        plane = cc_reflectionProbeData1;\n        planarReflectionDepthScale = cc_reflectionProbeData2.x;\n        mipCount = cc_reflectionProbeData2.w;\n      #endif\n  }\n  void GetCubeReflectionProbeData(out vec3 centerPos, out vec3 boxHalfSize, out float mipCount, float probeId)\n  {\n      #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData1 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 0.0, uv_y);\n        vec4 texData2 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 4.0, uv_y);\n        vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n        centerPos = texData1.xyz;\n        boxHalfSize = texData2.xyz;\n        mipCount = texData3.x;\n      #else\n        centerPos = cc_reflectionProbeData1.xyz;\n        boxHalfSize = cc_reflectionProbeData2.xyz;\n        mipCount = cc_reflectionProbeData2.w;\n      #endif\n      if (mipCount > 1000.0) mipCount -= 1000.0;\n  }\n  bool isReflectProbeUsingRGBE(float probeId)\n  {\n    #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n        return texData3.x > 1000.0;\n    #else\n      return cc_reflectionProbeData2.w > 1000.0;\n    #endif\n  }\n#endif\n#if CC_USE_LIGHT_PROBE\n#if CC_USE_LIGHT_PROBE\n  #if USE_INSTANCING\n    layout(location = 12) in mediump vec4 v_sh_linear_const_r;\n    layout(location = 13) in mediump vec4 v_sh_linear_const_g;\n    layout(location = 14) in mediump vec4 v_sh_linear_const_b;\n  #else\n    layout(set = 2, binding = 6) uniform CCSH {\n      vec4 cc_sh_linear_const_r;\n      vec4 cc_sh_linear_const_g;\n      vec4 cc_sh_linear_const_b;\n      vec4 cc_sh_quadratic_r;\n      vec4 cc_sh_quadratic_g;\n      vec4 cc_sh_quadratic_b;\n      vec4 cc_sh_quadratic_a;\n    };\n  #endif\n  #if CC_USE_LIGHT_PROBE\n  vec3 SHEvaluate(vec3 normal)\n  {\n      vec3 result;\n  #if USE_INSTANCING\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(v_sh_linear_const_r, normal4);\n      result.g = dot(v_sh_linear_const_g, normal4);\n      result.b = dot(v_sh_linear_const_b, normal4);\n  #else\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(cc_sh_linear_const_r, normal4);\n      result.g = dot(cc_sh_linear_const_g, normal4);\n      result.b = dot(cc_sh_linear_const_b, normal4);\n      vec4 n14 = normal.xyzz * normal.yzzx;\n      float n5 = normal.x * normal.x - normal.y * normal.y;\n      result.r += dot(cc_sh_quadratic_r, n14);\n      result.g += dot(cc_sh_quadratic_g, n14);\n      result.b += dot(cc_sh_quadratic_b, n14);\n      result += (cc_sh_quadratic_a.rgb * n5);\n  #endif\n    #if CC_USE_HDR\n      result *= cc_exposure.w * cc_exposure.x;\n    #endif\n    return result;\n  }\n  #endif\n#endif\n#endif\nfloat GGXMobile (float roughness, float NoH, vec3 H, vec3 N) {\n  vec3 NxH = cross(N, H);\n  float OneMinusNoHSqr = dot(NxH, NxH);\n  float a = roughness * roughness;\n  float n = NoH * a;\n  float p = a / max(EPSILON, OneMinusNoHSqr + n * n);\n  return p * p;\n}\nfloat CalcSpecular (float roughness, float NoH, vec3 H, vec3 N) {\n  return (roughness * 0.25 + 0.25) * GGXMobile(roughness, NoH, H, N);\n}\nvec3 BRDFApprox (vec3 specular, float roughness, float NoV) {\n  const vec4 c0 = vec4(-1.0, -0.0275, -0.572, 0.022);\n  const vec4 c1 = vec4(1.0, 0.0425, 1.04, -0.04);\n  vec4 r = roughness * c0 + c1;\n  float a004 = min(r.x * r.x, exp2(-9.28 * NoV)) * r.x + r.y;\n  vec2 AB = vec2(-1.04, 1.04) * a004 + r.zw;\n  AB.y *= clamp(50.0 * specular.g, 0.0, 1.0);\n  return max(vec3(0.0), specular * AB.x + AB.y);\n}\n#if USE_REFLECTION_DENOISE\n  vec3 GetEnvReflectionWithMipFiltering(vec3 R, float roughness, float mipCount, float denoiseIntensity, vec2 screenUV) {\n    #if CC_USE_IBL\n    \tfloat mip = roughness * (mipCount - 1.0);\n    \tfloat delta = (dot(dFdx(R), dFdy(R))) * 1000.0;\n    \tfloat mipBias = mix(0.0, 5.0, clamp(delta, 0.0, 1.0));\n      #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_CUBE\n        vec4 biased = fragTextureLod(cc_reflectionProbeCubemap, R, mip + mipBias);\n     \t  vec4 filtered = texture(cc_reflectionProbeCubemap, R);\n      #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_PLANAR\n        vec4 biased = fragTextureLod(cc_reflectionProbePlanarMap, screenUV, mip + mipBias);\n        vec4 filtered = texture(cc_reflectionProbePlanarMap, screenUV);\n      #else\n        vec4 biased = fragTextureLod(cc_environment, R, mip + mipBias);\n     \t  vec4 filtered = texture(cc_environment, R);\n      #endif\n      #if CC_USE_IBL == 2 || CC_USE_REFLECTION_PROBE != REFLECTION_PROBE_TYPE_NONE\n        biased.rgb = unpackRGBE(biased);\n      \tfiltered.rgb = unpackRGBE(filtered);\n      #else\n      \tbiased.rgb = SRGBToLinear(biased.rgb);\n      \tfiltered.rgb = SRGBToLinear(filtered.rgb);\n      #endif\n      return mix(biased.rgb, filtered.rgb, denoiseIntensity);\n    #else\n      return vec3(0.0, 0.0, 0.0);\n    #endif\n  }\n#endif\nstruct StandardSurface {\n  vec4 albedo;\n    #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n    vec3 position, position_fract_part;\n    #else\n    vec3 position;\n    #endif\n  vec3 normal;\n  vec3 emissive;\n  vec4 lightmap;\n  float lightmap_test;\n  float roughness;\n  float metallic;\n  float occlusion;\n  float specularIntensity;\n  #if CC_RECEIVE_SHADOW\n    vec2 shadowBias;\n  #endif\n  #if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n    float reflectionProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n    float reflectionProbeBlendId;\n    float reflectionProbeBlendFactor;\n  #endif\n};\n vec3 SampleReflectionProbe(samplerCube tex, vec3 R, float roughness, float mipCount, bool isRGBE) {\n    vec4 envmap = fragTextureLod(tex, R, roughness * (mipCount - 1.0));\n    if (isRGBE)\n      return unpackRGBE(envmap);\n    else\n      return SRGBToLinear(envmap.rgb);\n  }\nvec4 CCStandardShadingBase (StandardSurface s, vec4 shadowPos) {\n  vec3 diffuse = s.albedo.rgb * (1.0 - s.metallic);\n  vec3 specular = mix(vec3(0.08 * s.specularIntensity), s.albedo.rgb, s.metallic);\n  vec3 position;\n    #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n    position = unpackHighpData(s.position, s.position_fract_part);\n    #else\n    position = s.position;\n    #endif\n  vec3 N = normalize(s.normal);\n  vec3 V = normalize(cc_cameraPos.xyz - position);\n  vec3 L = normalize(-cc_mainLitDir.xyz);\n  float NL = max(dot(N, L), 0.0);\n  float shadow = 1.0;\n  #if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n    if (NL > 0.0 && cc_mainLitDir.w > 0.0) {\n      #if CC_DIR_LIGHT_SHADOW_TYPE == 2\n        shadow = CCCSMFactorBase(position, N, s.shadowBias);\n      #endif\n      #if CC_DIR_LIGHT_SHADOW_TYPE == 1\n        shadow = CCShadowFactorBase(shadowPos, N, s.shadowBias);\n      #endif\n    }\n  #endif\n  vec3 finalColor = vec3(0.0);\n  #if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n    vec3 lightmap = s.lightmap.rgb;\n    #if CC_USE_HDR\n        lightmap.rgb *= cc_exposure.w * cc_exposure.x;\n    #endif\n    #if CC_USE_LIGHTMAP == LIGHT_MAP_TYPE_INDIRECT_OCCLUSION\n      shadow *= s.lightmap.a;\n      finalColor += diffuse * lightmap.rgb;\n    #else\n      finalColor += diffuse * lightmap.rgb * shadow;\n    #endif\n    s.occlusion *= s.lightmap_test;\n  #endif\n  #if !CC_DISABLE_DIRECTIONAL_LIGHT\n    float NV = max(abs(dot(N, V)), 0.0);\n    specular = BRDFApprox(specular, s.roughness, NV);\n    vec3 H = normalize(L + V);\n    float NH = max(dot(N, H), 0.0);\n    vec3 lightingColor = NL * cc_mainLitColor.rgb * cc_mainLitColor.w;\n    vec3 diffuseContrib = diffuse / PI;\n    vec3 specularContrib = specular * CalcSpecular(s.roughness, NH, H, N);\n    vec3 dirlightContrib = (diffuseContrib + specularContrib);\n    dirlightContrib *= shadow;\n    finalColor += lightingColor * dirlightContrib;\n  #endif\n  float fAmb = max(EPSILON, 0.5 - N.y * 0.5);\n  vec3 ambDiff = mix(cc_ambientSky.rgb, cc_ambientGround.rgb, fAmb);\n  vec3 env = vec3(0.0), rotationDir;\n  #if CC_USE_IBL\n    #if CC_USE_DIFFUSEMAP && !CC_USE_LIGHT_PROBE\n      rotationDir = RotationVecFromAxisY(N.xyz, cc_surfaceTransform.z, cc_surfaceTransform.w);\n      vec4 diffuseMap = texture(cc_diffuseMap, rotationDir);\n      #if CC_USE_DIFFUSEMAP == 2\n        ambDiff = unpackRGBE(diffuseMap);\n      #else\n        ambDiff = SRGBToLinear(diffuseMap.rgb);\n      #endif\n    #endif\n    #if !CC_USE_REFLECTION_PROBE\n      vec3 R = normalize(reflect(-V, N));\n      rotationDir = RotationVecFromAxisY(R.xyz, cc_surfaceTransform.z, cc_surfaceTransform.w);\n      #if USE_REFLECTION_DENOISE && !CC_IBL_CONVOLUTED\n        env = GetEnvReflectionWithMipFiltering(rotationDir, s.roughness, cc_ambientGround.w, 0.6, vec2(0.0));\n      #else\n        vec4 envmap = fragTextureLod(cc_environment, rotationDir, s.roughness * (cc_ambientGround.w - 1.0));\n        #if CC_USE_IBL == 2\n          env = unpackRGBE(envmap);\n        #else\n          env = SRGBToLinear(envmap.rgb);\n        #endif\n      #endif\n    #endif\n  #endif\n  float lightIntensity = cc_ambientSky.w;\n  #if CC_USE_REFLECTION_PROBE\n    vec4 probe = vec4(0.0);\n    vec3 R = normalize(reflect(-V, N));\n    #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_CUBE\n      if(s.reflectionProbeId < 0.0){\n        env = SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2);\n      }else{\n        vec3 centerPos, boxHalfSize;\n        float mipCount;\n        GetCubeReflectionProbeData(centerPos, boxHalfSize, mipCount, s.reflectionProbeId);\n        vec4 fixedR = CalculateBoxProjectedDirection(R, position, centerPos, boxHalfSize);\n        env = mix(SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2) * lightIntensity,\n          SampleReflectionProbe(cc_reflectionProbeCubemap, fixedR.xyz, s.roughness, mipCount, isReflectProbeUsingRGBE(s.reflectionProbeId)), fixedR.w);\n      }\n    #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_PLANAR\n      if(s.reflectionProbeId < 0.0){\n        vec2 screenUV = GetPlanarReflectScreenUV(s.position, cc_matViewProj, cc_cameraPos.w, V, R);\n        probe = fragTextureLod(cc_reflectionProbePlanarMap, screenUV, 1.0);\n      }else{\n        vec4 plane;\n        float planarReflectionDepthScale, mipCount;\n        GetPlanarReflectionProbeData(plane, planarReflectionDepthScale, mipCount, s.reflectionProbeId);\n        R = normalize(CalculateReflectDirection(N, V, max(abs(dot(N, V)), 0.0)));\n        vec3 worldPosOffset = CalculatePlanarReflectPositionOnPlane(N, V, s.position, plane, cc_cameraPos.xyz, planarReflectionDepthScale);\n        vec2 screenUV = GetPlanarReflectScreenUV(worldPosOffset, cc_matViewProj, cc_cameraPos.w, V, R);\n        probe = fragTextureLod(cc_reflectionProbePlanarMap, screenUV, mipCount);\n      }\n      env = unpackRGBE(probe);\n    #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n      if (s.reflectionProbeId < 0.0) {\n        env = SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2);\n      } else {\n        vec3 centerPos, boxHalfSize;\n        float mipCount;\n        GetCubeReflectionProbeData(centerPos, boxHalfSize, mipCount, s.reflectionProbeId);\n        vec4 fixedR = CalculateBoxProjectedDirection(R, s.position, centerPos, boxHalfSize);\n        env = SampleReflectionProbe(cc_reflectionProbeCubemap, fixedR.xyz, s.roughness, mipCount, isReflectProbeUsingRGBE(s.reflectionProbeId));\n        if (s.reflectionProbeBlendId < 0.0) {\n          vec3 skyBoxEnv = SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2) * lightIntensity;\n          #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n            env = mix(env, skyBoxEnv, s.reflectionProbeBlendFactor);\n          #else\n            env = mix(skyBoxEnv, env, fixedR.w);\n          #endif\n        }\n      }\n    #endif\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    lightIntensity = s.reflectionProbeId < 0.0 ? lightIntensity : 1.0;\n  #endif\n  finalColor += env * lightIntensity * specular * s.occlusion;\n#if CC_USE_LIGHT_PROBE\n  finalColor += SHEvaluate(N) * diffuse * s.occlusion;\n#endif\n  finalColor += ambDiff.rgb * cc_ambientSky.w * diffuse * s.occlusion;\n  finalColor += s.emissive;\n  return vec4(finalColor, s.albedo.a);\n}\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec4 CCFragOutput (vec4 color) {\n  #if CC_USE_RGBE_OUTPUT\n    color = packRGBE(color.rgb);\n  #elif !CC_USE_FLOAT_OUTPUT\n    #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    color.rgb = LinearToSRGB(color.rgb);\n  #endif\n  return color;\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\nvoid CC_APPLY_FOG_BASE(inout vec4 color, float factor) {\n\tcolor = vec4(mix(cc_fogColor.rgb, color.rgb, factor), color.a);\n}\n#if !CC_USE_ACCURATE_FOG\nlayout(location = 0) in mediump float v_fog_factor;\n#endif\nvoid CC_APPLY_FOG(inout vec4 color) {\n#if !CC_USE_ACCURATE_FOG\n    CC_APPLY_FOG_BASE(color, v_fog_factor);\n#endif\n}\nvoid CC_APPLY_FOG(inout vec4 color, vec3 worldPos) {\n#if CC_USE_ACCURATE_FOG\n    float factor;\n    CC_TRANSFER_FOG_BASE(vec4(worldPos, 1.0), factor);\n#else\n    float factor = v_fog_factor;\n#endif\n    CC_APPLY_FOG_BASE(color, factor);\n}\nlayout(location = 1) in highp vec4 v_shadowPos;\n#if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  layout(location = 15) in vec3 v_luv;\n  layout(set = 2, binding = 11) uniform sampler2D cc_lightingMap;\n  void SampleAndDecodeLightMapColor(out vec3 lightmapColor, out float dirShadow, out float ao, sampler2D lightingMap, vec2 luv, float lum, vec3 worldNormal)\n  {\n  #if CC_LIGHT_MAP_VERSION > 2\n  #elif CC_LIGHT_MAP_VERSION > 1\n  \tvec4 dataLow = texture(lightingMap, luv);\n  \tvec4 dataHigh = texture(lightingMap, luv + vec2(0.5, 0.0));\n  \tlightmapColor.xyz = dataLow.xyz + dataHigh.xyz * 0.00392156862745098;\n      lightmapColor.rgb *= lum;\n  \tdirShadow = dataLow.a;\n  \tao = dataHigh.a;\n  #else\n      vec4 lightmap = texture(lightingMap, luv);\n      lightmapColor = lightmap.rgb * lum;\n  \tdirShadow = lightmap.a;\n  \tao = 1.0;\n  #endif\n  }\n#endif\nlayout(location = 3) in highp vec3 v_position;\nlayout(location = 4) in mediump vec3 v_normal;\n#if CC_RECEIVE_SHADOW\n  layout(location = 2) in vec2 v_shadowBias;\n#endif\nlayout(location = 5) in mediump vec2 uvw;\nlayout(location = 6) in mediump vec2 uv0;\nlayout(location = 7) in mediump vec2 uv1;\nlayout(location = 8) in mediump vec2 uv2;\nlayout(location = 9) in mediump vec2 uv3;\nlayout(location = 11) in mediump vec3 diffuse;\nlayout(location = 10) in mediump vec3 luv;\nlayout(set = 1, binding = 1) uniform PbrParams {\n  vec4 metallic;\n  vec4 roughness;\n};\nlayout(set = 1, binding = 2) uniform sampler2D weightMap;\nlayout(set = 1, binding = 3) uniform sampler2D detailMap0;\nlayout(set = 1, binding = 4) uniform sampler2D detailMap1;\nlayout(set = 1, binding = 5) uniform sampler2D detailMap2;\nlayout(set = 1, binding = 6) uniform sampler2D detailMap3;\nlayout(set = 1, binding = 7) uniform sampler2D normalMap0;\nlayout(set = 1, binding = 8) uniform sampler2D normalMap1;\nlayout(set = 1, binding = 9) uniform sampler2D normalMap2;\nlayout(set = 1, binding = 10) uniform sampler2D normalMap3;\nvoid surf (out StandardSurface s) {\n  #if LAYERS > 1\n    vec4 w = texture(weightMap, uvw);\n  #endif\n  vec4 baseColor = vec4(0, 0, 0, 0);\n  #if LAYERS == 1\n    baseColor = texture(detailMap0, uv0);\n  #elif LAYERS == 2\n    baseColor += texture(detailMap0, uv0) * w.r;\n    baseColor += texture(detailMap1, uv1) * w.g;\n  #elif LAYERS == 3\n    baseColor += texture(detailMap0, uv0) * w.r;\n    baseColor += texture(detailMap1, uv1) * w.g;\n    baseColor += texture(detailMap2, uv2) * w.b;\n  #elif LAYERS == 4\n    baseColor += texture(detailMap0, uv0) * w.r;\n    baseColor += texture(detailMap1, uv1) * w.g;\n    baseColor += texture(detailMap2, uv2) * w.b;\n    baseColor += texture(detailMap3, uv3) * w.a;\n  #else\n    baseColor = texture(detailMap0, uv0);\n  #endif\n    #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n    packHighpData(s.position, s.position_fract_part, v_position);\n    #else\n    s.position = v_position;\n    #endif\n  #if USE_NORMALMAP\n    vec4 baseNormal = vec4(0, 0, 0, 0);\n    #if LAYERS == 1\n      baseNormal = texture(normalMap0, uv0);\n    #elif LAYERS == 2\n      baseNormal += texture(normalMap0, uv0) * w.r;\n      baseNormal += texture(normalMap1, uv1) * w.g;\n    #elif LAYERS == 3\n      baseNormal += texture(normalMap0, uv0) * w.r;\n      baseNormal += texture(normalMap1, uv1) * w.g;\n      baseNormal += texture(normalMap2, uv2) * w.b;\n    #elif LAYERS == 4\n      baseNormal += texture(normalMap0, uv0) * w.r;\n      baseNormal += texture(normalMap1, uv1) * w.g;\n      baseNormal += texture(normalMap2, uv2) * w.b;\n      baseNormal += texture(normalMap3, uv3) * w.a;\n    #else\n      baseNormal = texture(normalMap0, uv0);\n    #endif\n    vec3 tangent = vec3(1.0, 0.0, 0.0);\n    vec3 binormal = vec3(0.0, 0.0, 1.0);\n    binormal = cross(tangent, v_normal);\n    tangent = cross(v_normal, binormal);\n    vec3 nmmp = baseNormal.xyz - vec3(0.5);\n    s.normal =\n      nmmp.x * normalize(tangent) +\n      nmmp.y * normalize(binormal) +\n      nmmp.z * normalize(v_normal);\n  #else\n    s.normal = v_normal;\n  #endif\n  #if CC_RECEIVE_SHADOW\n    s.shadowBias = v_shadowBias;\n  #endif\n  s.albedo = vec4(SRGBToLinear(baseColor.rgb), 1.0);\n  s.occlusion = 1.0;\n  #if USE_PBR\n    s.roughness = 0.0;\n    #if LAYERS == 1\n      s.roughness = roughness.x;\n    #elif LAYERS == 2\n      s.roughness += roughness.x * w.r;\n      s.roughness += roughness.y * w.g;\n    #elif LAYERS == 3\n      s.roughness += roughness.x * w.r;\n      s.roughness += roughness.y * w.g;\n      s.roughness += roughness.z * w.b;\n    #elif LAYERS == 4\n      s.roughness += roughness.x * w.r;\n      s.roughness += roughness.y * w.g;\n      s.roughness += roughness.z * w.b;\n      s.roughness += roughness.w * w.a;\n    #else\n      s.roughness = 1.0;\n    #endif\n    s.specularIntensity = 0.5;\n    s.metallic = 0.0;\n    #if LAYERS == 1\n      s.specularIntensity = 0.5;\n      s.metallic = metallic.x;\n    #elif LAYERS == 2\n      s.metallic += metallic.x * w.r;\n      s.metallic += metallic.y * w.g;\n    #elif LAYERS == 3\n      s.metallic += metallic.x * w.r;\n      s.metallic += metallic.y * w.g;\n      s.metallic += metallic.z * w.b;\n    #elif LAYERS == 4\n      s.metallic += metallic.x * w.r;\n      s.metallic += metallic.y * w.g;\n      s.metallic += metallic.z * w.b;\n      s.metallic += metallic.w * w.a;\n    #else\n      s.specularIntensity = 0.5;\n      s.metallic = 0.0;\n    #endif\n  #else\n    s.roughness = 1.0;\n    s.specularIntensity = 0.5;\n    s.metallic = 0.0;\n  #endif\n  s.emissive = vec3(0.0, 0.0, 0.0);\n  #if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n    SampleAndDecodeLightMapColor(s.lightmap.rgb, s.lightmap.a, s.lightmap_test, cc_lightingMap, luv.xy, luv.z, s.normal);\n  #endif\n}\n#if CC_FORWARD_ADD\n  #if CC_PIPELINE_TYPE == 0\n    #define LIGHTS_PER_PASS 1\n  #else\n    #define LIGHTS_PER_PASS 10\n  #endif\n  #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 0\n  layout(set = 2, binding = 1) uniform CCForwardLight {\n    highp vec4 cc_lightPos[LIGHTS_PER_PASS];\n    vec4 cc_lightColor[LIGHTS_PER_PASS];\n    vec4 cc_lightSizeRangeAngle[LIGHTS_PER_PASS];\n    vec4 cc_lightDir[LIGHTS_PER_PASS];\n    vec4 cc_lightBoundingSizeVS[LIGHTS_PER_PASS];\n  };\n  #endif\n  float SmoothDistAtt (float distSqr, float invSqrAttRadius) {\n    float factor = distSqr * invSqrAttRadius;\n    float smoothFactor = clamp(1.0 - factor * factor, 0.0, 1.0);\n    return smoothFactor * smoothFactor;\n  }\n  float GetDistAtt (float distSqr, float invSqrAttRadius) {\n    float attenuation = 1.0 / max(distSqr, 0.01*0.01);\n    attenuation *= SmoothDistAtt(distSqr , invSqrAttRadius);\n    return attenuation;\n  }\n  float GetAngleAtt (vec3 L, vec3 litDir, float litAngleScale, float litAngleOffset) {\n    float cd = dot(litDir, L);\n    float attenuation = clamp(cd * litAngleScale + litAngleOffset, 0.0, 1.0);\n    return (attenuation * attenuation);\n  }\n  float GetOutOfRange (vec3 worldPos, vec3 lightPos, vec3 lookAt, vec3 right, vec3 BoundingHalfSizeVS) {\n    vec3 v = vec3(0.0);\n    vec3 up = cross(right, lookAt);\n    worldPos -= lightPos;\n    v.x = dot(worldPos, right);\n    v.y = dot(worldPos, up);\n    v.z = dot(worldPos, lookAt);\n    vec3 result = step(abs(v), BoundingHalfSizeVS);\n    return result.x * result.y * result.z;\n  }\n  #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 0\n  vec4 CCStandardShadingAdditive (StandardSurface s, vec4 shadowPos) {\n    vec3 position;\n      #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n      position = unpackHighpData(s.position, s.position_fract_part);\n      #else\n      position = s.position;\n      #endif\n    vec3 diffuse = s.albedo.rgb * (1.0 - s.metallic);\n    vec3 specular = mix(vec3(0.04), s.albedo.rgb, s.metallic);\n    vec3 diffuseContrib = diffuse / PI;\n    vec3 N = normalize(s.normal);\n    vec3 V = normalize(cc_cameraPos.xyz - position);\n    float NV = max(abs(dot(N, V)), 0.0);\n    specular = BRDFApprox(specular, s.roughness, NV);\n    vec3 finalColor = vec3(0.0);\n    int numLights = CC_PIPELINE_TYPE == 0 ? LIGHTS_PER_PASS : int(cc_lightDir[0].w);\n    for (int i = 0; i < LIGHTS_PER_PASS; i++) {\n      if (i >= numLights) break;\n      vec3 SLU = IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w) ? -cc_lightDir[i].xyz : cc_lightPos[i].xyz - position;\n      vec3 SL = normalize(SLU);\n      vec3 SH = normalize(SL + V);\n      float SNL = max(dot(N, SL), 0.0);\n      float SNH = max(dot(N, SH), 0.0);\n      vec3 lspec = specular * CalcSpecular(s.roughness, SNH, SH, N);\n      float illum = 1.0;\n      float att = 1.0;\n      if (IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w)) {\n        att = GetOutOfRange(position, cc_lightPos[i].xyz, cc_lightDir[i].xyz, cc_lightSizeRangeAngle[i].xyz, cc_lightBoundingSizeVS[i].xyz);\n      } else {\n        float distSqr = dot(SLU, SLU);\n        float litRadius = cc_lightSizeRangeAngle[i].x;\n        float litRadiusSqr = litRadius * litRadius;\n        illum = (IS_POINT_LIGHT(cc_lightPos[i].w) || IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w)) ? 1.0 : litRadiusSqr / max(litRadiusSqr, distSqr);\n        float attRadiusSqrInv = 1.0 / max(cc_lightSizeRangeAngle[i].y, 0.01);\n        attRadiusSqrInv *= attRadiusSqrInv;\n        att = GetDistAtt(distSqr, attRadiusSqrInv);\n        if (IS_SPOT_LIGHT(cc_lightPos[i].w)) {\n          float cosInner = max(dot(-cc_lightDir[i].xyz, SL), 0.01);\n          float cosOuter = cc_lightSizeRangeAngle[i].z;\n          float strength = clamp(cc_lightBoundingSizeVS[i].w, 0.0, 1.0);\n          float litAngleScale = 1.0 / max(0.001, mix(cosInner, 1.0, strength) - cosOuter);\n          float litAngleOffset = -cosOuter * litAngleScale;\n          att *= GetAngleAtt(SL, -cc_lightDir[i].xyz, litAngleScale, litAngleOffset);\n        }\n      }\n      float shadow = 1.0;\n      #if CC_RECEIVE_SHADOW  && CC_SHADOW_TYPE == 2\n        if (IS_SPOT_LIGHT(cc_lightPos[i].w) && cc_lightSizeRangeAngle[i].w > 0.0) {\n          shadow = CCSpotShadowFactorBase(shadowPos, position, s.shadowBias);\n        }\n      #endif\n      finalColor += SNL * cc_lightColor[i].rgb * shadow * cc_lightColor[i].w * illum * att * (diffuseContrib + lspec);\n    }\n    return vec4(finalColor, 0.0);\n  }\n  #endif\n  #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 1\n  layout(std430, set = 1, binding = 11) readonly buffer b_ccLightsBuffer { vec4 b_ccLights[]; };\n  layout(std430, set = 1, binding = 12) readonly buffer b_clusterLightIndicesBuffer { uint b_clusterLightIndices[]; };\n  layout(std430, set = 1, binding = 13) readonly buffer b_clusterLightGridBuffer { uvec4 b_clusterLightGrid[]; };\n  struct CCLight\n  {\n    vec4 cc_lightPos;\n    vec4 cc_lightColor;\n    vec4 cc_lightSizeRangeAngle;\n    vec4 cc_lightDir;\n    vec4 cc_lightBoundingSizeVS;\n  };\n  struct Cluster\n  {\n    vec3 minBounds;\n    vec3 maxBounds;\n  };\n  struct LightGrid\n  {\n    uint offset;\n    uint ccLights;\n  };\n  CCLight getCCLight(uint i)\n  {\n    CCLight light;\n    light.cc_lightPos = b_ccLights[5u * i + 0u];\n    light.cc_lightColor = b_ccLights[5u * i + 1u];\n    light.cc_lightSizeRangeAngle = b_ccLights[5u * i + 2u];\n    light.cc_lightDir = b_ccLights[5u * i + 3u];\n    light.cc_lightBoundingSizeVS = b_ccLights[5u * i + 4u];\n    return light;\n  }\n  LightGrid getLightGrid(uint cluster)\n  {\n    uvec4 gridvec = b_clusterLightGrid[cluster];\n    LightGrid grid;\n    grid.offset = gridvec.x;\n    grid.ccLights = gridvec.y;\n    return grid;\n  }\n  uint getGridLightIndex(uint start, uint offset)\n  {\n    return b_clusterLightIndices[start + offset];\n  }\n  uint getClusterZIndex(vec4 worldPos)\n  {\n    float scale = float(24u) / log(cc_nearFar.y / cc_nearFar.x);\n    float bias = -(float(24u) * log(cc_nearFar.x) / log(cc_nearFar.y / cc_nearFar.x));\n    float eyeDepth = -(cc_matView * worldPos).z;\n    uint zIndex = uint(max(log(eyeDepth) * scale + bias, 0.0));\n    return zIndex;\n  }\n  uint getClusterIndex(vec4 fragCoord, vec4 worldPos)\n  {\n    uint zIndex = getClusterZIndex(worldPos);\n    float clusterSizeX = ceil(cc_viewPort.z / float(16u));\n    float clusterSizeY = ceil(cc_viewPort.w / float(8u));\n    uvec3 indices = uvec3(uvec2(fragCoord.xy / vec2(clusterSizeX, clusterSizeY)), zIndex);\n    uint cluster = (16u * 8u) * indices.z + 16u * indices.y + indices.x;\n    return cluster;\n  }\n  vec4 CCClusterShadingAdditive (StandardSurface s, vec4 shadowPos) {\n    vec3 diffuse = s.albedo.rgb * (1.0 - s.metallic);\n    vec3 specular = mix(vec3(0.04), s.albedo.rgb, s.metallic);\n    vec3 diffuseContrib = diffuse / PI;\n    vec3 position;\n      #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n      position = unpackHighpData(s.position, s.position_fract_part);\n      #else\n      position = s.position;\n      #endif\n    vec3 N = normalize(s.normal);\n    vec3 V = normalize(cc_cameraPos.xyz - position);\n    float NV = max(abs(dot(N, V)), 0.001);\n    specular = BRDFApprox(specular, s.roughness, NV);\n    vec3 finalColor = vec3(0.0);\n    uint cluster = getClusterIndex(gl_FragCoord, vec4(position, 1.0));\n    LightGrid grid = getLightGrid(cluster);\n    uint numLights = grid.ccLights;\n    for (uint i = 0u; i < 200u; i++) {\n      if (i >= numLights) break;\n      uint lightIndex = getGridLightIndex(grid.offset, i);\n      CCLight light = getCCLight(lightIndex);\n      vec3 SLU = light.cc_lightPos.xyz - position;\n      vec3 SL = normalize(SLU);\n      vec3 SH = normalize(SL + V);\n      float SNL = max(dot(N, SL), 0.001);\n      float SNH = max(dot(N, SH), 0.0);\n      float distSqr = dot(SLU, SLU);\n      float litRadius = light.cc_lightSizeRangeAngle.x;\n      float litRadiusSqr = litRadius * litRadius;\n      float illum = PI * (litRadiusSqr / max(litRadiusSqr , distSqr));\n      float attRadiusSqrInv = 1.0 / max(light.cc_lightSizeRangeAngle.y, 0.01);\n      attRadiusSqrInv *= attRadiusSqrInv;\n      float att = GetDistAtt(distSqr, attRadiusSqrInv);\n      vec3 lspec = specular * CalcSpecular(s.roughness, SNH, SH, N);\n      if (IS_SPOT_LIGHT(light.cc_lightPos.w)) {\n        float cosInner = max(dot(-light.cc_lightDir.xyz, SL), 0.01);\n        float cosOuter = light.cc_lightSizeRangeAngle.z;\n        float litAngleScale = 1.0 / max(0.001, cosInner - cosOuter);\n        float litAngleOffset = -cosOuter * litAngleScale;\n        att *= GetAngleAtt(SL, -light.cc_lightDir.xyz, litAngleScale, litAngleOffset);\n      }\n      vec3 lightColor = light.cc_lightColor.rgb;\n      float shadow = 1.0;\n      #if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n        if (IS_SPOT_LIGHT(light.cc_lightPos.w)  && light.cc_lightSizeRangeAngle.w > 0.0) {\n          shadow = CCSpotShadowFactorBase(shadowPos, position, s.shadowBias);\n        }\n      #endif\n      lightColor *= shadow;\n      finalColor += SNL * lightColor * light.cc_lightColor.w * illum * att * (diffuseContrib + lspec);\n    }\n    return vec4(finalColor, 0.0);\n  }\n  #endif\n  layout(location = 0) out vec4 fragColorX;\n  void main () {\n    StandardSurface s; surf(s);\n    #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 1\n    vec4 color = CCClusterShadingAdditive(s, v_shadowPos);\n    #else\n    vec4 color = CCStandardShadingAdditive(s, v_shadowPos);\n    #endif\n    fragColorX = CCFragOutput(color);\n  }\n#elif (CC_PIPELINE_TYPE == 0 || CC_FORCE_FORWARD_SHADING)\n  layout(location = 0) out vec4 fragColorX;\n  void main () {\n    StandardSurface s; surf(s);\n    vec4 color = CCStandardShadingBase(s, v_shadowPos);\n    #if CC_USE_FOG != 4\n      #if CC_USE_FLOAT_OUTPUT\n        CC_APPLY_FOG(color, s.position.xyz);\n      #elif !CC_FORWARD_ADD\n        CC_APPLY_FOG(color, s.position.xyz);\n      #endif\n    #endif\n    fragColorX = CCFragOutput(color);\n  }\n#elif CC_PIPELINE_TYPE == 1\n  vec2 signNotZero(vec2 v) {\n    return vec2((v.x >= 0.0) ? +1.0 : -1.0, (v.y >= 0.0) ? +1.0 : -1.0);\n  }\n  vec2 float32x3_to_oct(in vec3 v) {\n    vec2 p = v.xy * (1.0 / (abs(v.x) + abs(v.y) + abs(v.z)));\n    return (v.z <= 0.0) ? ((1.0 - abs(p.yx)) * signNotZero(p)) : p;\n  }\n  layout(location = 0) out vec4 albedoOut;\n  layout(location = 1) out vec4 emissiveOut;\n  layout(location = 2) out vec4 normalOut;\n  void main () {\n    StandardSurface s; surf(s);\n    albedoOut = s.albedo;\n    normalOut = vec4(float32x3_to_oct(s.normal), s.roughness, s.metallic);\n    emissiveOut = vec4(s.emissive, s.occlusion);\n  }\n#endif"}, "glsl3": {"vert": "\nprecision mediump float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(std140) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\n#if !CC_USE_ACCURATE_FOG\nout mediump float v_fog_factor;\n#endif\nvoid CC_TRANSFER_FOG(vec4 pos) {\n#if !CC_USE_ACCURATE_FOG\n    CC_TRANSFER_FOG_BASE(pos, v_fog_factor);\n#endif\n}\nout highp vec4 v_shadowPos;\nlayout(std140) uniform CCShadow {\n  highp mat4 cc_matLightView;\n  highp mat4 cc_matLightViewProj;\n  highp vec4 cc_shadowInvProjDepthInfo;\n  highp vec4 cc_shadowProjDepthInfo;\n  highp vec4 cc_shadowProjInfo;\n  mediump vec4 cc_shadowNFLSInfo;\n  mediump vec4 cc_shadowWHPBInfo;\n  mediump vec4 cc_shadowLPNNInfo;\n  lowp vec4 cc_shadowColor;\n  mediump vec4 cc_planarNDInfo;\n};\n#if CC_SUPPORT_CASCADED_SHADOW_MAP\n  layout(std140) uniform CCCSM {\n    highp vec4 cc_csmViewDir0[4];\n    highp vec4 cc_csmViewDir1[4];\n    highp vec4 cc_csmViewDir2[4];\n    highp vec4 cc_csmAtlas[4];\n    highp mat4 cc_matCSMViewProj[4];\n    highp vec4 cc_csmProjDepthInfo[4];\n    highp vec4 cc_csmProjInfo[4];\n    highp vec4 cc_csmSplitsInfo;\n  };\n#endif\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\n#if CC_RECEIVE_SHADOW\n  uniform highp sampler2D cc_shadowMap;\n  uniform highp sampler2D cc_spotShadowMap;\n    #define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\n  #if CC_SUPPORT_CASCADED_SHADOW_MAP\n  #else\n  #endif\n#endif\n#if CC_RECEIVE_SHADOW\n#endif\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\n#if CC_RECEIVE_SHADOW\n  out vec2 v_shadowBias;\n#endif\nout highp vec3 v_position;\nout mediump vec3 v_normal;\nout mediump vec2 uvw;\nout mediump vec2 uv0;\nout mediump vec2 uv1;\nout mediump vec2 uv2;\nout mediump vec2 uv3;\nout mediump vec3 luv;\nout mediump vec3 diffuse;\nlayout(std140) uniform TexCoords {\n  vec4 UVScale;\n  vec4 lightMapUVParam;\n};\nvoid main () {\n  vec3 worldPos;\n  worldPos.x = cc_matWorld[3][0] + a_position.x;\n  worldPos.y = cc_matWorld[3][1] + a_position.y;\n  worldPos.z = cc_matWorld[3][2] + a_position.z;\n  vec4 pos = vec4(worldPos, 1.0);\n  pos = cc_matViewProj * pos;\n  uvw = a_texCoord;\n  uv0 = a_position.xz * UVScale.x;\n  uv1 = a_position.xz * UVScale.y;\n  uv2 = a_position.xz * UVScale.z;\n  uv3 = a_position.xz * UVScale.w;\n  #if CC_USE_LIGHTMAP\n    luv.xy = cc_lightingMapUVParam.xy + a_texCoord * cc_lightingMapUVParam.z;\n    luv.z = cc_lightingMapUVParam.w;\n  #endif\n  v_position = worldPos;\n  v_normal = a_normal;\n  CC_TRANSFER_FOG(vec4(worldPos, 1.0));\n  #if CC_RECEIVE_SHADOW\n    v_shadowBias = vec2(0.0, 0.0);\n  #endif\n  v_shadowPos = cc_matLightViewProj * vec4(worldPos, 1.0);\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nvec3 SRGBToLinear (vec3 gamma) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return gamma;\n    }\n  #endif\n#endif\n  return gamma * gamma;\n}\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nlayout(std140) uniform CCShadow {\n  highp mat4 cc_matLightView;\n  highp mat4 cc_matLightViewProj;\n  highp vec4 cc_shadowInvProjDepthInfo;\n  highp vec4 cc_shadowProjDepthInfo;\n  highp vec4 cc_shadowProjInfo;\n  mediump vec4 cc_shadowNFLSInfo;\n  mediump vec4 cc_shadowWHPBInfo;\n  mediump vec4 cc_shadowLPNNInfo;\n  lowp vec4 cc_shadowColor;\n  mediump vec4 cc_planarNDInfo;\n};\n#if CC_SUPPORT_CASCADED_SHADOW_MAP\n  layout(std140) uniform CCCSM {\n    highp vec4 cc_csmViewDir0[4];\n    highp vec4 cc_csmViewDir1[4];\n    highp vec4 cc_csmViewDir2[4];\n    highp vec4 cc_csmAtlas[4];\n    highp mat4 cc_matCSMViewProj[4];\n    highp vec4 cc_csmProjDepthInfo[4];\n    highp vec4 cc_csmProjInfo[4];\n    highp vec4 cc_csmSplitsInfo;\n  };\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nvec2 GetPlanarReflectScreenUV(vec3 worldPos, mat4 matVirtualCameraViewProj, float flipNDCSign, vec3 viewDir, vec3 reflectDir)\n{\n  vec4 clipPos = matVirtualCameraViewProj * vec4(worldPos, 1.0);\n  vec2 screenUV = clipPos.xy / clipPos.w * 0.5 + 0.5;\n  screenUV = vec2(1.0 - screenUV.x, screenUV.y);\n  screenUV = flipNDCSign == 1.0 ? vec2(screenUV.x, 1.0 - screenUV.y) : screenUV;\n  return screenUV;\n}\nfloat GetLinearDepthFromViewSpace(vec3 viewPos, float near, float far) {\n  float dist = length(viewPos);\n  return (dist - near) / (far - near);\n}\nvec3 RotationVecFromAxisY(vec3 v, float cosTheta, float sinTheta)\n{\n    vec3 result;\n    result.x = dot(v, vec3(cosTheta, 0.0, -sinTheta));\n    result.y = v.y;\n    result.z = dot(v, vec3(sinTheta, 0.0,  cosTheta));\n    return result;\n}\nvec3 RotationVecFromAxisY(vec3 v, float rotateAngleArc)\n{\n  return RotationVecFromAxisY(v, cos(rotateAngleArc), sin(rotateAngleArc));\n}\nfloat CCGetLinearDepth(vec3 worldPos, float viewSpaceBias) {\n\tvec4 viewPos = cc_matLightView * vec4(worldPos.xyz, 1.0);\n  viewPos.z += viewSpaceBias;\n\treturn GetLinearDepthFromViewSpace(viewPos.xyz, cc_shadowNFLSInfo.x, cc_shadowNFLSInfo.y);\n}\nfloat CCGetLinearDepth(vec3 worldPos) {\n\treturn CCGetLinearDepth(worldPos, 0.0);\n}\n#if CC_RECEIVE_SHADOW\n  uniform highp sampler2D cc_shadowMap;\n  uniform highp sampler2D cc_spotShadowMap;\n    #define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\n    highp float unpackHighpData (float mainPart, float modPart) {\n      highp float data = mainPart;\n      return data + modPart;\n    }\n    void packHighpData (out float mainPart, out float modPart, highp float data) {\n      mainPart = fract(data);\n      modPart = data - mainPart;\n    }\n    highp float unpackHighpData (float mainPart, float modPart, const float modValue) {\n      highp float data = mainPart * modValue;\n      return data + modPart * modValue;\n    }\n    void packHighpData (out float mainPart, out float modPart, highp float data, const float modValue) {\n      highp float divide = data / modValue;\n      mainPart = floor(divide);\n      modPart = (data - mainPart * modValue) / modValue;\n    }\n    highp vec2 unpackHighpData (vec2 mainPart, vec2 modPart) {\n      highp vec2 data = mainPart;\n      return data + modPart;\n    }\n    void packHighpData (out vec2 mainPart, out vec2 modPart, highp vec2 data) {\n      mainPart = fract(data);\n      modPart = data - mainPart;\n    }\n    highp vec2 unpackHighpData (vec2 mainPart, vec2 modPart, const float modValue) {\n      highp vec2 data = mainPart * modValue;\n      return data + modPart * modValue;\n    }\n    void packHighpData (out vec2 mainPart, out vec2 modPart, highp vec2 data, const float modValue) {\n      highp vec2 divide = data / modValue;\n      mainPart = floor(divide);\n      modPart = (data - mainPart * modValue) / modValue;\n    }\n    highp vec3 unpackHighpData (vec3 mainPart, vec3 modPart) {\n      highp vec3 data = mainPart;\n      return data + modPart;\n    }\n    void packHighpData (out vec3 mainPart, out vec3 modPart, highp vec3 data) {\n      mainPart = fract(data);\n      modPart = data - mainPart;\n    }\n    highp vec3 unpackHighpData (vec3 mainPart, vec3 modPart, const float modValue) {\n      highp vec3 data = mainPart * modValue;\n      return data + modPart * modValue;\n    }\n    void packHighpData (out vec3 mainPart, out vec3 modPart, highp vec3 data, const float modValue) {\n      highp vec3 divide = data / modValue;\n      mainPart = floor(divide);\n      modPart = (data - mainPart * modValue) / modValue;\n    }\n    highp vec4 unpackHighpData (vec4 mainPart, vec4 modPart) {\n      highp vec4 data = mainPart;\n      return data + modPart;\n    }\n    void packHighpData (out vec4 mainPart, out vec4 modPart, highp vec4 data) {\n      mainPart = fract(data);\n      modPart = data - mainPart;\n    }\n    highp vec4 unpackHighpData (vec4 mainPart, vec4 modPart, const float modValue) {\n      highp vec4 data = mainPart * modValue;\n      return data + modPart * modValue;\n    }\n    void packHighpData (out vec4 mainPart, out vec4 modPart, highp vec4 data, const float modValue) {\n      highp vec4 divide = data / modValue;\n      mainPart = floor(divide);\n      modPart = (data - mainPart * modValue) / modValue;\n    }\n  vec4 shadowTexure(highp sampler2D shadowMap, vec2 coord) {\n      #if defined(CC_USE_WGPU)\n          return textureLod(shadowMap, coord, 0.0);\n      #else\n        return texture(shadowMap, coord);\n      #endif\n  }\n  float NativePCFShadowFactorHard (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    #if CC_SHADOWMAP_FORMAT == 1\n      return step(shadowNDCPos.z, dot(shadowTexure(shadowMap, shadowNDCPos.xy), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      return step(shadowNDCPos.z, shadowTexure(shadowMap, shadowNDCPos.xy).x);\n    #endif\n  }\n  float NativePCFShadowFactorSoft (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    vec2 shadowNDCPos_offset = shadowNDCPos.xy + oneTap;\n    float block0, block1, block2, block3;\n    #if CC_SHADOWMAP_FORMAT == 1\n      block0 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos_offset.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      block0 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)).x);\n      block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos.y)).x);\n      block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset.y)).x);\n      block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos_offset.y)).x);\n    #endif\n    float coefX   = mod(shadowNDCPos.x, oneTap.x) * shadowMapResolution.x;\n    float resultX = mix(block0, block1, coefX);\n    float resultY = mix(block2, block3, coefX);\n    float coefY   = mod(shadowNDCPos.y, oneTap.y) * shadowMapResolution.y;\n    return mix(resultX, resultY, coefY);\n  }\n  float NativePCFShadowFactorSoft3X (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    float shadowNDCPos_offset_L = shadowNDCPos.x - oneTap.x;\n    float shadowNDCPos_offset_R = shadowNDCPos.x + oneTap.x;\n    float shadowNDCPos_offset_U = shadowNDCPos.y - oneTap.y;\n    float shadowNDCPos_offset_D = shadowNDCPos.y + oneTap.y;\n    float block0, block1, block2, block3, block4, block5, block6, block7, block8;\n    #if CC_SHADOWMAP_FORMAT == 1\n      block0 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block4 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block5 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block6 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block7 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block8 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      block0 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_U)).x);\n      block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_U)).x);\n      block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_U)).x);\n      block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos.y)).x);\n      block4 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)).x);\n      block5 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos.y)).x);\n      block6 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_D)).x);\n      block7 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_D)).x);\n      block8 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_D)).x);\n    #endif\n    float coefX = mod(shadowNDCPos.x, oneTap.x) * shadowMapResolution.x;\n    float coefY = mod(shadowNDCPos.y, oneTap.y) * shadowMapResolution.y;\n    float shadow = 0.0;\n    float resultX = mix(block0, block1, coefX);\n    float resultY = mix(block3, block4, coefX);\n    shadow += mix(resultX , resultY, coefY);\n    resultX = mix(block1, block2, coefX);\n    resultY = mix(block4, block5, coefX);\n    shadow += mix(resultX , resultY, coefY);\n    resultX = mix(block3, block4, coefX);\n    resultY = mix(block6, block7, coefX);\n    shadow += mix(resultX, resultY, coefY);\n    resultX = mix(block4, block5, coefX);\n    resultY = mix(block7, block8, coefX);\n    shadow += mix(resultX, resultY, coefY);\n    return shadow * 0.25;\n  }\n  float NativePCFShadowFactorSoft5X (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    vec2 twoTap = oneTap * 2.0;\n    vec2 offset1 = shadowNDCPos.xy + vec2(-twoTap.x, -twoTap.y);\n    vec2 offset2 = shadowNDCPos.xy + vec2(-oneTap.x, -twoTap.y);\n    vec2 offset3 = shadowNDCPos.xy + vec2(0.0, -twoTap.y);\n    vec2 offset4 = shadowNDCPos.xy + vec2(oneTap.x, -twoTap.y);\n    vec2 offset5 = shadowNDCPos.xy + vec2(twoTap.x, -twoTap.y);\n    vec2 offset6 = shadowNDCPos.xy + vec2(-twoTap.x, -oneTap.y);\n    vec2 offset7 = shadowNDCPos.xy + vec2(-oneTap.x, -oneTap.y);\n    vec2 offset8 = shadowNDCPos.xy + vec2(0.0, -oneTap.y);\n    vec2 offset9 = shadowNDCPos.xy + vec2(oneTap.x, -oneTap.y);\n    vec2 offset10 = shadowNDCPos.xy + vec2(twoTap.x, -oneTap.y);\n    vec2 offset11 = shadowNDCPos.xy + vec2(-twoTap.x, 0.0);\n    vec2 offset12 = shadowNDCPos.xy + vec2(-oneTap.x, 0.0);\n    vec2 offset13 = shadowNDCPos.xy + vec2(0.0, 0.0);\n    vec2 offset14 = shadowNDCPos.xy + vec2(oneTap.x, 0.0);\n    vec2 offset15 = shadowNDCPos.xy + vec2(twoTap.x, 0.0);\n    vec2 offset16 = shadowNDCPos.xy + vec2(-twoTap.x, oneTap.y);\n    vec2 offset17 = shadowNDCPos.xy + vec2(-oneTap.x, oneTap.y);\n    vec2 offset18 = shadowNDCPos.xy + vec2(0.0, oneTap.y);\n    vec2 offset19 = shadowNDCPos.xy + vec2(oneTap.x, oneTap.y);\n    vec2 offset20 = shadowNDCPos.xy + vec2(twoTap.x, oneTap.y);\n    vec2 offset21 = shadowNDCPos.xy + vec2(-twoTap.x, twoTap.y);\n    vec2 offset22 = shadowNDCPos.xy + vec2(-oneTap.x, twoTap.y);\n    vec2 offset23 = shadowNDCPos.xy + vec2(0.0, twoTap.y);\n    vec2 offset24 = shadowNDCPos.xy + vec2(oneTap.x, twoTap.y);\n    vec2 offset25 = shadowNDCPos.xy + vec2(twoTap.x, twoTap.y);\n    float block1, block2, block3, block4, block5, block6, block7, block8, block9, block10, block11, block12, block13, block14, block15, block16, block17, block18, block19, block20, block21, block22, block23, block24, block25;\n    #if CC_SHADOWMAP_FORMAT == 1\n      block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset1), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset2), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset3), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block4 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset4), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block5 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset5), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block6 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset6), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block7 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset7), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block8 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset8), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block9 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset9), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block10 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset10), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block11 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset11), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block12 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset12), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block13 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset13), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block14 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset14), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block15 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset15), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block16 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset16), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block17 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset17), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block18 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset18), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block19 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset19), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block20 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset20), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block21 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset21), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block22 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset22), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block23 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset23), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block24 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset24), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block25 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset25), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset1).x);\n      block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset2).x);\n      block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset3).x);\n      block4 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset4).x);\n      block5 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset5).x);\n      block6 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset6).x);\n      block7 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset7).x);\n      block8 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset8).x);\n      block9 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset9).x);\n      block10 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset10).x);\n      block11 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset11).x);\n      block12 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset12).x);\n      block13 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset13).x);\n      block14 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset14).x);\n      block15 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset15).x);\n      block16 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset16).x);\n      block17 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset17).x);\n      block18 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset18).x);\n      block19 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset19).x);\n      block20 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset20).x);\n      block21 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset21).x);\n      block22 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset22).x);\n      block23 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset23).x);\n      block24 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset24).x);\n      block25 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset25).x);\n    #endif\n    vec2 coef = fract(shadowNDCPos.xy * shadowMapResolution);\n    vec2 v1X1 = mix(vec2(block1, block6), vec2(block2, block7), coef.xx);\n    vec2 v1X2 = mix(vec2(block2, block7), vec2(block3, block8), coef.xx);\n    vec2 v1X3 = mix(vec2(block3, block8), vec2(block4, block9), coef.xx);\n    vec2 v1X4 = mix(vec2(block4, block9), vec2(block5, block10), coef.xx);\n    float v1 = mix(v1X1.x, v1X1.y, coef.y) + mix(v1X2.x, v1X2.y, coef.y) + mix(v1X3.x, v1X3.y, coef.y) + mix(v1X4.x, v1X4.y, coef.y);\n    vec2 v2X1 = mix(vec2(block6, block11), vec2(block7, block12), coef.xx);\n    vec2 v2X2 = mix(vec2(block7, block12), vec2(block8, block13), coef.xx);\n    vec2 v2X3 = mix(vec2(block8, block13), vec2(block9, block14), coef.xx);\n    vec2 v2X4 = mix(vec2(block9, block14), vec2(block10, block15), coef.xx);\n    float v2 = mix(v2X1.x, v2X1.y, coef.y) + mix(v2X2.x, v2X2.y, coef.y) + mix(v2X3.x, v2X3.y, coef.y) + mix(v2X4.x, v2X4.y, coef.y);\n    vec2 v3X1 = mix(vec2(block11, block16), vec2(block12, block17), coef.xx);\n    vec2 v3X2 = mix(vec2(block12, block17), vec2(block13, block18), coef.xx);\n    vec2 v3X3 = mix(vec2(block13, block18), vec2(block14, block19), coef.xx);\n    vec2 v3X4 = mix(vec2(block14, block19), vec2(block15, block20), coef.xx);\n    float v3 = mix(v3X1.x, v3X1.y, coef.y) + mix(v3X2.x, v3X2.y, coef.y) + mix(v3X3.x, v3X3.y, coef.y) + mix(v3X4.x, v3X4.y, coef.y);\n    vec2 v4X1 = mix(vec2(block16, block21), vec2(block17, block22), coef.xx);\n    vec2 v4X2 = mix(vec2(block17, block22), vec2(block18, block23), coef.xx);\n    vec2 v4X3 = mix(vec2(block18, block23), vec2(block19, block24), coef.xx);\n    vec2 v4X4 = mix(vec2(block19, block24), vec2(block20, block25), coef.xx);\n    float v4 = mix(v4X1.x, v4X1.y, coef.y) + mix(v4X2.x, v4X2.y, coef.y) + mix(v4X3.x, v4X3.y, coef.y) + mix(v4X4.x, v4X4.y, coef.y);\n    float fAvg = (v1 + v2 + v3 + v4) * 0.0625;\n    return fAvg;\n  }\n  bool GetShadowNDCPos(out vec3 shadowNDCPos, vec4 shadowPosWithDepthBias)\n  {\n  \tshadowNDCPos = shadowPosWithDepthBias.xyz / shadowPosWithDepthBias.w * 0.5 + 0.5;\n  \tif (shadowNDCPos.x < 0.0 || shadowNDCPos.x > 1.0 ||\n  \t\tshadowNDCPos.y < 0.0 || shadowNDCPos.y > 1.0 ||\n  \t\tshadowNDCPos.z < 0.0 || shadowNDCPos.z > 1.0) {\n  \t\treturn false;\n  \t}\n  \tshadowNDCPos.xy = cc_cameraPos.w == 1.0 ? vec2(shadowNDCPos.xy.x, 1.0 - shadowNDCPos.xy.y) : shadowNDCPos.xy;\n  \treturn true;\n  }\n  vec4 ApplyShadowDepthBias_FaceNormal(vec4 shadowPos, vec3 worldNormal, float normalBias, vec3 matViewDir0, vec3 matViewDir1, vec3 matViewDir2, vec2 projScaleXY)\n  {\n    vec4 newShadowPos = shadowPos;\n    if (normalBias > EPSILON_LOWP)\n    {\n      vec3 viewNormal = vec3(dot(matViewDir0, worldNormal), dot(matViewDir1, worldNormal), dot(matViewDir2, worldNormal));\n      if (viewNormal.z < 0.1)\n        newShadowPos.xy += viewNormal.xy * projScaleXY * normalBias * clamp(viewNormal.z, 0.001, 0.1);\n    }\n    return newShadowPos;\n  }\n  vec4 ApplyShadowDepthBias_FaceNormal(vec4 shadowPos, vec3 worldNormal, float normalBias, mat4 matLightView, vec2 projScaleXY)\n  {\n  \tvec4 newShadowPos = shadowPos;\n  \tif (normalBias > EPSILON_LOWP)\n  \t{\n  \t\tvec4 viewNormal = matLightView * vec4(worldNormal, 0.0);\n  \t\tif (viewNormal.z < 0.1)\n  \t\t\tnewShadowPos.xy += viewNormal.xy * projScaleXY * normalBias * clamp(viewNormal.z, 0.001, 0.1);\n  \t}\n  \treturn newShadowPos;\n  }\n  float GetViewSpaceDepthFromNDCDepth_Orthgraphic(float NDCDepth, float projScaleZ, float projBiasZ)\n  {\n  \treturn (NDCDepth - projBiasZ) / projScaleZ;\n  }\n  float GetViewSpaceDepthFromNDCDepth_Perspective(float NDCDepth, float homogenousDividW, float invProjScaleZ, float invProjBiasZ)\n  {\n  \treturn NDCDepth * invProjScaleZ + homogenousDividW * invProjBiasZ;\n  }\n  vec4 ApplyShadowDepthBias_Perspective(vec4 shadowPos, float viewspaceDepthBias)\n  {\n  \tvec3 viewSpacePos;\n  \tviewSpacePos.xy = shadowPos.xy * cc_shadowProjInfo.zw;\n  \tviewSpacePos.z = GetViewSpaceDepthFromNDCDepth_Perspective(shadowPos.z, shadowPos.w, cc_shadowInvProjDepthInfo.x, cc_shadowInvProjDepthInfo.y);\n  \tviewSpacePos.xyz += cc_shadowProjDepthInfo.z * normalize(viewSpacePos.xyz) * viewspaceDepthBias;\n  \tvec4 clipSpacePos;\n  \tclipSpacePos.xy = viewSpacePos.xy * cc_shadowProjInfo.xy;\n  \tclipSpacePos.zw = viewSpacePos.z * cc_shadowProjDepthInfo.xz + vec2(cc_shadowProjDepthInfo.y, 0.0);\n  \t#if CC_SHADOWMAP_USE_LINEAR_DEPTH\n  \t\tclipSpacePos.z = GetLinearDepthFromViewSpace(viewSpacePos.xyz, cc_shadowNFLSInfo.x, cc_shadowNFLSInfo.y);\n  \t\tclipSpacePos.z = (clipSpacePos.z * 2.0 - 1.0) * clipSpacePos.w;\n  \t#endif\n  \treturn clipSpacePos;\n  }\n  vec4 ApplyShadowDepthBias_Orthographic(vec4 shadowPos, float viewspaceDepthBias, float projScaleZ, float projBiasZ)\n  {\n  \tfloat coeffA = projScaleZ;\n  \tfloat coeffB = projBiasZ;\n  \tfloat viewSpacePos_z = GetViewSpaceDepthFromNDCDepth_Orthgraphic(shadowPos.z, projScaleZ, projBiasZ);\n  \tviewSpacePos_z += viewspaceDepthBias;\n  \tvec4 result = shadowPos;\n  \tresult.z = viewSpacePos_z * coeffA + coeffB;\n  \treturn result;\n  }\n  vec4 ApplyShadowDepthBias_PerspectiveLinearDepth(vec4 shadowPos, float viewspaceDepthBias, vec3 worldPos)\n  {\n    shadowPos.z = CCGetLinearDepth(worldPos, viewspaceDepthBias) * 2.0 - 1.0;\n    shadowPos.z *= shadowPos.w;\n    return shadowPos;\n  }\n  float CCGetDirLightShadowFactorHard (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorHard(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetDirLightShadowFactorSoft (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetDirLightShadowFactorSoft3X (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft3X(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetDirLightShadowFactorSoft5X (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft5X(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorHard (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorHard(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorSoft (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorSoft3X (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft3X(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorSoft5X (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft5X(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCSpotShadowFactorBase(out vec4 shadowPosWithDepthBias, vec4 shadowPos, vec3 worldPos, vec2 shadowBias)\n  {\n    float pcf = cc_shadowWHPBInfo.z;\n    vec4 pos = vec4(1.0);\n    #if CC_SHADOWMAP_USE_LINEAR_DEPTH\n      pos = ApplyShadowDepthBias_PerspectiveLinearDepth(shadowPos, shadowBias.x, worldPos);\n    #else\n      pos = ApplyShadowDepthBias_Perspective(shadowPos, shadowBias.x);\n    #endif\n    float realtimeShadow = 1.0;\n    if (pcf > 2.9) {\n      realtimeShadow = CCGetSpotLightShadowFactorSoft5X(pos, worldPos);\n    }else if (pcf > 1.9) {\n      realtimeShadow = CCGetSpotLightShadowFactorSoft3X(pos, worldPos);\n    }else if (pcf > 0.9) {\n      realtimeShadow = CCGetSpotLightShadowFactorSoft(pos, worldPos);\n    }else {\n      realtimeShadow = CCGetSpotLightShadowFactorHard(pos, worldPos);\n    }\n    shadowPosWithDepthBias = pos;\n    return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n  }\n  float CCShadowFactorBase(out vec4 shadowPosWithDepthBias, vec4 shadowPos, vec3 N, vec2 shadowBias)\n  {\n    vec4 pos = ApplyShadowDepthBias_FaceNormal(shadowPos, N, shadowBias.y, cc_matLightView, cc_shadowProjInfo.xy);\n    pos = ApplyShadowDepthBias_Orthographic(pos, shadowBias.x, cc_shadowProjDepthInfo.x, cc_shadowProjDepthInfo.y);\n    float realtimeShadow = 1.0;\n    #if CC_DIR_SHADOW_PCF_TYPE == 3\n      realtimeShadow = CCGetDirLightShadowFactorSoft5X(pos);\n    #endif\n    #if CC_DIR_SHADOW_PCF_TYPE == 2\n      realtimeShadow =  CCGetDirLightShadowFactorSoft3X(pos);\n    #endif\n    #if CC_DIR_SHADOW_PCF_TYPE == 1\n      realtimeShadow = CCGetDirLightShadowFactorSoft(pos);\n    #endif\n    #if CC_DIR_SHADOW_PCF_TYPE == 0\n      realtimeShadow = CCGetDirLightShadowFactorHard(pos);\n    #endif\n    shadowPosWithDepthBias = pos;\n    return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n  }\n  #if CC_SUPPORT_CASCADED_SHADOW_MAP\n    bool CCGetCSMLevelWithTransition(out highp float ratio, vec3 clipPos) {\n      highp float maxRange = 1.0 - cc_csmSplitsInfo.x;\n      highp float minRange = cc_csmSplitsInfo.x;\n      highp float thresholdInvert = 1.0 / cc_csmSplitsInfo.x;\n      ratio = 0.0;\n      if (clipPos.x <= minRange) {\n        ratio = clipPos.x * thresholdInvert;\n        return true;\n      }\n      if (clipPos.x >= maxRange) {\n        ratio = 1.0 - (clipPos.x - maxRange) * thresholdInvert;\n        return true;\n      }\n      if (clipPos.y <= minRange) {\n        ratio = clipPos.y  * thresholdInvert;\n        return true;\n      }\n      if (clipPos.y >= maxRange) {\n        ratio = 1.0 - (clipPos.y - maxRange) * thresholdInvert;\n        return true;\n      }\n      return false;\n    }\n    bool CCHasCSMLevel(int level, vec3 worldPos) {\n      highp float layerThreshold = cc_csmViewDir0[0].w;\n      bool hasLevel = false;\n      for (int i = 0; i < 4; i++) {\n        if (i == level) {\n          vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n          if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n              clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n              clipPos.z >= 0.0 && clipPos.z <= 1.0) {\n            hasLevel = true;\n          }\n        }\n      }\n      return hasLevel;\n    }\n    void CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos, int level) {\n      highp float layerThreshold = cc_csmViewDir0[0].w;\n      for (int i = 0; i < 4; i++) {\n        vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n        vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n        if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n            clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n            clipPos.z >= 0.0 && clipPos.z <= 1.0 && i == level) {\n          csmPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          csmPos.xy = csmPos.xy * cc_csmAtlas[i].xy + cc_csmAtlas[i].zw;\n          shadowProjDepthInfo = cc_csmProjDepthInfo[i];\n          shadowProjInfo = cc_csmProjInfo[i];\n          shadowViewDir0 = cc_csmViewDir0[i].xyz;\n          shadowViewDir1 = cc_csmViewDir1[i].xyz;\n          shadowViewDir2 = cc_csmViewDir2[i].xyz;\n        }\n      }\n    }\n    int CCGetCSMLevel(out bool isTransitionArea, out highp float transitionRatio, out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos)\n    {\n      int level = -1;\n      highp float layerThreshold = cc_csmViewDir0[0].w;\n      for (int i = 0; i < 4; i++) {\n        vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n        vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n        if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n            clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n            clipPos.z >= 0.0 && clipPos.z <= 1.0 && level < 0) {\n          #if CC_CASCADED_LAYERS_TRANSITION\n            isTransitionArea = CCGetCSMLevelWithTransition(transitionRatio, clipPos);\n          #endif\n          csmPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          csmPos.xy = csmPos.xy * cc_csmAtlas[i].xy + cc_csmAtlas[i].zw;\n          shadowProjDepthInfo = cc_csmProjDepthInfo[i];\n          shadowProjInfo = cc_csmProjInfo[i];\n          shadowViewDir0 = cc_csmViewDir0[i].xyz;\n          shadowViewDir1 = cc_csmViewDir1[i].xyz;\n          shadowViewDir2 = cc_csmViewDir2[i].xyz;\n          level = i;\n        }\n      }\n      return level;\n    }\n    int CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos)\n    {\n      bool isTransitionArea = false;\n      highp float transitionRatio = 0.0;\n      return CCGetCSMLevel(isTransitionArea, transitionRatio, csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n    }\n    float CCCSMFactorBase(out vec4 csmPos, out vec4 csmPosWithBias, vec3 worldPos, vec3 N, vec2 shadowBias)\n    {\n      bool isTransitionArea = false;\n      highp float ratio = 0.0;\n      csmPos = vec4(1.0);\n      vec4 shadowProjDepthInfo, shadowProjInfo;\n      vec3 shadowViewDir0, shadowViewDir1, shadowViewDir2;\n      int level = -1;\n      #if CC_CASCADED_LAYERS_TRANSITION\n        level = CCGetCSMLevel(isTransitionArea, ratio, csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n      #else\n        level = CCGetCSMLevel(csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n      #endif\n      if (level < 0) { return 1.0; }\n      vec4 pos = ApplyShadowDepthBias_FaceNormal(csmPos, N, shadowBias.y, shadowViewDir0, shadowViewDir1, shadowViewDir2, shadowProjInfo.xy);\n      pos = ApplyShadowDepthBias_Orthographic(pos, shadowBias.x, shadowProjDepthInfo.x, shadowProjDepthInfo.y);\n      csmPosWithBias = pos;\n      float realtimeShadow = 1.0;\n      #if CC_DIR_SHADOW_PCF_TYPE == 3\n        realtimeShadow = CCGetDirLightShadowFactorSoft5X(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 2\n        realtimeShadow = CCGetDirLightShadowFactorSoft3X(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 1\n        realtimeShadow = CCGetDirLightShadowFactorSoft(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 0\n        realtimeShadow = CCGetDirLightShadowFactorHard(pos);\n      #endif\n      #if CC_CASCADED_LAYERS_TRANSITION\n        vec4 nextCSMPos = vec4(1.0);\n        vec4 nextShadowProjDepthInfo, nextShadowProjInfo;\n        vec3 nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2;\n        float nextRealtimeShadow = 1.0;\n        CCGetCSMLevel(nextCSMPos, nextShadowProjDepthInfo, nextShadowProjInfo, nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2, worldPos, level + 1);\n        bool hasNextLevel = CCHasCSMLevel(level + 1, worldPos);\n        if (hasNextLevel && isTransitionArea) {\n          vec4 nexPos = ApplyShadowDepthBias_FaceNormal(nextCSMPos, N, shadowBias.y, nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2, nextShadowProjInfo.xy);\n          nexPos = ApplyShadowDepthBias_Orthographic(nexPos, shadowBias.x, nextShadowProjDepthInfo.x, nextShadowProjDepthInfo.y);\n          #if CC_DIR_SHADOW_PCF_TYPE == 3\n            nextRealtimeShadow = CCGetDirLightShadowFactorSoft5X(nexPos);\n          #endif\n          #if CC_DIR_SHADOW_PCF_TYPE == 2\n            nextRealtimeShadow = CCGetDirLightShadowFactorSoft3X(nexPos);\n          #endif\n          #if CC_DIR_SHADOW_PCF_TYPE == 1\n            nextRealtimeShadow = CCGetDirLightShadowFactorSoft(nexPos);\n          #endif\n          #if CC_DIR_SHADOW_PCF_TYPE == 0\n            nextRealtimeShadow = CCGetDirLightShadowFactorHard(nexPos);\n          #endif\n          return mix(mix(nextRealtimeShadow, realtimeShadow, ratio), 1.0, cc_shadowNFLSInfo.w);\n        }\n        return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n      #else\n        return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n      #endif\n    }\n  #else\n    int CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos) {\n      return -1;\n    }\n    float CCCSMFactorBase(out vec4 csmPos, out vec4 csmPosWithBias, vec3 worldPos, vec3 N, vec2 shadowBias) {\n      csmPos = cc_matLightViewProj * vec4(worldPos, 1.0);\n      return CCShadowFactorBase(csmPosWithBias, csmPos, N, shadowBias);\n    }\n  #endif\n  float CCShadowFactorBase(vec4 shadowPos, vec3 N, vec2 shadowBias) {\n    vec4 shadowPosWithDepthBias;\n    return CCShadowFactorBase(shadowPosWithDepthBias, shadowPos, N, shadowBias);\n  }\n  float CCCSMFactorBase(vec3 worldPos, vec3 N, vec2 shadowBias) {\n    vec4 csmPos, csmPosWithBias;\n    return CCCSMFactorBase(csmPos, csmPosWithBias, worldPos, N, shadowBias);\n  }\n  float CCSpotShadowFactorBase(vec4 shadowPos, vec3 worldPos, vec2 shadowBias)\n  {\n    vec4 shadowPosWithDepthBias;\n    return CCSpotShadowFactorBase(shadowPosWithDepthBias, shadowPos, worldPos, shadowBias);\n  }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\nvec4 packRGBE (vec3 rgb) {\n  highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n  highp float e = 128.0;\n  if (maxComp > 0.0001) {\n    e = log(maxComp) / log(1.1);\n    e = ceil(e);\n    e = clamp(e + 128.0, 0.0, 255.0);\n  }\n  highp float sc = 1.0 / pow(1.1, e - 128.0);\n  vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n  vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n  return vec4(encode_rounded, e) / 255.0;\n}\nvec3 unpackRGBE (vec4 rgbe) {\n  return rgbe.rgb * pow(1.1, rgbe.a * 255.0 - 128.0);\n}\nvec4 fragTextureLod (sampler2D tex, vec2 coord, float lod) {\n    return textureLod(tex, coord, lod);\n}\nvec4 fragTextureLod (samplerCube tex, vec3 coord, float lod) {\n      return textureLod(tex, coord, lod);\n}\nuniform samplerCube cc_environment;\nvec3 CalculateReflectDirection(vec3 N, vec3 V, float NoV)\n{\n  float sideSign = NoV < 0.0 ? -1.0 : 1.0;\n  N *= sideSign;\n  return reflect(-V, N);\n}\nvec3 CalculatePlanarReflectPositionOnPlane(vec3 N, vec3 V, vec3 worldPos, vec4 plane, vec3 cameraPos, float probeReflectedDepth)\n{\n  float distPixelToPlane = -dot(plane, vec4(worldPos, 1.0));\n  plane.w += distPixelToPlane;\n  float distCameraToPlane = abs(-dot(plane, vec4(cameraPos, 1.0)));\n  vec3 planeN = plane.xyz;\n  vec3 virtualCameraPos = cameraPos - 2.0 * distCameraToPlane * planeN;\n  vec3 bumpedR = normalize(reflect(-V, N));\n  vec3 reflectedPointPos = worldPos + probeReflectedDepth * bumpedR;\n  vec3 virtualCameraToReflectedPoint = normalize(reflectedPointPos - virtualCameraPos);\n  float y = distCameraToPlane / max(EPSILON_LOWP, dot(planeN, virtualCameraToReflectedPoint));\n  return virtualCameraPos + y * virtualCameraToReflectedPoint;\n}\nvec4 CalculateBoxProjectedDirection(vec3 R, vec3 worldPos, vec3 cubeCenterPos, vec3 cubeBoxHalfSize)\n{\n  vec3 W = worldPos - cubeCenterPos;\n  vec3 projectedLength = (sign(R) * cubeBoxHalfSize - W) / (R + vec3(EPSILON));\n  float len = min(min(projectedLength.x, projectedLength.y), projectedLength.z);\n  vec3 P = W + len * R;\n  float weight = len < 0.0 ? 0.0 : 1.0;\n  return vec4(P, weight);\n}\n#if CC_USE_IBL\n  #if CC_USE_DIFFUSEMAP\n    uniform samplerCube cc_diffuseMap;\n  #endif\n#endif\n#if CC_USE_REFLECTION_PROBE\n  uniform samplerCube cc_reflectionProbeCubemap;\n  uniform sampler2D cc_reflectionProbePlanarMap;\n  uniform sampler2D cc_reflectionProbeDataMap;\n  layout(std140) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n  vec4 GetTexData(sampler2D dataMap, float dataMapWidth, float x, float uv_y)\n  {\n    return vec4(\n        decode32(texture(dataMap, vec2(((x + 0.5)/dataMapWidth), uv_y))),\n        decode32(texture(dataMap, vec2(((x + 1.5)/dataMapWidth), uv_y))),\n        decode32(texture(dataMap, vec2(((x + 2.5)/dataMapWidth), uv_y))),\n        decode32(texture(dataMap, vec2(((x + 3.5)/dataMapWidth), uv_y)))\n      );\n  }\n  void GetPlanarReflectionProbeData(out vec4 plane, out float planarReflectionDepthScale, out float mipCount, float probeId)\n  {\n      #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData1 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 0.0, uv_y);\n        vec4 texData2 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 4.0, uv_y);\n        plane.xyz = texData1.xyz;\n        plane.w = texData2.x;\n        planarReflectionDepthScale = texData2.y;\n        mipCount = texData2.z;\n      #else\n        plane = cc_reflectionProbeData1;\n        planarReflectionDepthScale = cc_reflectionProbeData2.x;\n        mipCount = cc_reflectionProbeData2.w;\n      #endif\n  }\n  void GetCubeReflectionProbeData(out vec3 centerPos, out vec3 boxHalfSize, out float mipCount, float probeId)\n  {\n      #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData1 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 0.0, uv_y);\n        vec4 texData2 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 4.0, uv_y);\n        vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n        centerPos = texData1.xyz;\n        boxHalfSize = texData2.xyz;\n        mipCount = texData3.x;\n      #else\n        centerPos = cc_reflectionProbeData1.xyz;\n        boxHalfSize = cc_reflectionProbeData2.xyz;\n        mipCount = cc_reflectionProbeData2.w;\n      #endif\n      if (mipCount > 1000.0) mipCount -= 1000.0;\n  }\n  bool isReflectProbeUsingRGBE(float probeId)\n  {\n    #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n        return texData3.x > 1000.0;\n    #else\n      return cc_reflectionProbeData2.w > 1000.0;\n    #endif\n  }\n#endif\n#if CC_USE_LIGHT_PROBE\n#if CC_USE_LIGHT_PROBE\n  #if USE_INSTANCING\n    in mediump vec4 v_sh_linear_const_r;\n    in mediump vec4 v_sh_linear_const_g;\n    in mediump vec4 v_sh_linear_const_b;\n  #else\n    layout(std140) uniform CCSH {\n      vec4 cc_sh_linear_const_r;\n      vec4 cc_sh_linear_const_g;\n      vec4 cc_sh_linear_const_b;\n      vec4 cc_sh_quadratic_r;\n      vec4 cc_sh_quadratic_g;\n      vec4 cc_sh_quadratic_b;\n      vec4 cc_sh_quadratic_a;\n    };\n  #endif\n  #if CC_USE_LIGHT_PROBE\n  vec3 SHEvaluate(vec3 normal)\n  {\n      vec3 result;\n  #if USE_INSTANCING\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(v_sh_linear_const_r, normal4);\n      result.g = dot(v_sh_linear_const_g, normal4);\n      result.b = dot(v_sh_linear_const_b, normal4);\n  #else\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(cc_sh_linear_const_r, normal4);\n      result.g = dot(cc_sh_linear_const_g, normal4);\n      result.b = dot(cc_sh_linear_const_b, normal4);\n      vec4 n14 = normal.xyzz * normal.yzzx;\n      float n5 = normal.x * normal.x - normal.y * normal.y;\n      result.r += dot(cc_sh_quadratic_r, n14);\n      result.g += dot(cc_sh_quadratic_g, n14);\n      result.b += dot(cc_sh_quadratic_b, n14);\n      result += (cc_sh_quadratic_a.rgb * n5);\n  #endif\n    #if CC_USE_HDR\n      result *= cc_exposure.w * cc_exposure.x;\n    #endif\n    return result;\n  }\n  #endif\n#endif\n#endif\nfloat GGXMobile (float roughness, float NoH, vec3 H, vec3 N) {\n  vec3 NxH = cross(N, H);\n  float OneMinusNoHSqr = dot(NxH, NxH);\n  float a = roughness * roughness;\n  float n = NoH * a;\n  float p = a / max(EPSILON, OneMinusNoHSqr + n * n);\n  return p * p;\n}\nfloat CalcSpecular (float roughness, float NoH, vec3 H, vec3 N) {\n  return (roughness * 0.25 + 0.25) * GGXMobile(roughness, NoH, H, N);\n}\nvec3 BRDFApprox (vec3 specular, float roughness, float NoV) {\n  const vec4 c0 = vec4(-1.0, -0.0275, -0.572, 0.022);\n  const vec4 c1 = vec4(1.0, 0.0425, 1.04, -0.04);\n  vec4 r = roughness * c0 + c1;\n  float a004 = min(r.x * r.x, exp2(-9.28 * NoV)) * r.x + r.y;\n  vec2 AB = vec2(-1.04, 1.04) * a004 + r.zw;\n  AB.y *= clamp(50.0 * specular.g, 0.0, 1.0);\n  return max(vec3(0.0), specular * AB.x + AB.y);\n}\n#if USE_REFLECTION_DENOISE\n  vec3 GetEnvReflectionWithMipFiltering(vec3 R, float roughness, float mipCount, float denoiseIntensity, vec2 screenUV) {\n    #if CC_USE_IBL\n    \tfloat mip = roughness * (mipCount - 1.0);\n    \tfloat delta = (dot(dFdx(R), dFdy(R))) * 1000.0;\n    \tfloat mipBias = mix(0.0, 5.0, clamp(delta, 0.0, 1.0));\n      #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_CUBE\n        vec4 biased = fragTextureLod(cc_reflectionProbeCubemap, R, mip + mipBias);\n     \t  vec4 filtered = texture(cc_reflectionProbeCubemap, R);\n      #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_PLANAR\n        vec4 biased = fragTextureLod(cc_reflectionProbePlanarMap, screenUV, mip + mipBias);\n        vec4 filtered = texture(cc_reflectionProbePlanarMap, screenUV);\n      #else\n        vec4 biased = fragTextureLod(cc_environment, R, mip + mipBias);\n     \t  vec4 filtered = texture(cc_environment, R);\n      #endif\n      #if CC_USE_IBL == 2 || CC_USE_REFLECTION_PROBE != REFLECTION_PROBE_TYPE_NONE\n        biased.rgb = unpackRGBE(biased);\n      \tfiltered.rgb = unpackRGBE(filtered);\n      #else\n      \tbiased.rgb = SRGBToLinear(biased.rgb);\n      \tfiltered.rgb = SRGBToLinear(filtered.rgb);\n      #endif\n      return mix(biased.rgb, filtered.rgb, denoiseIntensity);\n    #else\n      return vec3(0.0, 0.0, 0.0);\n    #endif\n  }\n#endif\nstruct StandardSurface {\n  vec4 albedo;\n    #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n    vec3 position, position_fract_part;\n    #else\n    vec3 position;\n    #endif\n  vec3 normal;\n  vec3 emissive;\n  vec4 lightmap;\n  float lightmap_test;\n  float roughness;\n  float metallic;\n  float occlusion;\n  float specularIntensity;\n  #if CC_RECEIVE_SHADOW\n    vec2 shadowBias;\n  #endif\n  #if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n    float reflectionProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n    float reflectionProbeBlendId;\n    float reflectionProbeBlendFactor;\n  #endif\n};\n vec3 SampleReflectionProbe(samplerCube tex, vec3 R, float roughness, float mipCount, bool isRGBE) {\n    vec4 envmap = fragTextureLod(tex, R, roughness * (mipCount - 1.0));\n    if (isRGBE)\n      return unpackRGBE(envmap);\n    else\n      return SRGBToLinear(envmap.rgb);\n  }\nvec4 CCStandardShadingBase (StandardSurface s, vec4 shadowPos) {\n  vec3 diffuse = s.albedo.rgb * (1.0 - s.metallic);\n  vec3 specular = mix(vec3(0.08 * s.specularIntensity), s.albedo.rgb, s.metallic);\n  vec3 position;\n    #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n    position = unpackHighpData(s.position, s.position_fract_part);\n    #else\n    position = s.position;\n    #endif\n  vec3 N = normalize(s.normal);\n  vec3 V = normalize(cc_cameraPos.xyz - position);\n  vec3 L = normalize(-cc_mainLitDir.xyz);\n  float NL = max(dot(N, L), 0.0);\n  float shadow = 1.0;\n  #if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n    if (NL > 0.0 && cc_mainLitDir.w > 0.0) {\n      #if CC_DIR_LIGHT_SHADOW_TYPE == 2\n        shadow = CCCSMFactorBase(position, N, s.shadowBias);\n      #endif\n      #if CC_DIR_LIGHT_SHADOW_TYPE == 1\n        shadow = CCShadowFactorBase(shadowPos, N, s.shadowBias);\n      #endif\n    }\n  #endif\n  vec3 finalColor = vec3(0.0);\n  #if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n    vec3 lightmap = s.lightmap.rgb;\n    #if CC_USE_HDR\n        lightmap.rgb *= cc_exposure.w * cc_exposure.x;\n    #endif\n    #if CC_USE_LIGHTMAP == LIGHT_MAP_TYPE_INDIRECT_OCCLUSION\n      shadow *= s.lightmap.a;\n      finalColor += diffuse * lightmap.rgb;\n    #else\n      finalColor += diffuse * lightmap.rgb * shadow;\n    #endif\n    s.occlusion *= s.lightmap_test;\n  #endif\n  #if !CC_DISABLE_DIRECTIONAL_LIGHT\n    float NV = max(abs(dot(N, V)), 0.0);\n    specular = BRDFApprox(specular, s.roughness, NV);\n    vec3 H = normalize(L + V);\n    float NH = max(dot(N, H), 0.0);\n    vec3 lightingColor = NL * cc_mainLitColor.rgb * cc_mainLitColor.w;\n    vec3 diffuseContrib = diffuse / PI;\n    vec3 specularContrib = specular * CalcSpecular(s.roughness, NH, H, N);\n    vec3 dirlightContrib = (diffuseContrib + specularContrib);\n    dirlightContrib *= shadow;\n    finalColor += lightingColor * dirlightContrib;\n  #endif\n  float fAmb = max(EPSILON, 0.5 - N.y * 0.5);\n  vec3 ambDiff = mix(cc_ambientSky.rgb, cc_ambientGround.rgb, fAmb);\n  vec3 env = vec3(0.0), rotationDir;\n  #if CC_USE_IBL\n    #if CC_USE_DIFFUSEMAP && !CC_USE_LIGHT_PROBE\n      rotationDir = RotationVecFromAxisY(N.xyz, cc_surfaceTransform.z, cc_surfaceTransform.w);\n      vec4 diffuseMap = texture(cc_diffuseMap, rotationDir);\n      #if CC_USE_DIFFUSEMAP == 2\n        ambDiff = unpackRGBE(diffuseMap);\n      #else\n        ambDiff = SRGBToLinear(diffuseMap.rgb);\n      #endif\n    #endif\n    #if !CC_USE_REFLECTION_PROBE\n      vec3 R = normalize(reflect(-V, N));\n      rotationDir = RotationVecFromAxisY(R.xyz, cc_surfaceTransform.z, cc_surfaceTransform.w);\n      #if USE_REFLECTION_DENOISE && !CC_IBL_CONVOLUTED\n        env = GetEnvReflectionWithMipFiltering(rotationDir, s.roughness, cc_ambientGround.w, 0.6, vec2(0.0));\n      #else\n        vec4 envmap = fragTextureLod(cc_environment, rotationDir, s.roughness * (cc_ambientGround.w - 1.0));\n        #if CC_USE_IBL == 2\n          env = unpackRGBE(envmap);\n        #else\n          env = SRGBToLinear(envmap.rgb);\n        #endif\n      #endif\n    #endif\n  #endif\n  float lightIntensity = cc_ambientSky.w;\n  #if CC_USE_REFLECTION_PROBE\n    vec4 probe = vec4(0.0);\n    vec3 R = normalize(reflect(-V, N));\n    #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_CUBE\n      if(s.reflectionProbeId < 0.0){\n        env = SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2);\n      }else{\n        vec3 centerPos, boxHalfSize;\n        float mipCount;\n        GetCubeReflectionProbeData(centerPos, boxHalfSize, mipCount, s.reflectionProbeId);\n        vec4 fixedR = CalculateBoxProjectedDirection(R, position, centerPos, boxHalfSize);\n        env = mix(SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2) * lightIntensity,\n          SampleReflectionProbe(cc_reflectionProbeCubemap, fixedR.xyz, s.roughness, mipCount, isReflectProbeUsingRGBE(s.reflectionProbeId)), fixedR.w);\n      }\n    #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_PLANAR\n      if(s.reflectionProbeId < 0.0){\n        vec2 screenUV = GetPlanarReflectScreenUV(s.position, cc_matViewProj, cc_cameraPos.w, V, R);\n        probe = fragTextureLod(cc_reflectionProbePlanarMap, screenUV, 1.0);\n      }else{\n        vec4 plane;\n        float planarReflectionDepthScale, mipCount;\n        GetPlanarReflectionProbeData(plane, planarReflectionDepthScale, mipCount, s.reflectionProbeId);\n        R = normalize(CalculateReflectDirection(N, V, max(abs(dot(N, V)), 0.0)));\n        vec3 worldPosOffset = CalculatePlanarReflectPositionOnPlane(N, V, s.position, plane, cc_cameraPos.xyz, planarReflectionDepthScale);\n        vec2 screenUV = GetPlanarReflectScreenUV(worldPosOffset, cc_matViewProj, cc_cameraPos.w, V, R);\n        probe = fragTextureLod(cc_reflectionProbePlanarMap, screenUV, mipCount);\n      }\n      env = unpackRGBE(probe);\n    #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n      if (s.reflectionProbeId < 0.0) {\n        env = SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2);\n      } else {\n        vec3 centerPos, boxHalfSize;\n        float mipCount;\n        GetCubeReflectionProbeData(centerPos, boxHalfSize, mipCount, s.reflectionProbeId);\n        vec4 fixedR = CalculateBoxProjectedDirection(R, s.position, centerPos, boxHalfSize);\n        env = SampleReflectionProbe(cc_reflectionProbeCubemap, fixedR.xyz, s.roughness, mipCount, isReflectProbeUsingRGBE(s.reflectionProbeId));\n        if (s.reflectionProbeBlendId < 0.0) {\n          vec3 skyBoxEnv = SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2) * lightIntensity;\n          #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n            env = mix(env, skyBoxEnv, s.reflectionProbeBlendFactor);\n          #else\n            env = mix(skyBoxEnv, env, fixedR.w);\n          #endif\n        }\n      }\n    #endif\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    lightIntensity = s.reflectionProbeId < 0.0 ? lightIntensity : 1.0;\n  #endif\n  finalColor += env * lightIntensity * specular * s.occlusion;\n#if CC_USE_LIGHT_PROBE\n  finalColor += SHEvaluate(N) * diffuse * s.occlusion;\n#endif\n  finalColor += ambDiff.rgb * cc_ambientSky.w * diffuse * s.occlusion;\n  finalColor += s.emissive;\n  return vec4(finalColor, s.albedo.a);\n}\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec4 CCFragOutput (vec4 color) {\n  #if CC_USE_RGBE_OUTPUT\n    color = packRGBE(color.rgb);\n  #elif !CC_USE_FLOAT_OUTPUT\n    #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    color.rgb = LinearToSRGB(color.rgb);\n  #endif\n  return color;\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\nvoid CC_APPLY_FOG_BASE(inout vec4 color, float factor) {\n\tcolor = vec4(mix(cc_fogColor.rgb, color.rgb, factor), color.a);\n}\n#if !CC_USE_ACCURATE_FOG\nin mediump float v_fog_factor;\n#endif\nvoid CC_APPLY_FOG(inout vec4 color) {\n#if !CC_USE_ACCURATE_FOG\n    CC_APPLY_FOG_BASE(color, v_fog_factor);\n#endif\n}\nvoid CC_APPLY_FOG(inout vec4 color, vec3 worldPos) {\n#if CC_USE_ACCURATE_FOG\n    float factor;\n    CC_TRANSFER_FOG_BASE(vec4(worldPos, 1.0), factor);\n#else\n    float factor = v_fog_factor;\n#endif\n    CC_APPLY_FOG_BASE(color, factor);\n}\nin highp vec4 v_shadowPos;\n#if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  in vec3 v_luv;\n  uniform sampler2D cc_lightingMap;\n  void SampleAndDecodeLightMapColor(out vec3 lightmapColor, out float dirShadow, out float ao, sampler2D lightingMap, vec2 luv, float lum, vec3 worldNormal)\n  {\n  #if CC_LIGHT_MAP_VERSION > 2\n  #elif CC_LIGHT_MAP_VERSION > 1\n  \tvec4 dataLow = texture(lightingMap, luv);\n  \tvec4 dataHigh = texture(lightingMap, luv + vec2(0.5, 0.0));\n  \tlightmapColor.xyz = dataLow.xyz + dataHigh.xyz * 0.00392156862745098;\n      lightmapColor.rgb *= lum;\n  \tdirShadow = dataLow.a;\n  \tao = dataHigh.a;\n  #else\n      vec4 lightmap = texture(lightingMap, luv);\n      lightmapColor = lightmap.rgb * lum;\n  \tdirShadow = lightmap.a;\n  \tao = 1.0;\n  #endif\n  }\n#endif\nin highp vec3 v_position;\nin mediump vec3 v_normal;\n#if CC_RECEIVE_SHADOW\n  in vec2 v_shadowBias;\n#endif\nin mediump vec2 uvw;\nin mediump vec2 uv0;\nin mediump vec2 uv1;\nin mediump vec2 uv2;\nin mediump vec2 uv3;\nin mediump vec3 diffuse;\nin mediump vec3 luv;\nlayout(std140) uniform PbrParams {\n  vec4 metallic;\n  vec4 roughness;\n};\nuniform sampler2D weightMap;\nuniform sampler2D detailMap0;\nuniform sampler2D detailMap1;\nuniform sampler2D detailMap2;\nuniform sampler2D detailMap3;\nuniform sampler2D normalMap0;\nuniform sampler2D normalMap1;\nuniform sampler2D normalMap2;\nuniform sampler2D normalMap3;\nvoid surf (out StandardSurface s) {\n  #if LAYERS > 1\n    vec4 w = texture(weightMap, uvw);\n  #endif\n  vec4 baseColor = vec4(0, 0, 0, 0);\n  #if LAYERS == 1\n    baseColor = texture(detailMap0, uv0);\n  #elif LAYERS == 2\n    baseColor += texture(detailMap0, uv0) * w.r;\n    baseColor += texture(detailMap1, uv1) * w.g;\n  #elif LAYERS == 3\n    baseColor += texture(detailMap0, uv0) * w.r;\n    baseColor += texture(detailMap1, uv1) * w.g;\n    baseColor += texture(detailMap2, uv2) * w.b;\n  #elif LAYERS == 4\n    baseColor += texture(detailMap0, uv0) * w.r;\n    baseColor += texture(detailMap1, uv1) * w.g;\n    baseColor += texture(detailMap2, uv2) * w.b;\n    baseColor += texture(detailMap3, uv3) * w.a;\n  #else\n    baseColor = texture(detailMap0, uv0);\n  #endif\n    #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n    packHighpData(s.position, s.position_fract_part, v_position);\n    #else\n    s.position = v_position;\n    #endif\n  #if USE_NORMALMAP\n    vec4 baseNormal = vec4(0, 0, 0, 0);\n    #if LAYERS == 1\n      baseNormal = texture(normalMap0, uv0);\n    #elif LAYERS == 2\n      baseNormal += texture(normalMap0, uv0) * w.r;\n      baseNormal += texture(normalMap1, uv1) * w.g;\n    #elif LAYERS == 3\n      baseNormal += texture(normalMap0, uv0) * w.r;\n      baseNormal += texture(normalMap1, uv1) * w.g;\n      baseNormal += texture(normalMap2, uv2) * w.b;\n    #elif LAYERS == 4\n      baseNormal += texture(normalMap0, uv0) * w.r;\n      baseNormal += texture(normalMap1, uv1) * w.g;\n      baseNormal += texture(normalMap2, uv2) * w.b;\n      baseNormal += texture(normalMap3, uv3) * w.a;\n    #else\n      baseNormal = texture(normalMap0, uv0);\n    #endif\n    vec3 tangent = vec3(1.0, 0.0, 0.0);\n    vec3 binormal = vec3(0.0, 0.0, 1.0);\n    binormal = cross(tangent, v_normal);\n    tangent = cross(v_normal, binormal);\n    vec3 nmmp = baseNormal.xyz - vec3(0.5);\n    s.normal =\n      nmmp.x * normalize(tangent) +\n      nmmp.y * normalize(binormal) +\n      nmmp.z * normalize(v_normal);\n  #else\n    s.normal = v_normal;\n  #endif\n  #if CC_RECEIVE_SHADOW\n    s.shadowBias = v_shadowBias;\n  #endif\n  s.albedo = vec4(SRGBToLinear(baseColor.rgb), 1.0);\n  s.occlusion = 1.0;\n  #if USE_PBR\n    s.roughness = 0.0;\n    #if LAYERS == 1\n      s.roughness = roughness.x;\n    #elif LAYERS == 2\n      s.roughness += roughness.x * w.r;\n      s.roughness += roughness.y * w.g;\n    #elif LAYERS == 3\n      s.roughness += roughness.x * w.r;\n      s.roughness += roughness.y * w.g;\n      s.roughness += roughness.z * w.b;\n    #elif LAYERS == 4\n      s.roughness += roughness.x * w.r;\n      s.roughness += roughness.y * w.g;\n      s.roughness += roughness.z * w.b;\n      s.roughness += roughness.w * w.a;\n    #else\n      s.roughness = 1.0;\n    #endif\n    s.specularIntensity = 0.5;\n    s.metallic = 0.0;\n    #if LAYERS == 1\n      s.specularIntensity = 0.5;\n      s.metallic = metallic.x;\n    #elif LAYERS == 2\n      s.metallic += metallic.x * w.r;\n      s.metallic += metallic.y * w.g;\n    #elif LAYERS == 3\n      s.metallic += metallic.x * w.r;\n      s.metallic += metallic.y * w.g;\n      s.metallic += metallic.z * w.b;\n    #elif LAYERS == 4\n      s.metallic += metallic.x * w.r;\n      s.metallic += metallic.y * w.g;\n      s.metallic += metallic.z * w.b;\n      s.metallic += metallic.w * w.a;\n    #else\n      s.specularIntensity = 0.5;\n      s.metallic = 0.0;\n    #endif\n  #else\n    s.roughness = 1.0;\n    s.specularIntensity = 0.5;\n    s.metallic = 0.0;\n  #endif\n  s.emissive = vec3(0.0, 0.0, 0.0);\n  #if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n    SampleAndDecodeLightMapColor(s.lightmap.rgb, s.lightmap.a, s.lightmap_test, cc_lightingMap, luv.xy, luv.z, s.normal);\n  #endif\n}\n#if CC_FORWARD_ADD\n  #if CC_PIPELINE_TYPE == 0\n    #define LIGHTS_PER_PASS 1\n  #else\n    #define LIGHTS_PER_PASS 10\n  #endif\n  #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 0\n  layout(std140) uniform CCForwardLight {\n    highp vec4 cc_lightPos[LIGHTS_PER_PASS];\n    vec4 cc_lightColor[LIGHTS_PER_PASS];\n    vec4 cc_lightSizeRangeAngle[LIGHTS_PER_PASS];\n    vec4 cc_lightDir[LIGHTS_PER_PASS];\n    vec4 cc_lightBoundingSizeVS[LIGHTS_PER_PASS];\n  };\n  #endif\n  float SmoothDistAtt (float distSqr, float invSqrAttRadius) {\n    float factor = distSqr * invSqrAttRadius;\n    float smoothFactor = clamp(1.0 - factor * factor, 0.0, 1.0);\n    return smoothFactor * smoothFactor;\n  }\n  float GetDistAtt (float distSqr, float invSqrAttRadius) {\n    float attenuation = 1.0 / max(distSqr, 0.01*0.01);\n    attenuation *= SmoothDistAtt(distSqr , invSqrAttRadius);\n    return attenuation;\n  }\n  float GetAngleAtt (vec3 L, vec3 litDir, float litAngleScale, float litAngleOffset) {\n    float cd = dot(litDir, L);\n    float attenuation = clamp(cd * litAngleScale + litAngleOffset, 0.0, 1.0);\n    return (attenuation * attenuation);\n  }\n  float GetOutOfRange (vec3 worldPos, vec3 lightPos, vec3 lookAt, vec3 right, vec3 BoundingHalfSizeVS) {\n    vec3 v = vec3(0.0);\n    vec3 up = cross(right, lookAt);\n    worldPos -= lightPos;\n    v.x = dot(worldPos, right);\n    v.y = dot(worldPos, up);\n    v.z = dot(worldPos, lookAt);\n    vec3 result = step(abs(v), BoundingHalfSizeVS);\n    return result.x * result.y * result.z;\n  }\n  #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 0\n  vec4 CCStandardShadingAdditive (StandardSurface s, vec4 shadowPos) {\n    vec3 position;\n      #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n      position = unpackHighpData(s.position, s.position_fract_part);\n      #else\n      position = s.position;\n      #endif\n    vec3 diffuse = s.albedo.rgb * (1.0 - s.metallic);\n    vec3 specular = mix(vec3(0.04), s.albedo.rgb, s.metallic);\n    vec3 diffuseContrib = diffuse / PI;\n    vec3 N = normalize(s.normal);\n    vec3 V = normalize(cc_cameraPos.xyz - position);\n    float NV = max(abs(dot(N, V)), 0.0);\n    specular = BRDFApprox(specular, s.roughness, NV);\n    vec3 finalColor = vec3(0.0);\n    int numLights = CC_PIPELINE_TYPE == 0 ? LIGHTS_PER_PASS : int(cc_lightDir[0].w);\n    for (int i = 0; i < LIGHTS_PER_PASS; i++) {\n      if (i >= numLights) break;\n      vec3 SLU = IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w) ? -cc_lightDir[i].xyz : cc_lightPos[i].xyz - position;\n      vec3 SL = normalize(SLU);\n      vec3 SH = normalize(SL + V);\n      float SNL = max(dot(N, SL), 0.0);\n      float SNH = max(dot(N, SH), 0.0);\n      vec3 lspec = specular * CalcSpecular(s.roughness, SNH, SH, N);\n      float illum = 1.0;\n      float att = 1.0;\n      if (IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w)) {\n        att = GetOutOfRange(position, cc_lightPos[i].xyz, cc_lightDir[i].xyz, cc_lightSizeRangeAngle[i].xyz, cc_lightBoundingSizeVS[i].xyz);\n      } else {\n        float distSqr = dot(SLU, SLU);\n        float litRadius = cc_lightSizeRangeAngle[i].x;\n        float litRadiusSqr = litRadius * litRadius;\n        illum = (IS_POINT_LIGHT(cc_lightPos[i].w) || IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w)) ? 1.0 : litRadiusSqr / max(litRadiusSqr, distSqr);\n        float attRadiusSqrInv = 1.0 / max(cc_lightSizeRangeAngle[i].y, 0.01);\n        attRadiusSqrInv *= attRadiusSqrInv;\n        att = GetDistAtt(distSqr, attRadiusSqrInv);\n        if (IS_SPOT_LIGHT(cc_lightPos[i].w)) {\n          float cosInner = max(dot(-cc_lightDir[i].xyz, SL), 0.01);\n          float cosOuter = cc_lightSizeRangeAngle[i].z;\n          float strength = clamp(cc_lightBoundingSizeVS[i].w, 0.0, 1.0);\n          float litAngleScale = 1.0 / max(0.001, mix(cosInner, 1.0, strength) - cosOuter);\n          float litAngleOffset = -cosOuter * litAngleScale;\n          att *= GetAngleAtt(SL, -cc_lightDir[i].xyz, litAngleScale, litAngleOffset);\n        }\n      }\n      float shadow = 1.0;\n      #if CC_RECEIVE_SHADOW  && CC_SHADOW_TYPE == 2\n        if (IS_SPOT_LIGHT(cc_lightPos[i].w) && cc_lightSizeRangeAngle[i].w > 0.0) {\n          shadow = CCSpotShadowFactorBase(shadowPos, position, s.shadowBias);\n        }\n      #endif\n      finalColor += SNL * cc_lightColor[i].rgb * shadow * cc_lightColor[i].w * illum * att * (diffuseContrib + lspec);\n    }\n    return vec4(finalColor, 0.0);\n  }\n  #endif\n  #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 1\n  layout(std430, binding = 0) readonly buffer b_ccLightsBuffer { vec4 b_ccLights[]; };\n  layout(std430, binding = 1) readonly buffer b_clusterLightIndicesBuffer { uint b_clusterLightIndices[]; };\n  layout(std430, binding = 2) readonly buffer b_clusterLightGridBuffer { uvec4 b_clusterLightGrid[]; };\n  struct CCLight\n  {\n    vec4 cc_lightPos;\n    vec4 cc_lightColor;\n    vec4 cc_lightSizeRangeAngle;\n    vec4 cc_lightDir;\n    vec4 cc_lightBoundingSizeVS;\n  };\n  struct Cluster\n  {\n    vec3 minBounds;\n    vec3 maxBounds;\n  };\n  struct LightGrid\n  {\n    uint offset;\n    uint ccLights;\n  };\n  CCLight getCCLight(uint i)\n  {\n    CCLight light;\n    light.cc_lightPos = b_ccLights[5u * i + 0u];\n    light.cc_lightColor = b_ccLights[5u * i + 1u];\n    light.cc_lightSizeRangeAngle = b_ccLights[5u * i + 2u];\n    light.cc_lightDir = b_ccLights[5u * i + 3u];\n    light.cc_lightBoundingSizeVS = b_ccLights[5u * i + 4u];\n    return light;\n  }\n  LightGrid getLightGrid(uint cluster)\n  {\n    uvec4 gridvec = b_clusterLightGrid[cluster];\n    LightGrid grid;\n    grid.offset = gridvec.x;\n    grid.ccLights = gridvec.y;\n    return grid;\n  }\n  uint getGridLightIndex(uint start, uint offset)\n  {\n    return b_clusterLightIndices[start + offset];\n  }\n  uint getClusterZIndex(vec4 worldPos)\n  {\n    float scale = float(24u) / log(cc_nearFar.y / cc_nearFar.x);\n    float bias = -(float(24u) * log(cc_nearFar.x) / log(cc_nearFar.y / cc_nearFar.x));\n    float eyeDepth = -(cc_matView * worldPos).z;\n    uint zIndex = uint(max(log(eyeDepth) * scale + bias, 0.0));\n    return zIndex;\n  }\n  uint getClusterIndex(vec4 fragCoord, vec4 worldPos)\n  {\n    uint zIndex = getClusterZIndex(worldPos);\n    float clusterSizeX = ceil(cc_viewPort.z / float(16u));\n    float clusterSizeY = ceil(cc_viewPort.w / float(8u));\n    uvec3 indices = uvec3(uvec2(fragCoord.xy / vec2(clusterSizeX, clusterSizeY)), zIndex);\n    uint cluster = (16u * 8u) * indices.z + 16u * indices.y + indices.x;\n    return cluster;\n  }\n  vec4 CCClusterShadingAdditive (StandardSurface s, vec4 shadowPos) {\n    vec3 diffuse = s.albedo.rgb * (1.0 - s.metallic);\n    vec3 specular = mix(vec3(0.04), s.albedo.rgb, s.metallic);\n    vec3 diffuseContrib = diffuse / PI;\n    vec3 position;\n      #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n      position = unpackHighpData(s.position, s.position_fract_part);\n      #else\n      position = s.position;\n      #endif\n    vec3 N = normalize(s.normal);\n    vec3 V = normalize(cc_cameraPos.xyz - position);\n    float NV = max(abs(dot(N, V)), 0.001);\n    specular = BRDFApprox(specular, s.roughness, NV);\n    vec3 finalColor = vec3(0.0);\n    uint cluster = getClusterIndex(gl_FragCoord, vec4(position, 1.0));\n    LightGrid grid = getLightGrid(cluster);\n    uint numLights = grid.ccLights;\n    for (uint i = 0u; i < 200u; i++) {\n      if (i >= numLights) break;\n      uint lightIndex = getGridLightIndex(grid.offset, i);\n      CCLight light = getCCLight(lightIndex);\n      vec3 SLU = light.cc_lightPos.xyz - position;\n      vec3 SL = normalize(SLU);\n      vec3 SH = normalize(SL + V);\n      float SNL = max(dot(N, SL), 0.001);\n      float SNH = max(dot(N, SH), 0.0);\n      float distSqr = dot(SLU, SLU);\n      float litRadius = light.cc_lightSizeRangeAngle.x;\n      float litRadiusSqr = litRadius * litRadius;\n      float illum = PI * (litRadiusSqr / max(litRadiusSqr , distSqr));\n      float attRadiusSqrInv = 1.0 / max(light.cc_lightSizeRangeAngle.y, 0.01);\n      attRadiusSqrInv *= attRadiusSqrInv;\n      float att = GetDistAtt(distSqr, attRadiusSqrInv);\n      vec3 lspec = specular * CalcSpecular(s.roughness, SNH, SH, N);\n      if (IS_SPOT_LIGHT(light.cc_lightPos.w)) {\n        float cosInner = max(dot(-light.cc_lightDir.xyz, SL), 0.01);\n        float cosOuter = light.cc_lightSizeRangeAngle.z;\n        float litAngleScale = 1.0 / max(0.001, cosInner - cosOuter);\n        float litAngleOffset = -cosOuter * litAngleScale;\n        att *= GetAngleAtt(SL, -light.cc_lightDir.xyz, litAngleScale, litAngleOffset);\n      }\n      vec3 lightColor = light.cc_lightColor.rgb;\n      float shadow = 1.0;\n      #if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n        if (IS_SPOT_LIGHT(light.cc_lightPos.w)  && light.cc_lightSizeRangeAngle.w > 0.0) {\n          shadow = CCSpotShadowFactorBase(shadowPos, position, s.shadowBias);\n        }\n      #endif\n      lightColor *= shadow;\n      finalColor += SNL * lightColor * light.cc_lightColor.w * illum * att * (diffuseContrib + lspec);\n    }\n    return vec4(finalColor, 0.0);\n  }\n  #endif\n  layout(location = 0) out vec4 fragColorX;\n  void main () {\n    StandardSurface s; surf(s);\n    #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 1\n    vec4 color = CCClusterShadingAdditive(s, v_shadowPos);\n    #else\n    vec4 color = CCStandardShadingAdditive(s, v_shadowPos);\n    #endif\n    fragColorX = CCFragOutput(color);\n  }\n#elif (CC_PIPELINE_TYPE == 0 || CC_FORCE_FORWARD_SHADING)\n  layout(location = 0) out vec4 fragColorX;\n  void main () {\n    StandardSurface s; surf(s);\n    vec4 color = CCStandardShadingBase(s, v_shadowPos);\n    #if CC_USE_FOG != 4\n      #if CC_USE_FLOAT_OUTPUT\n        CC_APPLY_FOG(color, s.position.xyz);\n      #elif !CC_FORWARD_ADD\n        CC_APPLY_FOG(color, s.position.xyz);\n      #endif\n    #endif\n    fragColorX = CCFragOutput(color);\n  }\n#elif CC_PIPELINE_TYPE == 1\n  vec2 signNotZero(vec2 v) {\n    return vec2((v.x >= 0.0) ? +1.0 : -1.0, (v.y >= 0.0) ? +1.0 : -1.0);\n  }\n  vec2 float32x3_to_oct(in vec3 v) {\n    vec2 p = v.xy * (1.0 / (abs(v.x) + abs(v.y) + abs(v.z)));\n    return (v.z <= 0.0) ? ((1.0 - abs(p.yx)) * signNotZero(p)) : p;\n  }\n  layout(location = 0) out vec4 albedoOut;\n  layout(location = 1) out vec4 emissiveOut;\n  layout(location = 2) out vec4 normalOut;\n  void main () {\n    StandardSurface s; surf(s);\n    albedoOut = s.albedo;\n    normalOut = vec4(float32x3_to_oct(s.normal), s.roughness, s.metallic);\n    emissiveOut = vec4(s.emissive, s.occlusion);\n  }\n#endif"}, "glsl1": {"vert": "\n#ifdef GL_EXT_shader_texture_lod\n#extension GL_EXT_shader_texture_lod: enable\n#endif\nprecision mediump float;\nuniform highp mat4 cc_matViewProj;\n  uniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_fogBase;\n  uniform mediump vec4 cc_fogAdd;\nuniform highp mat4 cc_matWorld;\n  uniform highp vec4 cc_lightingMapUVParam;\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\n#if !CC_USE_ACCURATE_FOG\nvarying mediump float v_fog_factor;\n#endif\nvoid CC_TRANSFER_FOG(vec4 pos) {\n#if !CC_USE_ACCURATE_FOG\n    CC_TRANSFER_FOG_BASE(pos, v_fog_factor);\n#endif\n}\nvarying highp vec4 v_shadowPos;\nuniform highp mat4 cc_matLightViewProj;\n#if CC_SUPPORT_CASCADED_SHADOW_MAP\n  #endif\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\n#if CC_RECEIVE_SHADOW\n  uniform highp sampler2D cc_shadowMap;\n  uniform highp sampler2D cc_spotShadowMap;\n    #define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\n  #if CC_SUPPORT_CASCADED_SHADOW_MAP\n  #else\n  #endif\n#endif\n#if CC_RECEIVE_SHADOW\n#endif\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\n#if CC_RECEIVE_SHADOW\n  varying vec2 v_shadowBias;\n#endif\nvarying highp vec3 v_position;\nvarying mediump vec3 v_normal;\nvarying mediump vec2 uvw;\nvarying mediump vec2 uv0;\nvarying mediump vec2 uv1;\nvarying mediump vec2 uv2;\nvarying mediump vec2 uv3;\nvarying mediump vec3 luv;\nvarying mediump vec3 diffuse;\n   uniform vec4 UVScale;\nvoid main () {\n  vec3 worldPos;\n  worldPos.x = cc_matWorld[3][0] + a_position.x;\n  worldPos.y = cc_matWorld[3][1] + a_position.y;\n  worldPos.z = cc_matWorld[3][2] + a_position.z;\n  vec4 pos = vec4(worldPos, 1.0);\n  pos = cc_matViewProj * pos;\n  uvw = a_texCoord;\n  uv0 = a_position.xz * UVScale.x;\n  uv1 = a_position.xz * UVScale.y;\n  uv2 = a_position.xz * UVScale.z;\n  uv3 = a_position.xz * UVScale.w;\n  #if CC_USE_LIGHTMAP\n    luv.xy = cc_lightingMapUVParam.xy + a_texCoord * cc_lightingMapUVParam.z;\n    luv.z = cc_lightingMapUVParam.w;\n  #endif\n  v_position = worldPos;\n  v_normal = a_normal;\n  CC_TRANSFER_FOG(vec4(worldPos, 1.0));\n  #if CC_RECEIVE_SHADOW\n    v_shadowBias = vec2(0.0, 0.0);\n  #endif\n  v_shadowPos = cc_matLightViewProj * vec4(worldPos, 1.0);\n  gl_Position = pos;\n}", "frag": "\n#ifdef GL_EXT_draw_buffers\n#extension GL_EXT_draw_buffers: enable\n#endif\n#ifdef GL_OES_standard_derivatives\n#extension GL_OES_standard_derivatives: enable\n#endif\n#ifdef GL_EXT_shader_texture_lod\n#extension GL_EXT_shader_texture_lod: enable\n#endif\nprecision highp float;\nuniform mediump vec4 cc_probeInfo;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matViewProj;\n  uniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_surfaceTransform;\n  uniform mediump vec4 cc_exposure;\n  uniform mediump vec4 cc_mainLitDir;\n  uniform mediump vec4 cc_mainLitColor;\n  uniform mediump vec4 cc_ambientSky;\n  uniform mediump vec4 cc_ambientGround;\n  uniform mediump vec4 cc_fogColor;\n  uniform mediump vec4 cc_fogBase;\n  uniform mediump vec4 cc_fogAdd;\n  uniform mediump vec4 cc_nearFar;\n  uniform mediump vec4 cc_viewPort;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nvec3 SRGBToLinear (vec3 gamma) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return gamma;\n    }\n  #endif\n#endif\n  return gamma * gamma;\n}\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nuniform highp mat4 cc_matLightView;\n  uniform highp mat4 cc_matLightViewProj;\n  uniform highp vec4 cc_shadowInvProjDepthInfo;\n  uniform highp vec4 cc_shadowProjDepthInfo;\n  uniform highp vec4 cc_shadowProjInfo;\n  uniform mediump vec4 cc_shadowNFLSInfo;\n  uniform mediump vec4 cc_shadowWHPBInfo;\n#if CC_SUPPORT_CASCADED_SHADOW_MAP\n  uniform highp vec4 cc_csmViewDir0[4];\n  uniform highp vec4 cc_csmViewDir1[4];\n  uniform highp vec4 cc_csmViewDir2[4];\n  uniform highp vec4 cc_csmAtlas[4];\n  uniform highp mat4 cc_matCSMViewProj[4];\n  uniform highp vec4 cc_csmProjDepthInfo[4];\n  uniform highp vec4 cc_csmProjInfo[4];\n  uniform highp vec4 cc_csmSplitsInfo;\n#endif\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nvec2 GetPlanarReflectScreenUV(vec3 worldPos, mat4 matVirtualCameraViewProj, float flipNDCSign, vec3 viewDir, vec3 reflectDir)\n{\n  vec4 clipPos = matVirtualCameraViewProj * vec4(worldPos, 1.0);\n  vec2 screenUV = clipPos.xy / clipPos.w * 0.5 + 0.5;\n  screenUV = vec2(1.0 - screenUV.x, screenUV.y);\n  screenUV = flipNDCSign == 1.0 ? vec2(screenUV.x, 1.0 - screenUV.y) : screenUV;\n  return screenUV;\n}\nfloat GetLinearDepthFromViewSpace(vec3 viewPos, float near, float far) {\n  float dist = length(viewPos);\n  return (dist - near) / (far - near);\n}\nvec3 RotationVecFromAxisY(vec3 v, float cosTheta, float sinTheta)\n{\n    vec3 result;\n    result.x = dot(v, vec3(cosTheta, 0.0, -sinTheta));\n    result.y = v.y;\n    result.z = dot(v, vec3(sinTheta, 0.0,  cosTheta));\n    return result;\n}\nvec3 RotationVecFromAxisY(vec3 v, float rotateAngleArc)\n{\n  return RotationVecFromAxisY(v, cos(rotateAngleArc), sin(rotateAngleArc));\n}\nfloat CCGetLinearDepth(vec3 worldPos, float viewSpaceBias) {\n\tvec4 viewPos = cc_matLightView * vec4(worldPos.xyz, 1.0);\n  viewPos.z += viewSpaceBias;\n\treturn GetLinearDepthFromViewSpace(viewPos.xyz, cc_shadowNFLSInfo.x, cc_shadowNFLSInfo.y);\n}\nfloat CCGetLinearDepth(vec3 worldPos) {\n\treturn CCGetLinearDepth(worldPos, 0.0);\n}\n#if CC_RECEIVE_SHADOW\n  uniform highp sampler2D cc_shadowMap;\n  uniform highp sampler2D cc_spotShadowMap;\n    #define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\n    highp float unpackHighpData (float mainPart, float modPart) {\n      highp float data = mainPart;\n      return data + modPart;\n    }\n    void packHighpData (out float mainPart, out float modPart, highp float data) {\n      mainPart = fract(data);\n      modPart = data - mainPart;\n    }\n    highp float unpackHighpData (float mainPart, float modPart, const float modValue) {\n      highp float data = mainPart * modValue;\n      return data + modPart * modValue;\n    }\n    void packHighpData (out float mainPart, out float modPart, highp float data, const float modValue) {\n      highp float divide = data / modValue;\n      mainPart = floor(divide);\n      modPart = (data - mainPart * modValue) / modValue;\n    }\n    highp vec2 unpackHighpData (vec2 mainPart, vec2 modPart) {\n      highp vec2 data = mainPart;\n      return data + modPart;\n    }\n    void packHighpData (out vec2 mainPart, out vec2 modPart, highp vec2 data) {\n      mainPart = fract(data);\n      modPart = data - mainPart;\n    }\n    highp vec2 unpackHighpData (vec2 mainPart, vec2 modPart, const float modValue) {\n      highp vec2 data = mainPart * modValue;\n      return data + modPart * modValue;\n    }\n    void packHighpData (out vec2 mainPart, out vec2 modPart, highp vec2 data, const float modValue) {\n      highp vec2 divide = data / modValue;\n      mainPart = floor(divide);\n      modPart = (data - mainPart * modValue) / modValue;\n    }\n    highp vec3 unpackHighpData (vec3 mainPart, vec3 modPart) {\n      highp vec3 data = mainPart;\n      return data + modPart;\n    }\n    void packHighpData (out vec3 mainPart, out vec3 modPart, highp vec3 data) {\n      mainPart = fract(data);\n      modPart = data - mainPart;\n    }\n    highp vec3 unpackHighpData (vec3 mainPart, vec3 modPart, const float modValue) {\n      highp vec3 data = mainPart * modValue;\n      return data + modPart * modValue;\n    }\n    void packHighpData (out vec3 mainPart, out vec3 modPart, highp vec3 data, const float modValue) {\n      highp vec3 divide = data / modValue;\n      mainPart = floor(divide);\n      modPart = (data - mainPart * modValue) / modValue;\n    }\n    highp vec4 unpackHighpData (vec4 mainPart, vec4 modPart) {\n      highp vec4 data = mainPart;\n      return data + modPart;\n    }\n    void packHighpData (out vec4 mainPart, out vec4 modPart, highp vec4 data) {\n      mainPart = fract(data);\n      modPart = data - mainPart;\n    }\n    highp vec4 unpackHighpData (vec4 mainPart, vec4 modPart, const float modValue) {\n      highp vec4 data = mainPart * modValue;\n      return data + modPart * modValue;\n    }\n    void packHighpData (out vec4 mainPart, out vec4 modPart, highp vec4 data, const float modValue) {\n      highp vec4 divide = data / modValue;\n      mainPart = floor(divide);\n      modPart = (data - mainPart * modValue) / modValue;\n    }\n  vec4 shadowTexure(highp sampler2D shadowMap, vec2 coord) {\n      #if defined(CC_USE_WGPU)\n          return texture2DLod(shadowMap, coord, 0.0);\n      #else\n        return texture2D(shadowMap, coord);\n      #endif\n  }\n  float NativePCFShadowFactorHard (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    #if CC_SHADOWMAP_FORMAT == 1\n      return step(shadowNDCPos.z, dot(shadowTexure(shadowMap, shadowNDCPos.xy), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      return step(shadowNDCPos.z, shadowTexure(shadowMap, shadowNDCPos.xy).x);\n    #endif\n  }\n  float NativePCFShadowFactorSoft (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    vec2 shadowNDCPos_offset = shadowNDCPos.xy + oneTap;\n    float block0, block1, block2, block3;\n    #if CC_SHADOWMAP_FORMAT == 1\n      block0 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos_offset.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      block0 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)).x);\n      block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos.y)).x);\n      block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset.y)).x);\n      block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos_offset.y)).x);\n    #endif\n    float coefX   = mod(shadowNDCPos.x, oneTap.x) * shadowMapResolution.x;\n    float resultX = mix(block0, block1, coefX);\n    float resultY = mix(block2, block3, coefX);\n    float coefY   = mod(shadowNDCPos.y, oneTap.y) * shadowMapResolution.y;\n    return mix(resultX, resultY, coefY);\n  }\n  float NativePCFShadowFactorSoft3X (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    float shadowNDCPos_offset_L = shadowNDCPos.x - oneTap.x;\n    float shadowNDCPos_offset_R = shadowNDCPos.x + oneTap.x;\n    float shadowNDCPos_offset_U = shadowNDCPos.y - oneTap.y;\n    float shadowNDCPos_offset_D = shadowNDCPos.y + oneTap.y;\n    float block0, block1, block2, block3, block4, block5, block6, block7, block8;\n    #if CC_SHADOWMAP_FORMAT == 1\n      block0 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block4 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block5 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block6 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block7 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block8 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      block0 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_U)).x);\n      block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_U)).x);\n      block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_U)).x);\n      block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos.y)).x);\n      block4 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)).x);\n      block5 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos.y)).x);\n      block6 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_D)).x);\n      block7 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_D)).x);\n      block8 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_D)).x);\n    #endif\n    float coefX = mod(shadowNDCPos.x, oneTap.x) * shadowMapResolution.x;\n    float coefY = mod(shadowNDCPos.y, oneTap.y) * shadowMapResolution.y;\n    float shadow = 0.0;\n    float resultX = mix(block0, block1, coefX);\n    float resultY = mix(block3, block4, coefX);\n    shadow += mix(resultX , resultY, coefY);\n    resultX = mix(block1, block2, coefX);\n    resultY = mix(block4, block5, coefX);\n    shadow += mix(resultX , resultY, coefY);\n    resultX = mix(block3, block4, coefX);\n    resultY = mix(block6, block7, coefX);\n    shadow += mix(resultX, resultY, coefY);\n    resultX = mix(block4, block5, coefX);\n    resultY = mix(block7, block8, coefX);\n    shadow += mix(resultX, resultY, coefY);\n    return shadow * 0.25;\n  }\n  float NativePCFShadowFactorSoft5X (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    vec2 twoTap = oneTap * 2.0;\n    vec2 offset1 = shadowNDCPos.xy + vec2(-twoTap.x, -twoTap.y);\n    vec2 offset2 = shadowNDCPos.xy + vec2(-oneTap.x, -twoTap.y);\n    vec2 offset3 = shadowNDCPos.xy + vec2(0.0, -twoTap.y);\n    vec2 offset4 = shadowNDCPos.xy + vec2(oneTap.x, -twoTap.y);\n    vec2 offset5 = shadowNDCPos.xy + vec2(twoTap.x, -twoTap.y);\n    vec2 offset6 = shadowNDCPos.xy + vec2(-twoTap.x, -oneTap.y);\n    vec2 offset7 = shadowNDCPos.xy + vec2(-oneTap.x, -oneTap.y);\n    vec2 offset8 = shadowNDCPos.xy + vec2(0.0, -oneTap.y);\n    vec2 offset9 = shadowNDCPos.xy + vec2(oneTap.x, -oneTap.y);\n    vec2 offset10 = shadowNDCPos.xy + vec2(twoTap.x, -oneTap.y);\n    vec2 offset11 = shadowNDCPos.xy + vec2(-twoTap.x, 0.0);\n    vec2 offset12 = shadowNDCPos.xy + vec2(-oneTap.x, 0.0);\n    vec2 offset13 = shadowNDCPos.xy + vec2(0.0, 0.0);\n    vec2 offset14 = shadowNDCPos.xy + vec2(oneTap.x, 0.0);\n    vec2 offset15 = shadowNDCPos.xy + vec2(twoTap.x, 0.0);\n    vec2 offset16 = shadowNDCPos.xy + vec2(-twoTap.x, oneTap.y);\n    vec2 offset17 = shadowNDCPos.xy + vec2(-oneTap.x, oneTap.y);\n    vec2 offset18 = shadowNDCPos.xy + vec2(0.0, oneTap.y);\n    vec2 offset19 = shadowNDCPos.xy + vec2(oneTap.x, oneTap.y);\n    vec2 offset20 = shadowNDCPos.xy + vec2(twoTap.x, oneTap.y);\n    vec2 offset21 = shadowNDCPos.xy + vec2(-twoTap.x, twoTap.y);\n    vec2 offset22 = shadowNDCPos.xy + vec2(-oneTap.x, twoTap.y);\n    vec2 offset23 = shadowNDCPos.xy + vec2(0.0, twoTap.y);\n    vec2 offset24 = shadowNDCPos.xy + vec2(oneTap.x, twoTap.y);\n    vec2 offset25 = shadowNDCPos.xy + vec2(twoTap.x, twoTap.y);\n    float block1, block2, block3, block4, block5, block6, block7, block8, block9, block10, block11, block12, block13, block14, block15, block16, block17, block18, block19, block20, block21, block22, block23, block24, block25;\n    #if CC_SHADOWMAP_FORMAT == 1\n      block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset1), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset2), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset3), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block4 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset4), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block5 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset5), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block6 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset6), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block7 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset7), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block8 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset8), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block9 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset9), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block10 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset10), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block11 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset11), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block12 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset12), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block13 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset13), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block14 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset14), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block15 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset15), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block16 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset16), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block17 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset17), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block18 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset18), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block19 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset19), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block20 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset20), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block21 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset21), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block22 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset22), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block23 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset23), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block24 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset24), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block25 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset25), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset1).x);\n      block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset2).x);\n      block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset3).x);\n      block4 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset4).x);\n      block5 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset5).x);\n      block6 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset6).x);\n      block7 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset7).x);\n      block8 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset8).x);\n      block9 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset9).x);\n      block10 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset10).x);\n      block11 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset11).x);\n      block12 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset12).x);\n      block13 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset13).x);\n      block14 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset14).x);\n      block15 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset15).x);\n      block16 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset16).x);\n      block17 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset17).x);\n      block18 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset18).x);\n      block19 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset19).x);\n      block20 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset20).x);\n      block21 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset21).x);\n      block22 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset22).x);\n      block23 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset23).x);\n      block24 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset24).x);\n      block25 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset25).x);\n    #endif\n    vec2 coef = fract(shadowNDCPos.xy * shadowMapResolution);\n    vec2 v1X1 = mix(vec2(block1, block6), vec2(block2, block7), coef.xx);\n    vec2 v1X2 = mix(vec2(block2, block7), vec2(block3, block8), coef.xx);\n    vec2 v1X3 = mix(vec2(block3, block8), vec2(block4, block9), coef.xx);\n    vec2 v1X4 = mix(vec2(block4, block9), vec2(block5, block10), coef.xx);\n    float v1 = mix(v1X1.x, v1X1.y, coef.y) + mix(v1X2.x, v1X2.y, coef.y) + mix(v1X3.x, v1X3.y, coef.y) + mix(v1X4.x, v1X4.y, coef.y);\n    vec2 v2X1 = mix(vec2(block6, block11), vec2(block7, block12), coef.xx);\n    vec2 v2X2 = mix(vec2(block7, block12), vec2(block8, block13), coef.xx);\n    vec2 v2X3 = mix(vec2(block8, block13), vec2(block9, block14), coef.xx);\n    vec2 v2X4 = mix(vec2(block9, block14), vec2(block10, block15), coef.xx);\n    float v2 = mix(v2X1.x, v2X1.y, coef.y) + mix(v2X2.x, v2X2.y, coef.y) + mix(v2X3.x, v2X3.y, coef.y) + mix(v2X4.x, v2X4.y, coef.y);\n    vec2 v3X1 = mix(vec2(block11, block16), vec2(block12, block17), coef.xx);\n    vec2 v3X2 = mix(vec2(block12, block17), vec2(block13, block18), coef.xx);\n    vec2 v3X3 = mix(vec2(block13, block18), vec2(block14, block19), coef.xx);\n    vec2 v3X4 = mix(vec2(block14, block19), vec2(block15, block20), coef.xx);\n    float v3 = mix(v3X1.x, v3X1.y, coef.y) + mix(v3X2.x, v3X2.y, coef.y) + mix(v3X3.x, v3X3.y, coef.y) + mix(v3X4.x, v3X4.y, coef.y);\n    vec2 v4X1 = mix(vec2(block16, block21), vec2(block17, block22), coef.xx);\n    vec2 v4X2 = mix(vec2(block17, block22), vec2(block18, block23), coef.xx);\n    vec2 v4X3 = mix(vec2(block18, block23), vec2(block19, block24), coef.xx);\n    vec2 v4X4 = mix(vec2(block19, block24), vec2(block20, block25), coef.xx);\n    float v4 = mix(v4X1.x, v4X1.y, coef.y) + mix(v4X2.x, v4X2.y, coef.y) + mix(v4X3.x, v4X3.y, coef.y) + mix(v4X4.x, v4X4.y, coef.y);\n    float fAvg = (v1 + v2 + v3 + v4) * 0.0625;\n    return fAvg;\n  }\n  bool GetShadowNDCPos(out vec3 shadowNDCPos, vec4 shadowPosWithDepthBias)\n  {\n  \tshadowNDCPos = shadowPosWithDepthBias.xyz / shadowPosWithDepthBias.w * 0.5 + 0.5;\n  \tif (shadowNDCPos.x < 0.0 || shadowNDCPos.x > 1.0 ||\n  \t\tshadowNDCPos.y < 0.0 || shadowNDCPos.y > 1.0 ||\n  \t\tshadowNDCPos.z < 0.0 || shadowNDCPos.z > 1.0) {\n  \t\treturn false;\n  \t}\n  \tshadowNDCPos.xy = cc_cameraPos.w == 1.0 ? vec2(shadowNDCPos.xy.x, 1.0 - shadowNDCPos.xy.y) : shadowNDCPos.xy;\n  \treturn true;\n  }\n  vec4 ApplyShadowDepthBias_FaceNormal(vec4 shadowPos, vec3 worldNormal, float normalBias, vec3 matViewDir0, vec3 matViewDir1, vec3 matViewDir2, vec2 projScaleXY)\n  {\n    vec4 newShadowPos = shadowPos;\n    if (normalBias > EPSILON_LOWP)\n    {\n      vec3 viewNormal = vec3(dot(matViewDir0, worldNormal), dot(matViewDir1, worldNormal), dot(matViewDir2, worldNormal));\n      if (viewNormal.z < 0.1)\n        newShadowPos.xy += viewNormal.xy * projScaleXY * normalBias * clamp(viewNormal.z, 0.001, 0.1);\n    }\n    return newShadowPos;\n  }\n  vec4 ApplyShadowDepthBias_FaceNormal(vec4 shadowPos, vec3 worldNormal, float normalBias, mat4 matLightView, vec2 projScaleXY)\n  {\n  \tvec4 newShadowPos = shadowPos;\n  \tif (normalBias > EPSILON_LOWP)\n  \t{\n  \t\tvec4 viewNormal = matLightView * vec4(worldNormal, 0.0);\n  \t\tif (viewNormal.z < 0.1)\n  \t\t\tnewShadowPos.xy += viewNormal.xy * projScaleXY * normalBias * clamp(viewNormal.z, 0.001, 0.1);\n  \t}\n  \treturn newShadowPos;\n  }\n  float GetViewSpaceDepthFromNDCDepth_Orthgraphic(float NDCDepth, float projScaleZ, float projBiasZ)\n  {\n  \treturn (NDCDepth - projBiasZ) / projScaleZ;\n  }\n  float GetViewSpaceDepthFromNDCDepth_Perspective(float NDCDepth, float homogenousDividW, float invProjScaleZ, float invProjBiasZ)\n  {\n  \treturn NDCDepth * invProjScaleZ + homogenousDividW * invProjBiasZ;\n  }\n  vec4 ApplyShadowDepthBias_Perspective(vec4 shadowPos, float viewspaceDepthBias)\n  {\n  \tvec3 viewSpacePos;\n  \tviewSpacePos.xy = shadowPos.xy * cc_shadowProjInfo.zw;\n  \tviewSpacePos.z = GetViewSpaceDepthFromNDCDepth_Perspective(shadowPos.z, shadowPos.w, cc_shadowInvProjDepthInfo.x, cc_shadowInvProjDepthInfo.y);\n  \tviewSpacePos.xyz += cc_shadowProjDepthInfo.z * normalize(viewSpacePos.xyz) * viewspaceDepthBias;\n  \tvec4 clipSpacePos;\n  \tclipSpacePos.xy = viewSpacePos.xy * cc_shadowProjInfo.xy;\n  \tclipSpacePos.zw = viewSpacePos.z * cc_shadowProjDepthInfo.xz + vec2(cc_shadowProjDepthInfo.y, 0.0);\n  \t#if CC_SHADOWMAP_USE_LINEAR_DEPTH\n  \t\tclipSpacePos.z = GetLinearDepthFromViewSpace(viewSpacePos.xyz, cc_shadowNFLSInfo.x, cc_shadowNFLSInfo.y);\n  \t\tclipSpacePos.z = (clipSpacePos.z * 2.0 - 1.0) * clipSpacePos.w;\n  \t#endif\n  \treturn clipSpacePos;\n  }\n  vec4 ApplyShadowDepthBias_Orthographic(vec4 shadowPos, float viewspaceDepthBias, float projScaleZ, float projBiasZ)\n  {\n  \tfloat coeffA = projScaleZ;\n  \tfloat coeffB = projBiasZ;\n  \tfloat viewSpacePos_z = GetViewSpaceDepthFromNDCDepth_Orthgraphic(shadowPos.z, projScaleZ, projBiasZ);\n  \tviewSpacePos_z += viewspaceDepthBias;\n  \tvec4 result = shadowPos;\n  \tresult.z = viewSpacePos_z * coeffA + coeffB;\n  \treturn result;\n  }\n  vec4 ApplyShadowDepthBias_PerspectiveLinearDepth(vec4 shadowPos, float viewspaceDepthBias, vec3 worldPos)\n  {\n    shadowPos.z = CCGetLinearDepth(worldPos, viewspaceDepthBias) * 2.0 - 1.0;\n    shadowPos.z *= shadowPos.w;\n    return shadowPos;\n  }\n  float CCGetDirLightShadowFactorHard (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorHard(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetDirLightShadowFactorSoft (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetDirLightShadowFactorSoft3X (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft3X(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetDirLightShadowFactorSoft5X (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft5X(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorHard (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorHard(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorSoft (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorSoft3X (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft3X(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorSoft5X (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft5X(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCSpotShadowFactorBase(out vec4 shadowPosWithDepthBias, vec4 shadowPos, vec3 worldPos, vec2 shadowBias)\n  {\n    float pcf = cc_shadowWHPBInfo.z;\n    vec4 pos = vec4(1.0);\n    #if CC_SHADOWMAP_USE_LINEAR_DEPTH\n      pos = ApplyShadowDepthBias_PerspectiveLinearDepth(shadowPos, shadowBias.x, worldPos);\n    #else\n      pos = ApplyShadowDepthBias_Perspective(shadowPos, shadowBias.x);\n    #endif\n    float realtimeShadow = 1.0;\n    if (pcf > 2.9) {\n      realtimeShadow = CCGetSpotLightShadowFactorSoft5X(pos, worldPos);\n    }else if (pcf > 1.9) {\n      realtimeShadow = CCGetSpotLightShadowFactorSoft3X(pos, worldPos);\n    }else if (pcf > 0.9) {\n      realtimeShadow = CCGetSpotLightShadowFactorSoft(pos, worldPos);\n    }else {\n      realtimeShadow = CCGetSpotLightShadowFactorHard(pos, worldPos);\n    }\n    shadowPosWithDepthBias = pos;\n    return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n  }\n  float CCShadowFactorBase(out vec4 shadowPosWithDepthBias, vec4 shadowPos, vec3 N, vec2 shadowBias)\n  {\n    vec4 pos = ApplyShadowDepthBias_FaceNormal(shadowPos, N, shadowBias.y, cc_matLightView, cc_shadowProjInfo.xy);\n    pos = ApplyShadowDepthBias_Orthographic(pos, shadowBias.x, cc_shadowProjDepthInfo.x, cc_shadowProjDepthInfo.y);\n    float realtimeShadow = 1.0;\n    #if CC_DIR_SHADOW_PCF_TYPE == 3\n      realtimeShadow = CCGetDirLightShadowFactorSoft5X(pos);\n    #endif\n    #if CC_DIR_SHADOW_PCF_TYPE == 2\n      realtimeShadow =  CCGetDirLightShadowFactorSoft3X(pos);\n    #endif\n    #if CC_DIR_SHADOW_PCF_TYPE == 1\n      realtimeShadow = CCGetDirLightShadowFactorSoft(pos);\n    #endif\n    #if CC_DIR_SHADOW_PCF_TYPE == 0\n      realtimeShadow = CCGetDirLightShadowFactorHard(pos);\n    #endif\n    shadowPosWithDepthBias = pos;\n    return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n  }\n  #if CC_SUPPORT_CASCADED_SHADOW_MAP\n    bool CCGetCSMLevelWithTransition(out highp float ratio, vec3 clipPos) {\n      highp float maxRange = 1.0 - cc_csmSplitsInfo.x;\n      highp float minRange = cc_csmSplitsInfo.x;\n      highp float thresholdInvert = 1.0 / cc_csmSplitsInfo.x;\n      ratio = 0.0;\n      if (clipPos.x <= minRange) {\n        ratio = clipPos.x * thresholdInvert;\n        return true;\n      }\n      if (clipPos.x >= maxRange) {\n        ratio = 1.0 - (clipPos.x - maxRange) * thresholdInvert;\n        return true;\n      }\n      if (clipPos.y <= minRange) {\n        ratio = clipPos.y  * thresholdInvert;\n        return true;\n      }\n      if (clipPos.y >= maxRange) {\n        ratio = 1.0 - (clipPos.y - maxRange) * thresholdInvert;\n        return true;\n      }\n      return false;\n    }\n    bool CCHasCSMLevel(int level, vec3 worldPos) {\n      highp float layerThreshold = cc_csmViewDir0[0].w;\n      bool hasLevel = false;\n      for (int i = 0; i < 4; i++) {\n        if (i == level) {\n          vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n          if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n              clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n              clipPos.z >= 0.0 && clipPos.z <= 1.0) {\n            hasLevel = true;\n          }\n        }\n      }\n      return hasLevel;\n    }\n    void CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos, int level) {\n      highp float layerThreshold = cc_csmViewDir0[0].w;\n      for (int i = 0; i < 4; i++) {\n        vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n        vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n        if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n            clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n            clipPos.z >= 0.0 && clipPos.z <= 1.0 && i == level) {\n          csmPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          csmPos.xy = csmPos.xy * cc_csmAtlas[i].xy + cc_csmAtlas[i].zw;\n          shadowProjDepthInfo = cc_csmProjDepthInfo[i];\n          shadowProjInfo = cc_csmProjInfo[i];\n          shadowViewDir0 = cc_csmViewDir0[i].xyz;\n          shadowViewDir1 = cc_csmViewDir1[i].xyz;\n          shadowViewDir2 = cc_csmViewDir2[i].xyz;\n        }\n      }\n    }\n    int CCGetCSMLevel(out bool isTransitionArea, out highp float transitionRatio, out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos)\n    {\n      int level = -1;\n      highp float layerThreshold = cc_csmViewDir0[0].w;\n      for (int i = 0; i < 4; i++) {\n        vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n        vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n        if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n            clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n            clipPos.z >= 0.0 && clipPos.z <= 1.0 && level < 0) {\n          #if CC_CASCADED_LAYERS_TRANSITION\n            isTransitionArea = CCGetCSMLevelWithTransition(transitionRatio, clipPos);\n          #endif\n          csmPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          csmPos.xy = csmPos.xy * cc_csmAtlas[i].xy + cc_csmAtlas[i].zw;\n          shadowProjDepthInfo = cc_csmProjDepthInfo[i];\n          shadowProjInfo = cc_csmProjInfo[i];\n          shadowViewDir0 = cc_csmViewDir0[i].xyz;\n          shadowViewDir1 = cc_csmViewDir1[i].xyz;\n          shadowViewDir2 = cc_csmViewDir2[i].xyz;\n          level = i;\n        }\n      }\n      return level;\n    }\n    int CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos)\n    {\n      bool isTransitionArea = false;\n      highp float transitionRatio = 0.0;\n      return CCGetCSMLevel(isTransitionArea, transitionRatio, csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n    }\n    float CCCSMFactorBase(out vec4 csmPos, out vec4 csmPosWithBias, vec3 worldPos, vec3 N, vec2 shadowBias)\n    {\n      bool isTransitionArea = false;\n      highp float ratio = 0.0;\n      csmPos = vec4(1.0);\n      vec4 shadowProjDepthInfo, shadowProjInfo;\n      vec3 shadowViewDir0, shadowViewDir1, shadowViewDir2;\n      int level = -1;\n      #if CC_CASCADED_LAYERS_TRANSITION\n        level = CCGetCSMLevel(isTransitionArea, ratio, csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n      #else\n        level = CCGetCSMLevel(csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n      #endif\n      if (level < 0) { return 1.0; }\n      vec4 pos = ApplyShadowDepthBias_FaceNormal(csmPos, N, shadowBias.y, shadowViewDir0, shadowViewDir1, shadowViewDir2, shadowProjInfo.xy);\n      pos = ApplyShadowDepthBias_Orthographic(pos, shadowBias.x, shadowProjDepthInfo.x, shadowProjDepthInfo.y);\n      csmPosWithBias = pos;\n      float realtimeShadow = 1.0;\n      #if CC_DIR_SHADOW_PCF_TYPE == 3\n        realtimeShadow = CCGetDirLightShadowFactorSoft5X(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 2\n        realtimeShadow = CCGetDirLightShadowFactorSoft3X(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 1\n        realtimeShadow = CCGetDirLightShadowFactorSoft(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 0\n        realtimeShadow = CCGetDirLightShadowFactorHard(pos);\n      #endif\n      #if CC_CASCADED_LAYERS_TRANSITION\n        vec4 nextCSMPos = vec4(1.0);\n        vec4 nextShadowProjDepthInfo, nextShadowProjInfo;\n        vec3 nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2;\n        float nextRealtimeShadow = 1.0;\n        CCGetCSMLevel(nextCSMPos, nextShadowProjDepthInfo, nextShadowProjInfo, nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2, worldPos, level + 1);\n        bool hasNextLevel = CCHasCSMLevel(level + 1, worldPos);\n        if (hasNextLevel && isTransitionArea) {\n          vec4 nexPos = ApplyShadowDepthBias_FaceNormal(nextCSMPos, N, shadowBias.y, nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2, nextShadowProjInfo.xy);\n          nexPos = ApplyShadowDepthBias_Orthographic(nexPos, shadowBias.x, nextShadowProjDepthInfo.x, nextShadowProjDepthInfo.y);\n          #if CC_DIR_SHADOW_PCF_TYPE == 3\n            nextRealtimeShadow = CCGetDirLightShadowFactorSoft5X(nexPos);\n          #endif\n          #if CC_DIR_SHADOW_PCF_TYPE == 2\n            nextRealtimeShadow = CCGetDirLightShadowFactorSoft3X(nexPos);\n          #endif\n          #if CC_DIR_SHADOW_PCF_TYPE == 1\n            nextRealtimeShadow = CCGetDirLightShadowFactorSoft(nexPos);\n          #endif\n          #if CC_DIR_SHADOW_PCF_TYPE == 0\n            nextRealtimeShadow = CCGetDirLightShadowFactorHard(nexPos);\n          #endif\n          return mix(mix(nextRealtimeShadow, realtimeShadow, ratio), 1.0, cc_shadowNFLSInfo.w);\n        }\n        return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n      #else\n        return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n      #endif\n    }\n  #else\n    int CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos) {\n      return -1;\n    }\n    float CCCSMFactorBase(out vec4 csmPos, out vec4 csmPosWithBias, vec3 worldPos, vec3 N, vec2 shadowBias) {\n      csmPos = cc_matLightViewProj * vec4(worldPos, 1.0);\n      return CCShadowFactorBase(csmPosWithBias, csmPos, N, shadowBias);\n    }\n  #endif\n  float CCShadowFactorBase(vec4 shadowPos, vec3 N, vec2 shadowBias) {\n    vec4 shadowPosWithDepthBias;\n    return CCShadowFactorBase(shadowPosWithDepthBias, shadowPos, N, shadowBias);\n  }\n  float CCCSMFactorBase(vec3 worldPos, vec3 N, vec2 shadowBias) {\n    vec4 csmPos, csmPosWithBias;\n    return CCCSMFactorBase(csmPos, csmPosWithBias, worldPos, N, shadowBias);\n  }\n  float CCSpotShadowFactorBase(vec4 shadowPos, vec3 worldPos, vec2 shadowBias)\n  {\n    vec4 shadowPosWithDepthBias;\n    return CCSpotShadowFactorBase(shadowPosWithDepthBias, shadowPos, worldPos, shadowBias);\n  }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\nvec4 packRGBE (vec3 rgb) {\n  highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n  highp float e = 128.0;\n  if (maxComp > 0.0001) {\n    e = log(maxComp) / log(1.1);\n    e = ceil(e);\n    e = clamp(e + 128.0, 0.0, 255.0);\n  }\n  highp float sc = 1.0 / pow(1.1, e - 128.0);\n  vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n  vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n  return vec4(encode_rounded, e) / 255.0;\n}\nvec3 unpackRGBE (vec4 rgbe) {\n  return rgbe.rgb * pow(1.1, rgbe.a * 255.0 - 128.0);\n}\nvec4 fragTextureLod (sampler2D tex, vec2 coord, float lod) {\n    #ifdef GL_EXT_shader_texture_lod\n      return texture2DLodEXT(tex, coord, lod);\n    #else\n      return texture2D(tex, coord, lod);\n    #endif\n}\nvec4 fragTextureLod (samplerCube tex, vec3 coord, float lod) {\n    #ifdef GL_EXT_shader_texture_lod\n      return textureCubeLodEXT(tex, coord, lod);\n    #else\n      return textureCube(tex, coord, lod);\n    #endif\n}\nuniform samplerCube cc_environment;\nvec3 CalculateReflectDirection(vec3 N, vec3 V, float NoV)\n{\n  float sideSign = NoV < 0.0 ? -1.0 : 1.0;\n  N *= sideSign;\n  return reflect(-V, N);\n}\nvec3 CalculatePlanarReflectPositionOnPlane(vec3 N, vec3 V, vec3 worldPos, vec4 plane, vec3 cameraPos, float probeReflectedDepth)\n{\n  float distPixelToPlane = -dot(plane, vec4(worldPos, 1.0));\n  plane.w += distPixelToPlane;\n  float distCameraToPlane = abs(-dot(plane, vec4(cameraPos, 1.0)));\n  vec3 planeN = plane.xyz;\n  vec3 virtualCameraPos = cameraPos - 2.0 * distCameraToPlane * planeN;\n  vec3 bumpedR = normalize(reflect(-V, N));\n  vec3 reflectedPointPos = worldPos + probeReflectedDepth * bumpedR;\n  vec3 virtualCameraToReflectedPoint = normalize(reflectedPointPos - virtualCameraPos);\n  float y = distCameraToPlane / max(EPSILON_LOWP, dot(planeN, virtualCameraToReflectedPoint));\n  return virtualCameraPos + y * virtualCameraToReflectedPoint;\n}\nvec4 CalculateBoxProjectedDirection(vec3 R, vec3 worldPos, vec3 cubeCenterPos, vec3 cubeBoxHalfSize)\n{\n  vec3 W = worldPos - cubeCenterPos;\n  vec3 projectedLength = (sign(R) * cubeBoxHalfSize - W) / (R + vec3(EPSILON));\n  float len = min(min(projectedLength.x, projectedLength.y), projectedLength.z);\n  vec3 P = W + len * R;\n  float weight = len < 0.0 ? 0.0 : 1.0;\n  return vec4(P, weight);\n}\n#if CC_USE_IBL\n  #if CC_USE_DIFFUSEMAP\n    uniform samplerCube cc_diffuseMap;\n  #endif\n#endif\n#if CC_USE_REFLECTION_PROBE\n  uniform samplerCube cc_reflectionProbeCubemap;\n  uniform sampler2D cc_reflectionProbePlanarMap;\n  uniform sampler2D cc_reflectionProbeDataMap;\n  uniform highp vec4 cc_reflectionProbeData1;\n  uniform highp vec4 cc_reflectionProbeData2;\n  vec4 GetTexData(sampler2D dataMap, float dataMapWidth, float x, float uv_y)\n  {\n    return vec4(\n        decode32(texture2D(dataMap, vec2(((x + 0.5)/dataMapWidth), uv_y))),\n        decode32(texture2D(dataMap, vec2(((x + 1.5)/dataMapWidth), uv_y))),\n        decode32(texture2D(dataMap, vec2(((x + 2.5)/dataMapWidth), uv_y))),\n        decode32(texture2D(dataMap, vec2(((x + 3.5)/dataMapWidth), uv_y)))\n      );\n  }\n  void GetPlanarReflectionProbeData(out vec4 plane, out float planarReflectionDepthScale, out float mipCount, float probeId)\n  {\n      #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData1 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 0.0, uv_y);\n        vec4 texData2 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 4.0, uv_y);\n        plane.xyz = texData1.xyz;\n        plane.w = texData2.x;\n        planarReflectionDepthScale = texData2.y;\n        mipCount = texData2.z;\n      #else\n        plane = cc_reflectionProbeData1;\n        planarReflectionDepthScale = cc_reflectionProbeData2.x;\n        mipCount = cc_reflectionProbeData2.w;\n      #endif\n  }\n  void GetCubeReflectionProbeData(out vec3 centerPos, out vec3 boxHalfSize, out float mipCount, float probeId)\n  {\n      #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData1 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 0.0, uv_y);\n        vec4 texData2 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 4.0, uv_y);\n        vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n        centerPos = texData1.xyz;\n        boxHalfSize = texData2.xyz;\n        mipCount = texData3.x;\n      #else\n        centerPos = cc_reflectionProbeData1.xyz;\n        boxHalfSize = cc_reflectionProbeData2.xyz;\n        mipCount = cc_reflectionProbeData2.w;\n      #endif\n      if (mipCount > 1000.0) mipCount -= 1000.0;\n  }\n  bool isReflectProbeUsingRGBE(float probeId)\n  {\n    #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n        return texData3.x > 1000.0;\n    #else\n      return cc_reflectionProbeData2.w > 1000.0;\n    #endif\n  }\n#endif\n#if CC_USE_LIGHT_PROBE\n#if CC_USE_LIGHT_PROBE\n  #if USE_INSTANCING\n    varying mediump vec4 v_sh_linear_const_r;\n    varying mediump vec4 v_sh_linear_const_g;\n    varying mediump vec4 v_sh_linear_const_b;\n  #else\n    uniform vec4 cc_sh_linear_const_r;\n  uniform vec4 cc_sh_linear_const_g;\n  uniform vec4 cc_sh_linear_const_b;\n  uniform vec4 cc_sh_quadratic_r;\n  uniform vec4 cc_sh_quadratic_g;\n  uniform vec4 cc_sh_quadratic_b;\n  uniform vec4 cc_sh_quadratic_a;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n  vec3 SHEvaluate(vec3 normal)\n  {\n      vec3 result;\n  #if USE_INSTANCING\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(v_sh_linear_const_r, normal4);\n      result.g = dot(v_sh_linear_const_g, normal4);\n      result.b = dot(v_sh_linear_const_b, normal4);\n  #else\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(cc_sh_linear_const_r, normal4);\n      result.g = dot(cc_sh_linear_const_g, normal4);\n      result.b = dot(cc_sh_linear_const_b, normal4);\n      vec4 n14 = normal.xyzz * normal.yzzx;\n      float n5 = normal.x * normal.x - normal.y * normal.y;\n      result.r += dot(cc_sh_quadratic_r, n14);\n      result.g += dot(cc_sh_quadratic_g, n14);\n      result.b += dot(cc_sh_quadratic_b, n14);\n      result += (cc_sh_quadratic_a.rgb * n5);\n  #endif\n    #if CC_USE_HDR\n      result *= cc_exposure.w * cc_exposure.x;\n    #endif\n    return result;\n  }\n  #endif\n#endif\n#endif\nfloat GGXMobile (float roughness, float NoH, vec3 H, vec3 N) {\n  vec3 NxH = cross(N, H);\n  float OneMinusNoHSqr = dot(NxH, NxH);\n  float a = roughness * roughness;\n  float n = NoH * a;\n  float p = a / max(EPSILON, OneMinusNoHSqr + n * n);\n  return p * p;\n}\nfloat CalcSpecular (float roughness, float NoH, vec3 H, vec3 N) {\n  return (roughness * 0.25 + 0.25) * GGXMobile(roughness, NoH, H, N);\n}\nvec3 BRDFApprox (vec3 specular, float roughness, float NoV) {\n  const vec4 c0 = vec4(-1.0, -0.0275, -0.572, 0.022);\n  const vec4 c1 = vec4(1.0, 0.0425, 1.04, -0.04);\n  vec4 r = roughness * c0 + c1;\n  float a004 = min(r.x * r.x, exp2(-9.28 * NoV)) * r.x + r.y;\n  vec2 AB = vec2(-1.04, 1.04) * a004 + r.zw;\n  AB.y *= clamp(50.0 * specular.g, 0.0, 1.0);\n  return max(vec3(0.0), specular * AB.x + AB.y);\n}\n#if USE_REFLECTION_DENOISE\n  vec3 GetEnvReflectionWithMipFiltering(vec3 R, float roughness, float mipCount, float denoiseIntensity, vec2 screenUV) {\n    #if CC_USE_IBL\n    \tfloat mip = roughness * (mipCount - 1.0);\n    \tfloat delta = (dot(dFdx(R), dFdy(R))) * 1000.0;\n    \tfloat mipBias = mix(0.0, 5.0, clamp(delta, 0.0, 1.0));\n      #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_CUBE\n        vec4 biased = fragTextureLod(cc_reflectionProbeCubemap, R, mip + mipBias);\n     \t  vec4 filtered = textureCube(cc_reflectionProbeCubemap, R);\n      #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_PLANAR\n        vec4 biased = fragTextureLod(cc_reflectionProbePlanarMap, screenUV, mip + mipBias);\n        vec4 filtered = texture2D(cc_reflectionProbePlanarMap, screenUV);\n      #else\n        vec4 biased = fragTextureLod(cc_environment, R, mip + mipBias);\n     \t  vec4 filtered = textureCube(cc_environment, R);\n      #endif\n      #if CC_USE_IBL == 2 || CC_USE_REFLECTION_PROBE != REFLECTION_PROBE_TYPE_NONE\n        biased.rgb = unpackRGBE(biased);\n      \tfiltered.rgb = unpackRGBE(filtered);\n      #else\n      \tbiased.rgb = SRGBToLinear(biased.rgb);\n      \tfiltered.rgb = SRGBToLinear(filtered.rgb);\n      #endif\n      return mix(biased.rgb, filtered.rgb, denoiseIntensity);\n    #else\n      return vec3(0.0, 0.0, 0.0);\n    #endif\n  }\n#endif\nstruct StandardSurface {\n  vec4 albedo;\n    #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n    vec3 position, position_fract_part;\n    #else\n    vec3 position;\n    #endif\n  vec3 normal;\n  vec3 emissive;\n  vec4 lightmap;\n  float lightmap_test;\n  float roughness;\n  float metallic;\n  float occlusion;\n  float specularIntensity;\n  #if CC_RECEIVE_SHADOW\n    vec2 shadowBias;\n  #endif\n  #if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n    float reflectionProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n    float reflectionProbeBlendId;\n    float reflectionProbeBlendFactor;\n  #endif\n};\n vec3 SampleReflectionProbe(samplerCube tex, vec3 R, float roughness, float mipCount, bool isRGBE) {\n    vec4 envmap = fragTextureLod(tex, R, roughness * (mipCount - 1.0));\n    if (isRGBE)\n      return unpackRGBE(envmap);\n    else\n      return SRGBToLinear(envmap.rgb);\n  }\nvec4 CCStandardShadingBase (StandardSurface s, vec4 shadowPos) {\n  vec3 diffuse = s.albedo.rgb * (1.0 - s.metallic);\n  vec3 specular = mix(vec3(0.08 * s.specularIntensity), s.albedo.rgb, s.metallic);\n  vec3 position;\n    #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n    position = unpackHighpData(s.position, s.position_fract_part);\n    #else\n    position = s.position;\n    #endif\n  vec3 N = normalize(s.normal);\n  vec3 V = normalize(cc_cameraPos.xyz - position);\n  vec3 L = normalize(-cc_mainLitDir.xyz);\n  float NL = max(dot(N, L), 0.0);\n  float shadow = 1.0;\n  #if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n    if (NL > 0.0 && cc_mainLitDir.w > 0.0) {\n      #if CC_DIR_LIGHT_SHADOW_TYPE == 2\n        shadow = CCCSMFactorBase(position, N, s.shadowBias);\n      #endif\n      #if CC_DIR_LIGHT_SHADOW_TYPE == 1\n        shadow = CCShadowFactorBase(shadowPos, N, s.shadowBias);\n      #endif\n    }\n  #endif\n  vec3 finalColor = vec3(0.0);\n  #if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n    vec3 lightmap = s.lightmap.rgb;\n    #if CC_USE_HDR\n        lightmap.rgb *= cc_exposure.w * cc_exposure.x;\n    #endif\n    #if CC_USE_LIGHTMAP == LIGHT_MAP_TYPE_INDIRECT_OCCLUSION\n      shadow *= s.lightmap.a;\n      finalColor += diffuse * lightmap.rgb;\n    #else\n      finalColor += diffuse * lightmap.rgb * shadow;\n    #endif\n    s.occlusion *= s.lightmap_test;\n  #endif\n  #if !CC_DISABLE_DIRECTIONAL_LIGHT\n    float NV = max(abs(dot(N, V)), 0.0);\n    specular = BRDFApprox(specular, s.roughness, NV);\n    vec3 H = normalize(L + V);\n    float NH = max(dot(N, H), 0.0);\n    vec3 lightingColor = NL * cc_mainLitColor.rgb * cc_mainLitColor.w;\n    vec3 diffuseContrib = diffuse / PI;\n    vec3 specularContrib = specular * CalcSpecular(s.roughness, NH, H, N);\n    vec3 dirlightContrib = (diffuseContrib + specularContrib);\n    dirlightContrib *= shadow;\n    finalColor += lightingColor * dirlightContrib;\n  #endif\n  float fAmb = max(EPSILON, 0.5 - N.y * 0.5);\n  vec3 ambDiff = mix(cc_ambientSky.rgb, cc_ambientGround.rgb, fAmb);\n  vec3 env = vec3(0.0), rotationDir;\n  #if CC_USE_IBL\n    #if CC_USE_DIFFUSEMAP && !CC_USE_LIGHT_PROBE\n      rotationDir = RotationVecFromAxisY(N.xyz, cc_surfaceTransform.z, cc_surfaceTransform.w);\n      vec4 diffuseMap = textureCube(cc_diffuseMap, rotationDir);\n      #if CC_USE_DIFFUSEMAP == 2\n        ambDiff = unpackRGBE(diffuseMap);\n      #else\n        ambDiff = SRGBToLinear(diffuseMap.rgb);\n      #endif\n    #endif\n    #if !CC_USE_REFLECTION_PROBE\n      vec3 R = normalize(reflect(-V, N));\n      rotationDir = RotationVecFromAxisY(R.xyz, cc_surfaceTransform.z, cc_surfaceTransform.w);\n      #if USE_REFLECTION_DENOISE && !CC_IBL_CONVOLUTED\n        env = GetEnvReflectionWithMipFiltering(rotationDir, s.roughness, cc_ambientGround.w, 0.6, vec2(0.0));\n      #else\n        vec4 envmap = fragTextureLod(cc_environment, rotationDir, s.roughness * (cc_ambientGround.w - 1.0));\n        #if CC_USE_IBL == 2\n          env = unpackRGBE(envmap);\n        #else\n          env = SRGBToLinear(envmap.rgb);\n        #endif\n      #endif\n    #endif\n  #endif\n  float lightIntensity = cc_ambientSky.w;\n  #if CC_USE_REFLECTION_PROBE\n    vec4 probe = vec4(0.0);\n    vec3 R = normalize(reflect(-V, N));\n    #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_CUBE\n      if(s.reflectionProbeId < 0.0){\n        env = SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2);\n      }else{\n        vec3 centerPos, boxHalfSize;\n        float mipCount;\n        GetCubeReflectionProbeData(centerPos, boxHalfSize, mipCount, s.reflectionProbeId);\n        vec4 fixedR = CalculateBoxProjectedDirection(R, position, centerPos, boxHalfSize);\n        env = mix(SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2) * lightIntensity,\n          SampleReflectionProbe(cc_reflectionProbeCubemap, fixedR.xyz, s.roughness, mipCount, isReflectProbeUsingRGBE(s.reflectionProbeId)), fixedR.w);\n      }\n    #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_PLANAR\n      if(s.reflectionProbeId < 0.0){\n        vec2 screenUV = GetPlanarReflectScreenUV(s.position, cc_matViewProj, cc_cameraPos.w, V, R);\n        probe = fragTextureLod(cc_reflectionProbePlanarMap, screenUV, 1.0);\n      }else{\n        vec4 plane;\n        float planarReflectionDepthScale, mipCount;\n        GetPlanarReflectionProbeData(plane, planarReflectionDepthScale, mipCount, s.reflectionProbeId);\n        R = normalize(CalculateReflectDirection(N, V, max(abs(dot(N, V)), 0.0)));\n        vec3 worldPosOffset = CalculatePlanarReflectPositionOnPlane(N, V, s.position, plane, cc_cameraPos.xyz, planarReflectionDepthScale);\n        vec2 screenUV = GetPlanarReflectScreenUV(worldPosOffset, cc_matViewProj, cc_cameraPos.w, V, R);\n        probe = fragTextureLod(cc_reflectionProbePlanarMap, screenUV, mipCount);\n      }\n      env = unpackRGBE(probe);\n    #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n      if (s.reflectionProbeId < 0.0) {\n        env = SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2);\n      } else {\n        vec3 centerPos, boxHalfSize;\n        float mipCount;\n        GetCubeReflectionProbeData(centerPos, boxHalfSize, mipCount, s.reflectionProbeId);\n        vec4 fixedR = CalculateBoxProjectedDirection(R, s.position, centerPos, boxHalfSize);\n        env = SampleReflectionProbe(cc_reflectionProbeCubemap, fixedR.xyz, s.roughness, mipCount, isReflectProbeUsingRGBE(s.reflectionProbeId));\n        if (s.reflectionProbeBlendId < 0.0) {\n          vec3 skyBoxEnv = SampleReflectionProbe(cc_environment, R, s.roughness, cc_ambientGround.w, CC_USE_IBL == 2) * lightIntensity;\n          #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n            env = mix(env, skyBoxEnv, s.reflectionProbeBlendFactor);\n          #else\n            env = mix(skyBoxEnv, env, fixedR.w);\n          #endif\n        }\n      }\n    #endif\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    lightIntensity = s.reflectionProbeId < 0.0 ? lightIntensity : 1.0;\n  #endif\n  finalColor += env * lightIntensity * specular * s.occlusion;\n#if CC_USE_LIGHT_PROBE\n  finalColor += SHEvaluate(N) * diffuse * s.occlusion;\n#endif\n  finalColor += ambDiff.rgb * cc_ambientSky.w * diffuse * s.occlusion;\n  finalColor += s.emissive;\n  return vec4(finalColor, s.albedo.a);\n}\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec4 CCFragOutput (vec4 color) {\n  #if CC_USE_RGBE_OUTPUT\n    color = packRGBE(color.rgb);\n  #elif !CC_USE_FLOAT_OUTPUT\n    #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    color.rgb = LinearToSRGB(color.rgb);\n  #endif\n  return color;\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\nvoid CC_APPLY_FOG_BASE(inout vec4 color, float factor) {\n\tcolor = vec4(mix(cc_fogColor.rgb, color.rgb, factor), color.a);\n}\n#if !CC_USE_ACCURATE_FOG\nvarying mediump float v_fog_factor;\n#endif\nvoid CC_APPLY_FOG(inout vec4 color) {\n#if !CC_USE_ACCURATE_FOG\n    CC_APPLY_FOG_BASE(color, v_fog_factor);\n#endif\n}\nvoid CC_APPLY_FOG(inout vec4 color, vec3 worldPos) {\n#if CC_USE_ACCURATE_FOG\n    float factor;\n    CC_TRANSFER_FOG_BASE(vec4(worldPos, 1.0), factor);\n#else\n    float factor = v_fog_factor;\n#endif\n    CC_APPLY_FOG_BASE(color, factor);\n}\nvarying highp vec4 v_shadowPos;\n#if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  varying vec3 v_luv;\n  uniform sampler2D cc_lightingMap;\n  void SampleAndDecodeLightMapColor(out vec3 lightmapColor, out float dirShadow, out float ao, sampler2D lightingMap, vec2 luv, float lum, vec3 worldNormal)\n  {\n  #if CC_LIGHT_MAP_VERSION > 2\n  #elif CC_LIGHT_MAP_VERSION > 1\n  \tvec4 dataLow = texture2D(lightingMap, luv);\n  \tvec4 dataHigh = texture2D(lightingMap, luv + vec2(0.5, 0.0));\n  \tlightmapColor.xyz = dataLow.xyz + dataHigh.xyz * 0.00392156862745098;\n      lightmapColor.rgb *= lum;\n  \tdirShadow = dataLow.a;\n  \tao = dataHigh.a;\n  #else\n      vec4 lightmap = texture2D(lightingMap, luv);\n      lightmapColor = lightmap.rgb * lum;\n  \tdirShadow = lightmap.a;\n  \tao = 1.0;\n  #endif\n  }\n#endif\nvarying highp vec3 v_position;\nvarying mediump vec3 v_normal;\n#if CC_RECEIVE_SHADOW\n  varying vec2 v_shadowBias;\n#endif\nvarying mediump vec2 uvw;\nvarying mediump vec2 uv0;\nvarying mediump vec2 uv1;\nvarying mediump vec2 uv2;\nvarying mediump vec2 uv3;\nvarying mediump vec3 diffuse;\nvarying mediump vec3 luv;\n   uniform vec4 metallic;\n   uniform vec4 roughness;\nuniform sampler2D weightMap;\nuniform sampler2D detailMap0;\nuniform sampler2D detailMap1;\nuniform sampler2D detailMap2;\nuniform sampler2D detailMap3;\nuniform sampler2D normalMap0;\nuniform sampler2D normalMap1;\nuniform sampler2D normalMap2;\nuniform sampler2D normalMap3;\nvoid surf (out StandardSurface s) {\n  #if LAYERS > 1\n    vec4 w = texture2D(weightMap, uvw);\n  #endif\n  vec4 baseColor = vec4(0, 0, 0, 0);\n  #if LAYERS == 1\n    baseColor = texture2D(detailMap0, uv0);\n  #elif LAYERS == 2\n    baseColor += texture2D(detailMap0, uv0) * w.r;\n    baseColor += texture2D(detailMap1, uv1) * w.g;\n  #elif LAYERS == 3\n    baseColor += texture2D(detailMap0, uv0) * w.r;\n    baseColor += texture2D(detailMap1, uv1) * w.g;\n    baseColor += texture2D(detailMap2, uv2) * w.b;\n  #elif LAYERS == 4\n    baseColor += texture2D(detailMap0, uv0) * w.r;\n    baseColor += texture2D(detailMap1, uv1) * w.g;\n    baseColor += texture2D(detailMap2, uv2) * w.b;\n    baseColor += texture2D(detailMap3, uv3) * w.a;\n  #else\n    baseColor = texture2D(detailMap0, uv0);\n  #endif\n    #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n    packHighpData(s.position, s.position_fract_part, v_position);\n    #else\n    s.position = v_position;\n    #endif\n  #if USE_NORMALMAP\n    vec4 baseNormal = vec4(0, 0, 0, 0);\n    #if LAYERS == 1\n      baseNormal = texture2D(normalMap0, uv0);\n    #elif LAYERS == 2\n      baseNormal += texture2D(normalMap0, uv0) * w.r;\n      baseNormal += texture2D(normalMap1, uv1) * w.g;\n    #elif LAYERS == 3\n      baseNormal += texture2D(normalMap0, uv0) * w.r;\n      baseNormal += texture2D(normalMap1, uv1) * w.g;\n      baseNormal += texture2D(normalMap2, uv2) * w.b;\n    #elif LAYERS == 4\n      baseNormal += texture2D(normalMap0, uv0) * w.r;\n      baseNormal += texture2D(normalMap1, uv1) * w.g;\n      baseNormal += texture2D(normalMap2, uv2) * w.b;\n      baseNormal += texture2D(normalMap3, uv3) * w.a;\n    #else\n      baseNormal = texture2D(normalMap0, uv0);\n    #endif\n    vec3 tangent = vec3(1.0, 0.0, 0.0);\n    vec3 binormal = vec3(0.0, 0.0, 1.0);\n    binormal = cross(tangent, v_normal);\n    tangent = cross(v_normal, binormal);\n    vec3 nmmp = baseNormal.xyz - vec3(0.5);\n    s.normal =\n      nmmp.x * normalize(tangent) +\n      nmmp.y * normalize(binormal) +\n      nmmp.z * normalize(v_normal);\n  #else\n    s.normal = v_normal;\n  #endif\n  #if CC_RECEIVE_SHADOW\n    s.shadowBias = v_shadowBias;\n  #endif\n  s.albedo = vec4(SRGBToLinear(baseColor.rgb), 1.0);\n  s.occlusion = 1.0;\n  #if USE_PBR\n    s.roughness = 0.0;\n    #if LAYERS == 1\n      s.roughness = roughness.x;\n    #elif LAYERS == 2\n      s.roughness += roughness.x * w.r;\n      s.roughness += roughness.y * w.g;\n    #elif LAYERS == 3\n      s.roughness += roughness.x * w.r;\n      s.roughness += roughness.y * w.g;\n      s.roughness += roughness.z * w.b;\n    #elif LAYERS == 4\n      s.roughness += roughness.x * w.r;\n      s.roughness += roughness.y * w.g;\n      s.roughness += roughness.z * w.b;\n      s.roughness += roughness.w * w.a;\n    #else\n      s.roughness = 1.0;\n    #endif\n    s.specularIntensity = 0.5;\n    s.metallic = 0.0;\n    #if LAYERS == 1\n      s.specularIntensity = 0.5;\n      s.metallic = metallic.x;\n    #elif LAYERS == 2\n      s.metallic += metallic.x * w.r;\n      s.metallic += metallic.y * w.g;\n    #elif LAYERS == 3\n      s.metallic += metallic.x * w.r;\n      s.metallic += metallic.y * w.g;\n      s.metallic += metallic.z * w.b;\n    #elif LAYERS == 4\n      s.metallic += metallic.x * w.r;\n      s.metallic += metallic.y * w.g;\n      s.metallic += metallic.z * w.b;\n      s.metallic += metallic.w * w.a;\n    #else\n      s.specularIntensity = 0.5;\n      s.metallic = 0.0;\n    #endif\n  #else\n    s.roughness = 1.0;\n    s.specularIntensity = 0.5;\n    s.metallic = 0.0;\n  #endif\n  s.emissive = vec3(0.0, 0.0, 0.0);\n  #if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n    SampleAndDecodeLightMapColor(s.lightmap.rgb, s.lightmap.a, s.lightmap_test, cc_lightingMap, luv.xy, luv.z, s.normal);\n  #endif\n}\n#if CC_FORWARD_ADD\n  #if CC_PIPELINE_TYPE == 0\n    #define LIGHTS_PER_PASS 1\n  #else\n    #define LIGHTS_PER_PASS 10\n  #endif\n  #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 0\n  uniform highp vec4 cc_lightPos[LIGHTS_PER_PASS];\n  uniform vec4 cc_lightColor[LIGHTS_PER_PASS];\n  uniform vec4 cc_lightSizeRangeAngle[LIGHTS_PER_PASS];\n  uniform vec4 cc_lightDir[LIGHTS_PER_PASS];\n  uniform vec4 cc_lightBoundingSizeVS[LIGHTS_PER_PASS];\n  #endif\n  float SmoothDistAtt (float distSqr, float invSqrAttRadius) {\n    float factor = distSqr * invSqrAttRadius;\n    float smoothFactor = clamp(1.0 - factor * factor, 0.0, 1.0);\n    return smoothFactor * smoothFactor;\n  }\n  float GetDistAtt (float distSqr, float invSqrAttRadius) {\n    float attenuation = 1.0 / max(distSqr, 0.01*0.01);\n    attenuation *= SmoothDistAtt(distSqr , invSqrAttRadius);\n    return attenuation;\n  }\n  float GetAngleAtt (vec3 L, vec3 litDir, float litAngleScale, float litAngleOffset) {\n    float cd = dot(litDir, L);\n    float attenuation = clamp(cd * litAngleScale + litAngleOffset, 0.0, 1.0);\n    return (attenuation * attenuation);\n  }\n  float GetOutOfRange (vec3 worldPos, vec3 lightPos, vec3 lookAt, vec3 right, vec3 BoundingHalfSizeVS) {\n    vec3 v = vec3(0.0);\n    vec3 up = cross(right, lookAt);\n    worldPos -= lightPos;\n    v.x = dot(worldPos, right);\n    v.y = dot(worldPos, up);\n    v.z = dot(worldPos, lookAt);\n    vec3 result = step(abs(v), BoundingHalfSizeVS);\n    return result.x * result.y * result.z;\n  }\n  #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 0\n  vec4 CCStandardShadingAdditive (StandardSurface s, vec4 shadowPos) {\n    vec3 position;\n      #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n      position = unpackHighpData(s.position, s.position_fract_part);\n      #else\n      position = s.position;\n      #endif\n    vec3 diffuse = s.albedo.rgb * (1.0 - s.metallic);\n    vec3 specular = mix(vec3(0.04), s.albedo.rgb, s.metallic);\n    vec3 diffuseContrib = diffuse / PI;\n    vec3 N = normalize(s.normal);\n    vec3 V = normalize(cc_cameraPos.xyz - position);\n    float NV = max(abs(dot(N, V)), 0.0);\n    specular = BRDFApprox(specular, s.roughness, NV);\n    vec3 finalColor = vec3(0.0);\n    int numLights = CC_PIPELINE_TYPE == 0 ? LIGHTS_PER_PASS : int(cc_lightDir[0].w);\n    for (int i = 0; i < LIGHTS_PER_PASS; i++) {\n      if (i >= numLights) break;\n      vec3 SLU = IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w) ? -cc_lightDir[i].xyz : cc_lightPos[i].xyz - position;\n      vec3 SL = normalize(SLU);\n      vec3 SH = normalize(SL + V);\n      float SNL = max(dot(N, SL), 0.0);\n      float SNH = max(dot(N, SH), 0.0);\n      vec3 lspec = specular * CalcSpecular(s.roughness, SNH, SH, N);\n      float illum = 1.0;\n      float att = 1.0;\n      if (IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w)) {\n        att = GetOutOfRange(position, cc_lightPos[i].xyz, cc_lightDir[i].xyz, cc_lightSizeRangeAngle[i].xyz, cc_lightBoundingSizeVS[i].xyz);\n      } else {\n        float distSqr = dot(SLU, SLU);\n        float litRadius = cc_lightSizeRangeAngle[i].x;\n        float litRadiusSqr = litRadius * litRadius;\n        illum = (IS_POINT_LIGHT(cc_lightPos[i].w) || IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w)) ? 1.0 : litRadiusSqr / max(litRadiusSqr, distSqr);\n        float attRadiusSqrInv = 1.0 / max(cc_lightSizeRangeAngle[i].y, 0.01);\n        attRadiusSqrInv *= attRadiusSqrInv;\n        att = GetDistAtt(distSqr, attRadiusSqrInv);\n        if (IS_SPOT_LIGHT(cc_lightPos[i].w)) {\n          float cosInner = max(dot(-cc_lightDir[i].xyz, SL), 0.01);\n          float cosOuter = cc_lightSizeRangeAngle[i].z;\n          float strength = clamp(cc_lightBoundingSizeVS[i].w, 0.0, 1.0);\n          float litAngleScale = 1.0 / max(0.001, mix(cosInner, 1.0, strength) - cosOuter);\n          float litAngleOffset = -cosOuter * litAngleScale;\n          att *= GetAngleAtt(SL, -cc_lightDir[i].xyz, litAngleScale, litAngleOffset);\n        }\n      }\n      float shadow = 1.0;\n      #if CC_RECEIVE_SHADOW  && CC_SHADOW_TYPE == 2\n        if (IS_SPOT_LIGHT(cc_lightPos[i].w) && cc_lightSizeRangeAngle[i].w > 0.0) {\n          shadow = CCSpotShadowFactorBase(shadowPos, position, s.shadowBias);\n        }\n      #endif\n      finalColor += SNL * cc_lightColor[i].rgb * shadow * cc_lightColor[i].w * illum * att * (diffuseContrib + lspec);\n    }\n    return vec4(finalColor, 0.0);\n  }\n  #endif\n  #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 1\n  readonly buffer b_ccLightsBuffer { vec4 b_ccLights[]; };\n  readonly buffer b_clusterLightIndicesBuffer { uint b_clusterLightIndices[]; };\n  readonly buffer b_clusterLightGridBuffer { uvec4 b_clusterLightGrid[]; };\n  struct CCLight\n  {\n    vec4 cc_lightPos;\n    vec4 cc_lightColor;\n    vec4 cc_lightSizeRangeAngle;\n    vec4 cc_lightDir;\n    vec4 cc_lightBoundingSizeVS;\n  };\n  struct Cluster\n  {\n    vec3 minBounds;\n    vec3 maxBounds;\n  };\n  struct LightGrid\n  {\n    uint offset;\n    uint ccLights;\n  };\n  CCLight getCCLight(uint i)\n  {\n    CCLight light;\n    light.cc_lightPos = b_ccLights[5u * i + 0u];\n    light.cc_lightColor = b_ccLights[5u * i + 1u];\n    light.cc_lightSizeRangeAngle = b_ccLights[5u * i + 2u];\n    light.cc_lightDir = b_ccLights[5u * i + 3u];\n    light.cc_lightBoundingSizeVS = b_ccLights[5u * i + 4u];\n    return light;\n  }\n  LightGrid getLightGrid(uint cluster)\n  {\n    uvec4 gridvec = b_clusterLightGrid[cluster];\n    LightGrid grid;\n    grid.offset = gridvec.x;\n    grid.ccLights = gridvec.y;\n    return grid;\n  }\n  uint getGridLightIndex(uint start, uint offset)\n  {\n    return b_clusterLightIndices[start + offset];\n  }\n  uint getClusterZIndex(vec4 worldPos)\n  {\n    float scale = float(24u) / log(cc_nearFar.y / cc_nearFar.x);\n    float bias = -(float(24u) * log(cc_nearFar.x) / log(cc_nearFar.y / cc_nearFar.x));\n    float eyeDepth = -(cc_matView * worldPos).z;\n    uint zIndex = uint(max(log(eyeDepth) * scale + bias, 0.0));\n    return zIndex;\n  }\n  uint getClusterIndex(vec4 fragCoord, vec4 worldPos)\n  {\n    uint zIndex = getClusterZIndex(worldPos);\n    float clusterSizeX = ceil(cc_viewPort.z / float(16u));\n    float clusterSizeY = ceil(cc_viewPort.w / float(8u));\n    uvec3 indices = uvec3(uvec2(fragCoord.xy / vec2(clusterSizeX, clusterSizeY)), zIndex);\n    uint cluster = (16u * 8u) * indices.z + 16u * indices.y + indices.x;\n    return cluster;\n  }\n  vec4 CCClusterShadingAdditive (StandardSurface s, vec4 shadowPos) {\n    vec3 diffuse = s.albedo.rgb * (1.0 - s.metallic);\n    vec3 specular = mix(vec3(0.04), s.albedo.rgb, s.metallic);\n    vec3 diffuseContrib = diffuse / PI;\n    vec3 position;\n      #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n      position = unpackHighpData(s.position, s.position_fract_part);\n      #else\n      position = s.position;\n      #endif\n    vec3 N = normalize(s.normal);\n    vec3 V = normalize(cc_cameraPos.xyz - position);\n    float NV = max(abs(dot(N, V)), 0.001);\n    specular = BRDFApprox(specular, s.roughness, NV);\n    vec3 finalColor = vec3(0.0);\n    uint cluster = getClusterIndex(gl_FragCoord, vec4(position, 1.0));\n    LightGrid grid = getLightGrid(cluster);\n    uint numLights = grid.ccLights;\n    for (uint i = 0u; i < 200u; i++) {\n      if (i >= numLights) break;\n      uint lightIndex = getGridLightIndex(grid.offset, i);\n      CCLight light = getCCLight(lightIndex);\n      vec3 SLU = light.cc_lightPos.xyz - position;\n      vec3 SL = normalize(SLU);\n      vec3 SH = normalize(SL + V);\n      float SNL = max(dot(N, SL), 0.001);\n      float SNH = max(dot(N, SH), 0.0);\n      float distSqr = dot(SLU, SLU);\n      float litRadius = light.cc_lightSizeRangeAngle.x;\n      float litRadiusSqr = litRadius * litRadius;\n      float illum = PI * (litRadiusSqr / max(litRadiusSqr , distSqr));\n      float attRadiusSqrInv = 1.0 / max(light.cc_lightSizeRangeAngle.y, 0.01);\n      attRadiusSqrInv *= attRadiusSqrInv;\n      float att = GetDistAtt(distSqr, attRadiusSqrInv);\n      vec3 lspec = specular * CalcSpecular(s.roughness, SNH, SH, N);\n      if (IS_SPOT_LIGHT(light.cc_lightPos.w)) {\n        float cosInner = max(dot(-light.cc_lightDir.xyz, SL), 0.01);\n        float cosOuter = light.cc_lightSizeRangeAngle.z;\n        float litAngleScale = 1.0 / max(0.001, cosInner - cosOuter);\n        float litAngleOffset = -cosOuter * litAngleScale;\n        att *= GetAngleAtt(SL, -light.cc_lightDir.xyz, litAngleScale, litAngleOffset);\n      }\n      vec3 lightColor = light.cc_lightColor.rgb;\n      float shadow = 1.0;\n      #if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n        if (IS_SPOT_LIGHT(light.cc_lightPos.w)  && light.cc_lightSizeRangeAngle.w > 0.0) {\n          shadow = CCSpotShadowFactorBase(shadowPos, position, s.shadowBias);\n        }\n      #endif\n      lightColor *= shadow;\n      finalColor += SNL * lightColor * light.cc_lightColor.w * illum * att * (diffuseContrib + lspec);\n    }\n    return vec4(finalColor, 0.0);\n  }\n  #endif\n  void main () {\n    StandardSurface s; surf(s);\n    #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 1\n    vec4 color = CCClusterShadingAdditive(s, v_shadowPos);\n    #else\n    vec4 color = CCStandardShadingAdditive(s, v_shadowPos);\n    #endif\n    gl_FragData[0] = CCFragOutput(color);\n  }\n#elif (CC_PIPELINE_TYPE == 0 || CC_FORCE_FORWARD_SHADING)\n  void main () {\n    StandardSurface s; surf(s);\n    vec4 color = CCStandardShadingBase(s, v_shadowPos);\n    #if CC_USE_FOG != 4\n      #if CC_USE_FLOAT_OUTPUT\n        CC_APPLY_FOG(color, s.position.xyz);\n      #elif !CC_FORWARD_ADD\n        CC_APPLY_FOG(color, s.position.xyz);\n      #endif\n    #endif\n    gl_FragData[0] = CCFragOutput(color);\n  }\n#elif CC_PIPELINE_TYPE == 1\n  vec2 signNotZero(vec2 v) {\n    return vec2((v.x >= 0.0) ? +1.0 : -1.0, (v.y >= 0.0) ? +1.0 : -1.0);\n  }\n  vec2 float32x3_to_oct(in vec3 v) {\n    vec2 p = v.xy * (1.0 / (abs(v.x) + abs(v.y) + abs(v.z)));\n    return (v.z <= 0.0) ? ((1.0 - abs(p.yx)) * signNotZero(p)) : p;\n  }\n  void main () {\n    StandardSurface s; surf(s);\n    gl_FragData[0] = s.albedo;\n    gl_FragData[2] = vec4(float32x3_to_oct(s.normal), s.roughness, s.metallic);\n    gl_FragData[1] = vec4(s.emissive, s.occlusion);\n  }\n#endif"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}, {"name": "CCShadow", "defines": []}, {"name": "CCCSM", "defines": ["CC_SUPPORT_CASCADED_SHADOW_MAP"]}], "samplerTextures": [{"name": "cc_shadowMap", "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "cc_spotShadowMap", "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "cc_environment", "defines": []}, {"name": "cc_diffuseMap", "defines": ["CC_USE_IBL", "CC_USE_DIFFUSEMAP"]}], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}, {"name": "CCSH", "defines": ["CC_USE_LIGHT_PROBE", "!USE_INSTANCING"]}, {"name": "CCForwardLight", "defines": ["CC_FORWARD_ADD", "CC_ENABLE_CLUSTERED_LIGHT_CULLING"]}], "samplerTextures": [{"name": "cc_reflectionProbeCubemap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbePlanarMap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeDataMap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_lightingMap", "defines": ["CC_USE_LIGHTMAP", "!CC_FORWARD_ADD"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 115, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 122}}, "defines": [{"name": "CC_USE_FOG", "type": "number", "defines": [], "range": [0, 4]}, {"name": "CC_USE_ACCURATE_FOG", "type": "boolean", "defines": []}, {"name": "CC_SUPPORT_CASCADED_SHADOW_MAP", "type": "boolean", "defines": []}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean", "defines": []}, {"name": "CC_USE_LIGHTMAP", "type": "number", "defines": [], "range": [0, 3]}, {"name": "CC_USE_DEBUG_VIEW", "type": "number", "defines": [], "range": [0, 3]}, {"name": "CC_SURFACES_ENABLE_DEBUG_VIEW", "type": "boolean", "defines": ["CC_USE_DEBUG_VIEW", "CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC"]}, {"name": "CC_SHADOWMAP_FORMAT", "type": "number", "defines": ["CC_RECEIVE_SHADOW"], "range": [0, 3]}, {"name": "CC_SHADOWMAP_USE_LINEAR_DEPTH", "type": "boolean", "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "CC_DIR_SHADOW_PCF_TYPE", "type": "number", "defines": ["CC_RECEIVE_SHADOW"], "range": [0, 3]}, {"name": "CC_CASCADED_LAYERS_TRANSITION", "type": "boolean", "defines": ["CC_RECEIVE_SHADOW", "CC_SUPPORT_CASCADED_SHADOW_MAP"]}, {"name": "CC_USE_IBL", "type": "number", "defines": [], "range": [0, 2]}, {"name": "CC_USE_DIFFUSEMAP", "type": "number", "defines": ["CC_USE_IBL"], "range": [0, 2]}, {"name": "CC_USE_REFLECTION_PROBE", "type": "number", "defines": [], "range": [0, 3]}, {"name": "USE_INSTANCING", "type": "boolean", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "defines": [], "default": 0}, {"name": "CC_USE_HDR", "type": "boolean", "defines": ["CC_USE_LIGHTMAP", "!CC_FORWARD_ADD"]}, {"name": "USE_REFLECTION_DENOISE", "type": "boolean", "defines": []}, {"name": "CC_SHADOW_TYPE", "type": "number", "defines": ["CC_RECEIVE_SHADOW"], "range": [0, 3]}, {"name": "CC_DIR_LIGHT_SHADOW_TYPE", "type": "number", "defines": ["CC_RECEIVE_SHADOW", "CC_SHADOW_TYPE"], "range": [0, 3]}, {"name": "CC_FORWARD_ADD", "type": "boolean", "defines": []}, {"name": "CC_DISABLE_DIRECTIONAL_LIGHT", "type": "boolean", "defines": []}, {"name": "CC_IBL_CONVOLUTED", "type": "boolean", "defines": ["USE_REFLECTION_DENOISE", "CC_USE_IBL", "!CC_USE_REFLECTION_PROBE"]}, {"name": "CC_USE_RGBE_OUTPUT", "type": "boolean", "defines": []}, {"name": "CC_USE_FLOAT_OUTPUT", "type": "boolean", "defines": ["!CC_USE_RGBE_OUTPUT"]}, {"name": "CC_TONE_MAPPING_TYPE", "type": "number", "defines": ["CC_USE_HDR", "!CC_USE_RGBE_OUTPUT", "!CC_USE_FLOAT_OUTPUT"], "range": [0, 3]}, {"name": "HDR_TONE_MAPPING_ACES", "type": "boolean", "defines": ["CC_USE_HDR", "CC_TONE_MAPPING_TYPE", "!CC_USE_RGBE_OUTPUT", "!CC_USE_FLOAT_OUTPUT"]}, {"name": "CC_LIGHT_MAP_VERSION", "type": "number", "defines": ["CC_USE_LIGHTMAP", "!CC_FORWARD_ADD"], "range": [0, 3]}, {"name": "LAYERS", "type": "number", "defines": [], "range": [0, 4]}, {"name": "USE_NORMALMAP", "type": "boolean", "defines": []}, {"name": "USE_PBR", "type": "boolean", "defines": []}, {"name": "CC_PIPELINE_TYPE", "type": "number", "defines": ["CC_FORWARD_ADD"], "range": [0, 1]}, {"name": "CC_FORCE_FORWARD_SHADING", "type": "boolean", "defines": ["CC_PIPELINE_TYPE", "!CC_FORWARD_ADD"]}, {"name": "CC_ENABLE_CLUSTERED_LIGHT_CULLING", "type": "number", "defines": ["CC_FORWARD_ADD"], "range": [0, 3]}], "name": "legacy/terrain|terrain-vs|terrain-fs"}, {"blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_normal", "defines": [], "format": 32, "location": 1}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 2}], "varyings": [{"name": "v_clip_depth", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [{"tags": {"builtin": "local"}, "name": "CCLocal", "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": [], "stageFlags": 1}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 1}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 1}, {"tags": {"builtin": "global"}, "name": "CCShadow", "members": [{"name": "cc_matLightView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matLightViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_shadowInvProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowNFLSInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowWHPBInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowLPNNInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowColor", "typename": "vec4", "type": 16, "count": 1, "precision": "lowp "}, {"name": "cc_planarNDInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 1}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 816809058, "glsl4": {"vert": "\nprecision highp float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(set = 2, binding = 0) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nlayout(set = 0, binding = 2) uniform CCShadow {\n  highp mat4 cc_matLightView;\n  highp mat4 cc_matLightViewProj;\n  highp vec4 cc_shadowInvProjDepthInfo;\n  highp vec4 cc_shadowProjDepthInfo;\n  highp vec4 cc_shadowProjInfo;\n  mediump vec4 cc_shadowNFLSInfo;\n  mediump vec4 cc_shadowWHPBInfo;\n  mediump vec4 cc_shadowLPNNInfo;\n  lowp vec4 cc_shadowColor;\n  mediump vec4 cc_planarNDInfo;\n};\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec3 a_normal;\nlayout(location = 2) in vec2 a_texCoord;\nlayout(location = 0) out highp vec2 v_clip_depth;\nvec4 vert () {\n  vec4 worldPos;\n  worldPos.x = cc_matWorld[3][0] + a_position.x;\n  worldPos.y = cc_matWorld[3][1] + a_position.y;\n  worldPos.z = cc_matWorld[3][2] + a_position.z;\n  worldPos.w = 1.0;\n  vec4 clipPos = cc_matLightViewProj * worldPos;\n  v_clip_depth = clipPos.zw;\n  return clipPos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 packDepthToRGBA (float depth) {\n  vec4 ret = vec4(1.0, 255.0, 65025.0, 16581375.0) * depth;\n  ret = fract(ret);\n  ret -= vec4(ret.yzw, 0.0) / 255.0;\n  return ret;\n}\n#define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\nlayout(location = 0) in highp vec2 v_clip_depth;\nvec4 frag () {\n  highp float clipDepth = v_clip_depth.x / v_clip_depth.y * 0.5 + 0.5;\n  #if CC_SHADOWMAP_FORMAT == 1\n    return packDepthToRGBA(clipDepth);\n  #else\n    return vec4(clipDepth, 1.0, 1.0, 1.0);\n  #endif\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl3": {"vert": "\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(std140) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nlayout(std140) uniform CCShadow {\n  highp mat4 cc_matLightView;\n  highp mat4 cc_matLightViewProj;\n  highp vec4 cc_shadowInvProjDepthInfo;\n  highp vec4 cc_shadowProjDepthInfo;\n  highp vec4 cc_shadowProjInfo;\n  mediump vec4 cc_shadowNFLSInfo;\n  mediump vec4 cc_shadowWHPBInfo;\n  mediump vec4 cc_shadowLPNNInfo;\n  lowp vec4 cc_shadowColor;\n  mediump vec4 cc_planarNDInfo;\n};\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\nout highp vec2 v_clip_depth;\nvec4 vert () {\n  vec4 worldPos;\n  worldPos.x = cc_matWorld[3][0] + a_position.x;\n  worldPos.y = cc_matWorld[3][1] + a_position.y;\n  worldPos.z = cc_matWorld[3][2] + a_position.z;\n  worldPos.w = 1.0;\n  vec4 clipPos = cc_matLightViewProj * worldPos;\n  v_clip_depth = clipPos.zw;\n  return clipPos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 packDepthToRGBA (float depth) {\n  vec4 ret = vec4(1.0, 255.0, 65025.0, 16581375.0) * depth;\n  ret = fract(ret);\n  ret -= vec4(ret.yzw, 0.0) / 255.0;\n  return ret;\n}\n#define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\nin highp vec2 v_clip_depth;\nvec4 frag () {\n  highp float clipDepth = v_clip_depth.x / v_clip_depth.y * 0.5 + 0.5;\n  #if CC_SHADOWMAP_FORMAT == 1\n    return packDepthToRGBA(clipDepth);\n  #else\n    return vec4(clipDepth, 1.0, 1.0, 1.0);\n  #endif\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\nuniform highp mat4 cc_matWorld;\nuniform highp mat4 cc_matLightViewProj;\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nvarying highp vec2 v_clip_depth;\nvec4 vert () {\n  vec4 worldPos;\n  worldPos.x = cc_matWorld[3][0] + a_position.x;\n  worldPos.y = cc_matWorld[3][1] + a_position.y;\n  worldPos.z = cc_matWorld[3][2] + a_position.z;\n  worldPos.w = 1.0;\n  vec4 clipPos = cc_matLightViewProj * worldPos;\n  v_clip_depth = clipPos.zw;\n  return clipPos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 packDepthToRGBA (float depth) {\n  vec4 ret = vec4(1.0, 255.0, 65025.0, 16581375.0) * depth;\n  ret = fract(ret);\n  ret -= vec4(ret.yzw, 0.0) / 255.0;\n  return ret;\n}\n#define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\nvarying highp vec2 v_clip_depth;\nvec4 frag () {\n  highp float clipDepth = v_clip_depth.x / v_clip_depth.y * 0.5 + 0.5;\n  #if CC_SHADOWMAP_FORMAT == 1\n    return packDepthToRGBA(clipDepth);\n  #else\n    return vec4(clipDepth, 1.0, 1.0, 1.0);\n  #endif\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}, {"name": "CCShadow", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 72, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 0}}, "defines": [{"name": "CC_SHADOWMAP_FORMAT", "type": "number", "defines": [], "range": [0, 3]}], "name": "legacy/terrain|shadow-caster-vs:vert|shadow-caster-fs:frag"}], "combinations": [], "hideInEditor": false}