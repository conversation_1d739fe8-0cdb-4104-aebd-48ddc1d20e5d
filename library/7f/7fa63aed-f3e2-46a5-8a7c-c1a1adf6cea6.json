[{"__type__": "cc.Prefab", "_name": "Mask", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "persistent": false}, {"__type__": "cc.Node", "_name": "Mask", "_objFlags": 0, "_parent": null, "_children": [], "_active": true, "_level": 2, "_components": [{"__id__": 2}, {"__id__": 4}], "_prefab": {"__id__": 6}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "", "_priority": 0, "__prefab": {"__id__": 3}}, {"__type__": "cc.CompPrefabInfo", "fileId": "85Rkr8igNOB6QSD/HtnhgH"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_sharedMaterial": null, "_type": 0, "_segments": 64, "_id": "", "__prefab": {"__id__": 5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "90TR3hptxAq7K974GgvafO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7fa63aed-f3e2-46a5-8a7c-c1a1adf6cea6"}, "fileId": "dcqbd5AqtAAa0hfeX2B3/Y"}]