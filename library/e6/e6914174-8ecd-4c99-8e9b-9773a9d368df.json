{"__type__": "cc.Json<PERSON>set", "_name": "parsed-effect-info", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "json": {"list": [{"args": ["vec3 color"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\color\\aces.chunk", "line": 2, "name": "ACESToneMap", "type": "vec3", "usage": "function"}, {"args": ["vec4 shadowPos", "vec3 worldNormal", "float normalBias", "vec3 matViewDir0", "vec3 matViewDir1", "vec3 matViewDir2", "vec2 projScaleXY"], "column": 7, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 52, "name": "ApplyShadowDepthBias_FaceNormal", "type": "vec4", "usage": "function"}, {"args": ["vec4 shadowPos", "vec3 worldNormal", "float normalBias", "mat4 matLightView", "vec2 projScaleXY"], "column": 7, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 64, "name": "ApplyShadowDepthBias_FaceNormal", "type": "vec4", "usage": "function"}, {"args": ["vec4 shadowPos", "float viewspaceDepthBias", "float projScaleZ", "float projBiasZ"], "column": 7, "comment": " (projScaleZ, projBiasZ) = cc_shadowProjDepthInfo.xy", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 110, "name": "ApplyShadowDepthBias_Orthographic", "type": "vec4", "usage": "function"}, {"args": ["vec4 shadowPos", "float viewspaceDepthBias"], "column": 7, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 85, "name": "ApplyShadowDepthBias_Perspective", "type": "vec4", "usage": "function"}, {"args": ["vec4 shadowPos", "float viewspaceDepthBias", "vec3 worldPos"], "column": 7, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 127, "name": "ApplyShadowDepthBias_PerspectiveLinearDepth", "type": "vec4", "usage": "function"}, {"args": ["float baseColor", "float detailedColor"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\effect\\special-effects.chunk", "line": 21, "name": "BlendDetailedColorMap", "type": "float", "usage": "function"}, {"args": ["vec3 baseColor", "vec3 detailedColor"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\effect\\special-effects.chunk", "line": 36, "name": "BlendDetailedColorMap", "type": "vec3", "usage": "function"}, {"args": ["vec3 baseNormalFromMap", "vec3 detailedNormalFromMap", "float detailedIntensity"], "column": 5, "comment": " NormalFromMap is: texture(normalmap) - 0.5", "file": "editor\\assets\\chunks\\common\\effect\\special-effects.chunk", "line": 15, "name": "BlendDetailedNormalMap", "type": "vec3", "usage": "function"}, {"args": ["out vec4 csmPos", "out vec4 csmPosWithBias", "vec3 worldPos", "vec3 N", "vec2 shadowBias"], "column": 10, "comment": " output csmPos is non-biased position that can be used for sampling shadow map after homogeneous divid", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 347, "name": "CCCSMFactorBase", "type": "float", "usage": "function"}, {"args": ["out vec4 csmPos", "out vec4 csmPosWithBias", "vec3 worldPos", "vec3 N", "vec2 shadowBias"], "column": 10, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 416, "name": "CCCSMFactorBase", "type": "float", "usage": "function"}, {"args": ["vec3 worldPos", "vec3 N", "vec2 shadowBias"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 428, "name": "CCCSMFactorBase", "type": "float", "usage": "function"}, {"args": ["out vec4 csmPos", "out vec4 shadowProjDepthInfo", "out vec4 shadowProjInfo", "out vec3 shadowViewDir0", "out vec3 shadowViewDir1", "out vec3 shadowViewDir2", "vec3 worldPos", "int level"], "column": 9, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 294, "name": "CCGetCSMLevel", "type": "void", "usage": "function"}, {"args": ["out bool isTransitionArea", "out highp float transitionRatio", "out vec4 csmPos", "out vec4 shadowProjDepthInfo", "out vec4 shadowProjInfo", "out vec3 shadowViewDir0", "out vec3 shadowViewDir1", "out vec3 shadowViewDir2", "vec3 worldPos"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 313, "name": "CCGetCSMLevel", "type": "int", "usage": "function"}, {"args": ["out vec4 csmPos", "out vec4 shadowProjDepthInfo", "out vec4 shadowProjInfo", "out vec3 shadowViewDir0", "out vec3 shadowViewDir1", "out vec3 shadowViewDir2", "vec3 worldPos"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 339, "name": "CCGetCSMLevel", "type": "int", "usage": "function"}, {"args": ["out vec4 csmPos", "out vec4 shadowProjDepthInfo", "out vec4 shadowProjInfo", "out vec3 shadowViewDir0", "out vec3 shadowViewDir1", "out vec3 shadowViewDir2", "vec3 worldPos"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 412, "name": "CCGetCSMLevel", "type": "int", "usage": "function"}, {"args": ["out highp float ratio", "vec3 clipPos"], "column": 9, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 253, "name": "CCGetCSMLevelWithTransition", "type": "bool", "usage": "function"}, {"args": ["vec4 shadowPosWithDepthBias"], "column": 8, "comment": "////////////////////////////////////////////////////////Directional Light Shadow", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 136, "name": "CCGetDirLightShadowFactorHard", "type": "float", "usage": "function"}, {"args": ["vec4 shadowPosWithDepthBias"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 144, "name": "CCGetDirLightShadowFactorSoft", "type": "float", "usage": "function"}, {"args": ["vec4 shadowPosWithDepthBias"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 152, "name": "CCGetDirLightShadowFactorSoft3X", "type": "float", "usage": "function"}, {"args": ["vec4 shadowPosWithDepthBias"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 160, "name": "CCGetDirLightShadowFactorSoft5X", "type": "float", "usage": "function"}, {"args": ["vec3 worldPos", "float viewSpaceBias"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 21, "name": "CCGetLinearDepth", "type": "float", "usage": "function"}, {"args": ["vec3 worldPos"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 27, "name": "CCGetLinearDepth", "type": "float", "usage": "function"}, {"args": ["vec4 shadowPosWithDepthBias", "vec3 worldPos"], "column": 8, "comment": "////////////////////////////////////////////////////////Spot Light Shadow", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 170, "name": "CCGetSpotLightShadowFactorHard", "type": "float", "usage": "function"}, {"args": ["vec4 shadowPosWithDepthBias", "vec3 worldPos"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 178, "name": "CCGetSpotLightShadowFactorSoft", "type": "float", "usage": "function"}, {"args": ["vec4 shadowPosWithDepthBias", "vec3 worldPos"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 186, "name": "CCGetSpotLightShadowFactorSoft3X", "type": "float", "usage": "function"}, {"args": ["vec4 shadowPosWithDepthBias", "vec3 worldPos"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 194, "name": "CCGetSpotLightShadowFactorSoft5X", "type": "float", "usage": "function"}, {"args": ["out mat4 matWorld"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\world-transform.chunk", "line": 3, "name": "CCGetWorldMatrix", "type": "void", "usage": "function"}, {"args": ["out mat4 matWorld", "out mat4 matWorldIT"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\world-transform.chunk", "line": 17, "name": "CCGetWorldMatrixFull", "type": "void", "usage": "function"}, {"args": ["int level", "vec3 worldPos"], "column": 9, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 277, "name": "CCHasCSMLevel", "type": "bool", "usage": "function"}, {"args": ["out vec4 shadowPosWithDepthBias", "vec4 shadowPos", "vec3 N", "vec2 shadowBias"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 229, "name": "CCShadowFactorBase", "type": "float", "usage": "function"}, {"args": ["vec4 shadowPos", "vec3 N", "vec2 shadowBias"], "column": 8, "comment": " compatible version", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 423, "name": "CCShadowFactorBase", "type": "float", "usage": "function"}, {"args": ["out vec4 shadowPosWithDepthBias", "vec4 shadowPos", "vec3 worldPos", "vec2 shadowBias"], "column": 8, "comment": "////////////////////////////////////////////////////////Main Functions", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 204, "name": "CCSpotShadowFactorBase", "type": "float", "usage": "function"}, {"args": ["vec4 shadowPos", "vec3 worldPos", "vec2 shadowBias"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 433, "name": "CCSpotShadowFactorBase", "type": "float", "usage": "function"}, {"args": ["inout vec4 color", "float factor"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\fog.chunk", "line": 29, "name": "CC_APPLY_FOG_BASE", "type": "void", "usage": "function"}, {"args": ["uv"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 47, "name": "CC_HANDLE_GET_CLIP_FLIP", "type": "", "usage": "macro"}, {"args": ["uv"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 46, "name": "CC_HANDLE_RT_SAMPLE_FLIP", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 98, "name": "CC_SURFACES_ENABLE_DEBUG_VIEW", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 39, "name": "CC_SURFACES_FLIP_UV", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 87, "name": "CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 48, "name": "CC_SURFACES_LIGHTING_ANISOTROPIC", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 51, "name": "CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 75, "name": "CC_SURFACES_LIGHTING_CLEAR_COAT", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 72, "name": "CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 81, "name": "CC_SURFACES_LIGHTING_SSS", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 63, "name": "CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 60, "name": "CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 69, "name": "CC_SURFACES_LIGHTING_TRT", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 78, "name": "CC_SURFACES_LIGHTING_TT", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 57, "name": "CC_SURFACES_LIGHTING_USE_FRESNEL", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 66, "name": "CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 25, "name": "CC_SURFACES_TRANSFER_CLIP_POS", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 22, "name": "CC_SURFACES_TRANSFER_LOCAL_POS", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 54, "name": "CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 31, "name": "CC_SURFACES_USE_LIGHT_MAP", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 45, "name": "CC_SURFACES_USE_REFLECTION_DENOISE", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 13, "name": "CC_SURFACES_USE_SECOND_UV", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 16, "name": "CC_SURFACES_USE_TANGENT_SPACE", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 42, "name": "CC_SURFACES_USE_TWO_SIDED", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\effect-macros\\common-macros.chunk", "line": 19, "name": "CC_SURFACES_USE_VERTEX_COLOR", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 2, "name": "CC_SURFACES_VARING_MODIFIER", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 2, "name": "CC_SURFACES_VARING_MODIFIER", "type": "", "usage": "macro"}, {"args": ["vec4 pos", "out float factor"], "column": 5, "comment": " Fog helper functions", "file": "editor\\assets\\chunks\\builtin\\functionalities\\fog.chunk", "line": 14, "name": "CC_TRANSFER_FOG_BASE", "type": "void", "usage": "function"}, {"args": ["vec3 spotLightDir", "vec3 L", "float cosAngleOuter"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\attenuation.chunk", "line": 42, "name": "CalculateAngleAttenuation", "type": "float", "usage": "function"}, {"args": ["vec3 normal", "vec3 tangent", "float mirrorNormal"], "column": 5, "comment": " for right-hand coordinates, params must be normalized", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 60, "name": "CalculateBinormal", "type": "vec3", "usage": "function"}, {"args": ["vec3 R", "vec3 worldPos", "vec3 cubeCenterPos", "vec3 cubeBoxHalfSize"], "column": 5, "comment": " fix cubemap direction with box projection\r\n return unnormalized vector and weight for exceeding", "file": "editor\\assets\\chunks\\common\\lighting\\functions.chunk", "line": 72, "name": "CalculateBoxProjectedDirection", "type": "vec4", "usage": "function"}, {"args": ["float distToLightSqr", "float lightRadius", "float lightRange"], "column": 6, "comment": " advanced", "file": "editor\\assets\\chunks\\common\\lighting\\attenuation.chunk", "line": 31, "name": "CalculateDistanceAttenuation", "type": "float", "usage": "function"}, {"args": ["float ior", "float NoVSat"], "column": 6, "comment": " saturated N dot V", "file": "editor\\assets\\chunks\\common\\lighting\\bxdf.chunk", "line": 3, "name": "CalculateFresnelCoefficient", "type": "float", "usage": "function"}, {"args": ["vec3 tangent", "vec3 binormal"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 68, "name": "CalculateNormal", "type": "vec3", "usage": "function"}, {"args": ["vec3 normalFromTangentSpace", "float normalStrength", "vec3 normal", "vec3 tangent", "float mirrorNormal"], "column": 5, "comment": " param1 is normal from normalmap\r\n return value is un-normalized", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 76, "name": "CalculateNormalFromTangentSpace", "type": "vec3", "usage": "function"}, {"args": ["vec3 N", "vec3 V", "vec3 worldPos", "vec4 plane", "vec3 cameraPos", "float probeR<PERSON><PERSON><PERSON>ept<PERSON>"], "column": 5, "comment": " for bumped planar reflection", "file": "editor\\assets\\chunks\\common\\lighting\\functions.chunk", "line": 44, "name": "CalculatePlanarReflectPositionOnPlane", "type": "vec3", "usage": "function"}, {"args": ["vec3 N", "vec3 V", "float NoV"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\functions.chunk", "line": 35, "name": "CalculateReflectDirection", "type": "vec3", "usage": "function"}, {"args": ["vec3 N", "vec3 V", "float NoV", "float ior"], "column": 5, "comment": " return unnormalized vector, support oppo-side\r\n V from pixel to camera", "file": "editor\\assets\\chunks\\common\\lighting\\functions.chunk", "line": 5, "name": "CalculateRefractDirection", "type": "vec3", "usage": "function"}, {"args": ["vec3 N", "vec3 V", "float NoV", "float ior"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\functions.chunk", "line": 23, "name": "CalculateRefractDirectionFast", "type": "vec3", "usage": "function"}, {"args": ["vec3 unscatteredColor", "float distance", "float outScatterExtinctCoef", "float inScatterExtinctCoef", "float inScatterCoef", "vec3 inScatterColor", "vec3 outScatterColor"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\bxdf.chunk", "line": 16, "name": "CalculateScattering", "type": "vec3", "usage": "function"}, {"args": ["vec3 normal", "vec3 binormal"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 64, "name": "CalculateTangent", "type": "vec3", "usage": "function"}, {"args": ["out vec2 deltaV", "float frameCount", "float animSpeed", "float elapseTime"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 29, "name": "CalculateVATAnimationUV", "type": "float", "usage": "function"}, {"args": ["float A", "float B"], "column": 6, "comment": " Experimental", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 168, "name": "CalculateVATDecodeUV", "type": "float", "usage": "function"}, {"args": ["inout vec3 L[5", ", out int n"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\rect-area-light.chunk", "line": 12, "name": "ClipQuadToHorizon", "type": "void", "usage": "function"}, {"args": ["type"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 19, "name": "DEFINE_PACK_HIGHP_FUNC", "type": "", "usage": "macro"}, {"args": ["float roughness", "float NoH"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\brdf.chunk", "line": 3, "name": "D_GGX", "type": "float", "usage": "function"}, {"args": ["float RoughnessX", "float RoughnessY", "float NoH", "vec3 H", "vec3 X", "vec3 Y"], "column": 6, "comment": " How to get an anisotropic offset along T/B:\r\n 1. accurate: rotation TBN basis, let N intend to T/B, such as flow map as normalmap, output vec3(0, delta, 1) instead of vec3(0, 0, 1)\r\n 2. not accurate: H intend to V, and passed to this function", "file": "editor\\assets\\chunks\\common\\lighting\\brdf.chunk", "line": 35, "name": "D_GGXAniso", "type": "float", "usage": "function"}, {"args": ["float roughness", "float NoH"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\brdf.chunk", "line": 11, "name": "D_GGXMobile", "type": "float", "usage": "function"}, {"args": ["vec3 color", "float fraction"], "column": 5, "comment": " fraction is saturation percentage, 0 means gray", "file": "editor\\assets\\chunks\\common\\graph-expression\\base.chunk", "line": 5, "name": "Desaturation", "type": "vec3", "usage": "function"}, {"args": ["vec4 clipPos", "vec2 screen_resolution", "float transparency"], "column": 5, "comment": " dithered transparency (dithered alpha test), better looking with TAA\r\n arguments: FSInput_clipPos, cc_viewPort.zw, baseColor.a", "file": "editor\\assets\\chunks\\common\\effect\\special-effects.chunk", "line": 4, "name": "DitheredAlphaClip", "type": "void", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 18, "name": "EPSILON", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 19, "name": "EPSILON_LOWP", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 21, "name": "EXP_VALUE", "type": "", "usage": "macro"}, {"args": ["samplerCube tex", "vec3 R", "float roughness", "float mipCount"], "column": 7, "comment": "", "file": "editor\\assets\\chunks\\common\\texture\\cubemap.chunk", "line": 33, "name": "EnvReflection", "type": "vec3", "usage": "function"}, {"args": ["vec3 R", "float roughness", "float mipCount", "float denoiseIntensity"], "column": 7, "comment": "", "file": "editor\\assets\\chunks\\common\\texture\\cubemap.chunk", "line": 3, "name": "EnvReflectionWithMipFiltering", "type": "vec3", "usage": "function"}, {"args": ["vec4 pos", "vec3 cameraPos", "float fogStart", "float fogDensity", "float fogAtten"], "column": 6, "comment": "pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z", "file": "editor\\assets\\chunks\\common\\effect\\fog.chunk", "line": 11, "name": "ExpFog", "type": "float", "usage": "function"}, {"args": ["vec4 pos", "vec3 cameraPos", "float fogStart", "float fogDensity", "float fogAtten"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\effect\\fog.chunk", "line": 18, "name": "ExpSquaredFog", "type": "float", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 22, "name": "FP_MAX", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 23, "name": "FP_SCALE", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 24, "name": "FP_SCALE_INV", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 58, "name": "FSInput_clipPos", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 15, "name": "FSInput_faceSideSign", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 50, "name": "FSInput_fogFactor", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 39, "name": "FSInput_lightMapUV", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 54, "name": "FSInput_localPos", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 26, "name": "FSInput_mirrorNormal", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 46, "name": "FSInput_reflectionProbeId", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 43, "name": "FSInput_shadowBias", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 16, "name": "FSInput_texcoord", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 33, "name": "FSInput_texcoord1", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 19, "name": "FSInput_vertexColor", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 14, "name": "FSInput_worldNormal", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 13, "name": "FSInput_worldPos", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\fs-input.chunk", "line": 25, "name": "FSInput_worldTangent", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 25, "name": "GRAY_VECTOR", "type": "", "usage": "macro"}, {"args": ["vec3 L", "vec3 litDir", "float litAngleScale", "float litAngleOffset"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\attenuation.chunk", "line": 22, "name": "GetAngleAtt", "type": "float", "usage": "function"}, {"args": ["float roughness", "float anisotropyShape", "vec3 V", "vec3 N", "vec3 X", "vec3 Y"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\brdf.chunk", "line": 45, "name": "GetAnisotropicReflect", "type": "vec3", "usage": "function"}, {"args": ["float roughness", "float anisotropyShape", "out float roughnessX", "out float roughnessY"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\brdf.chunk", "line": 19, "name": "GetAnisotropicRoughness", "type": "void", "usage": "function"}, {"args": ["float depthHS", "mat4 mat<PERSON><PERSON>j"], "column": 6, "comment": " depthHS (Z) = ndc depth(-1 ~ +1)\r\n return camera depth (W), negative in RH", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 32, "name": "GetCameraDepthRH", "type": "float", "usage": "function"}, {"args": ["float depthHS", "float matProj32", "float matProj22"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 36, "name": "GetCameraDepthRH", "type": "float", "usage": "function"}, {"args": ["out vec3 centerPos", "out vec3 boxHalfSize", "out float mipCount", "float probeId"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\probe.chunk", "line": 29, "name": "GetCubeReflectionProbeData", "type": "void", "usage": "function"}, {"args": ["float distSqr", "float invSqrAttRadius"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\attenuation.chunk", "line": 16, "name": "GetDistAtt", "type": "float", "usage": "function"}, {"args": ["out vec3 lightmapColor", "out float dirShadow", "out float ao", "sampler2D lightingMap", "vec2 luv", "float lum", "vec3 worldNormal"], "column": 5, "comment": " for surface shader", "file": "editor\\assets\\chunks\\common\\lighting\\light-map.chunk", "line": 23, "name": "GetLightMapColor", "type": "void", "usage": "function"}, {"args": ["vec3 viewPos", "float near", "float far"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 54, "name": "GetLinearDepthFromViewSpace", "type": "float", "usage": "function"}, {"args": ["out float metallic", "out vec3 albedo", "vec3 diffuse", "vec3 specular", "float channelFaultTolerant", "float f0"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\mesh\\material.chunk", "line": 1, "name": "GetMetallicAlbedoFromDiffuseSpecularMathematic", "type": "bool", "usage": "function"}, {"args": ["out float metallic", "out vec3 albedo", "vec3 diffuse", "vec3 specular", "float f0"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\mesh\\material.chunk", "line": 29, "name": "GetMetallicAlbedoFromDiffuseSpecularWithoutColor", "type": "bool", "usage": "function"}, {"args": ["vec3 worldPos", "mat4 matVirtualCameraViewProj", "float flipNDCSign", "vec3 viewDir", "vec3 reflectDir"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 21, "name": "GetPlanarReflectScreenUV", "type": "vec2", "usage": "function"}, {"args": ["out vec4 plane", "out float planarReflectionDepthScale", "out float mipCount", "float probeId"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\probe.chunk", "line": 11, "name": "GetPlanarReflectionProbeData", "type": "void", "usage": "function"}, {"args": ["vec4 clipPos", "float flipNDCSign"], "column": 5, "comment": " return 0-1", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 7, "name": "GetScreenUV", "type": "vec2", "usage": "function"}, {"args": ["vec3 worldPos", "mat4 matViewProj", "float flipNDCSign"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 15, "name": "GetScreenUV", "type": "vec2", "usage": "function"}, {"args": ["out vec3 shadowNDCPos", "vec4 shadowPosWithDepthBias"], "column": 7, "comment": "////////////////////////////////////////////////////////Helper Functions", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 40, "name": "GetShadowNDCPos", "type": "bool", "usage": "function"}, {"args": ["sampler2D dataMap", "float dataMapWidth", "float x", "float uv_y"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\probe.chunk", "line": 2, "name": "GetTexData", "type": "vec4", "usage": "function"}, {"args": ["vec3 posHS", "mat4 mat<PERSON><PERSON>j", "mat4 matProjInv"], "column": 5, "comment": " posHS = ndc pos (xyz: -1 ~ +1)", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 42, "name": "GetViewPosFromNDCPosRH", "type": "vec4", "usage": "function"}, {"args": ["float NDCDepth", "float projScaleZ", "float projBiasZ"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 76, "name": "GetViewSpaceDepthFromNDCDepth_Orthgraphic", "type": "float", "usage": "function"}, {"args": ["float NDCDepth", "float homogenousDividW", "float invProjScaleZ", "float invProjBiasZ"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\shadow-map.chunk", "line": 80, "name": "GetViewSpaceDepthFromNDCDepth_Perspective", "type": "float", "usage": "function"}, {"args": ["vec3 posHS", "mat4 mat<PERSON><PERSON>j", "mat4 matViewProjInv"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 48, "name": "GetWorldPosFromNDCPosRH", "type": "vec4", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 7, "name": "HALF_PI", "type": "", "usage": "macro"}, {"args": ["vec3 color"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\color\\tone-mapping.chunk", "line": 5, "name": "HDRToLDR", "type": "vec3", "usage": "function"}, {"args": ["value", "defined"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 53, "name": "HIGHP_VALUE_FROM_STRUCT_DEFINED", "type": "", "usage": "macro"}, {"args": ["value", "defined"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 70, "name": "HIGHP_VALUE_FROM_STRUCT_DEFINED_SMALL_RANGE", "type": "", "usage": "macro"}, {"args": ["type", "name"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 44, "name": "HIGHP_VALUE_STRUCT_DEFINE", "type": "", "usage": "macro"}, {"args": ["value", "defined"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 61, "name": "HIGHP_VALUE_TO_STRUCT_DEFINED", "type": "", "usage": "macro"}, {"args": ["value", "defined"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 78, "name": "HIGHP_VALUE_TO_STRUCT_DEFINED_SMALL_RANGE", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 13, "name": "INV_HALF_PI", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 14, "name": "INV_PI", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 15, "name": "INV_PI2", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 16, "name": "INV_PI4", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 12, "name": "INV_QUATER_PI", "type": "", "usage": "macro"}, {"args": ["light_type"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 65, "name": "IS_DIRECTIONAL_LIGHT", "type": "", "usage": "macro"}, {"args": ["light_type"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 68, "name": "IS_POINT_LIGHT", "type": "", "usage": "macro"}, {"args": ["light_type"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 69, "name": "IS_RANGED_DIRECTIONAL_LIGHT", "type": "", "usage": "macro"}, {"args": ["light_type"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 66, "name": "IS_SPHERE_LIGHT", "type": "", "usage": "macro"}, {"args": ["light_type"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 67, "name": "IS_SPOT_LIGHT", "type": "", "usage": "macro"}, {"args": ["vec3 v1", "vec3 v2"], "column": 6, "comment": " https://eheitzresearch.wordpress.com/415-2/", "file": "editor\\assets\\chunks\\common\\lighting\\rect-area-light.chunk", "line": 107, "name": "IntegrateEdge", "type": "float", "usage": "function"}, {"args": ["vec3 specular", "float roughness", "float NoV"], "column": 5, "comment": " EnvBRDFApprox", "file": "editor\\assets\\chunks\\common\\lighting\\brdf.chunk", "line": 63, "name": "IntegratedGFApprox", "type": "vec3", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 51, "name": "LIGHT_MAP_TYPE_ALL_IN_ONE", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 50, "name": "LIGHT_MAP_TYPE_DISABLED", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 52, "name": "LIGHT_MAP_TYPE_INDIRECT_OCCLUSION", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 60, "name": "LIGHT_TYPE_DIRECTIONAL", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 61, "name": "LIGHT_TYPE_POINT", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 64, "name": "LIGHT_TYPE_RANGED_DIRECTIONAL", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 63, "name": "LIGHT_TYPE_SPHERE", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 62, "name": "LIGHT_TYPE_SPOT", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 20, "name": "LOG2", "type": "", "usage": "macro"}, {"args": ["vec3 N", "vec3 V", "vec3 P", "mat3 Minv", "vec3 points[4]"], "column": 5, "comment": " https://blog.magnum.graphics/guest-posts/area-lights-with-ltcs/", "file": "editor\\assets\\chunks\\common\\lighting\\rect-area-light.chunk", "line": 114, "name": "LTC_Evaluate", "type": "vec3", "usage": "function"}, {"args": ["vec4 pos", "vec3 cameraPos", "float fogTop", "float fogRange", "float fogAtten"], "column": 6, "comment": "pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z", "file": "editor\\assets\\chunks\\common\\effect\\fog.chunk", "line": 26, "name": "LayeredFog", "type": "float", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\lighting-models\\data-structures\\lighting-intermediate-data.chunk", "line": 3, "name": "LightingIntermediateData", "type": "", "usage": "variable"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\lighting-models\\data-structures\\lighting-misc-data.chunk", "line": 3, "name": "LightingMiscData", "type": "", "usage": "variable"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\lighting-models\\data-structures\\lighting-result.chunk", "line": 3, "name": "LightingResult", "type": "", "usage": "variable"}, {"args": ["vec4 pos", "vec3 cameraPos", "float fogStart", "float fogEnd"], "column": 6, "comment": "pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y", "file": "editor\\assets\\chunks\\common\\effect\\fog.chunk", "line": 4, "name": "LinearFog", "type": "float", "usage": "function"}, {"args": ["vec3 linear"], "column": 5, "comment": " #pragma define LinearToSRGB(linear) pow(linear, vec3(0.454545))", "file": "editor\\assets\\chunks\\common\\color\\gamma.chunk", "line": 19, "name": "LinearToSRGB", "type": "vec3", "usage": "function"}, {"args": ["v"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\unpack.chunk", "line": 4, "name": "MOD_FINT_128", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 8, "name": "PI", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 9, "name": "PI2", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 10, "name": "PI4", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 6, "name": "QUATER_PI", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 56, "name": "REFLECTION_PROBE_TYPE_CUBE", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 55, "name": "REFLECTION_PROBE_TYPE_NONE", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 57, "name": "REFLECTION_PROBE_TYPE_PLANAR", "type": "", "usage": "macro"}, {"args": ["inout vec3 binormal", "inout vec3 normal", "in vec3 tangent", "float rotationAngle", "float mirrorNormal"], "column": 5, "comment": " fast rotation for anisotropic offset\r\n rotationAngle: -1 - +1", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 115, "name": "RotateNormalAndBinormal", "type": "void", "usage": "function"}, {"args": ["inout vec3 tangent", "inout vec3 binormal", "vec3 normal", "float rotationAngle"], "column": 5, "comment": " rotationAngle: radians, 0-2Pi", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 99, "name": "RotateTangentAndBinormal", "type": "void", "usage": "function"}, {"args": ["vec3 v", "float cosTheta", "float sinTheta"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 84, "name": "RotationVecFromAxisY", "type": "vec3", "usage": "function"}, {"args": ["vec3 v", "float rotateAngleArc"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\coordinates.chunk", "line": 93, "name": "RotationVecFromAxisY", "type": "vec3", "usage": "function"}, {"args": ["vec2 uv", "vec2 centerUV", "float time", "float speed"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\graph-expression\\base.chunk", "line": 11, "name": "Rotator", "type": "vec2", "usage": "function"}, {"args": ["vec3 normal"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\builtin\\functionalities\\sh.chunk", "line": 4, "name": "SHEvaluate", "type": "vec3", "usage": "function"}, {"args": ["vec3 gamma"], "column": 5, "comment": " #pragma define SRGBToLinear(gamma) pow(gamma, vec3(2.2))", "file": "editor\\assets\\chunks\\common\\color\\gamma.chunk", "line": 6, "name": "SRGBToLinear", "type": "vec3", "usage": "function"}, {"args": ["v"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\unpack.chunk", "line": 3, "name": "STEP_FINT_128", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 76, "name": "SURFACES_MAX_TRANSMIT_DEPTH_VALUE", "type": "", "usage": "macro"}, {"args": ["out vec3 lightmapColor", "out float dirShadow", "out float ao", "sampler2D lightingMap", "vec2 luv", "float lum", "vec3 worldNormal"], "column": 5, "comment": " for legacy effects", "file": "editor\\assets\\chunks\\common\\lighting\\light-map.chunk", "line": 2, "name": "SampleAndDecodeLightMapColor", "type": "void", "usage": "function"}, {"args": ["sampler2D exrRGBE", "vec2 uv"], "column": 5, "comment": " for exr data texture and sub resources", "file": "editor\\assets\\chunks\\common\\texture\\texture-misc.chunk", "line": 3, "name": "SampleTextureExr", "type": "vec3", "usage": "function"}, {"args": ["sampler2D exrRGBE", "sampler2D exrSign", "vec2 uv"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\texture\\texture-misc.chunk", "line": 9, "name": "SampleTextureExr", "type": "vec3", "usage": "function"}, {"args": ["sampler2D exrRGBE", "sampler2D exrSign", "sampler2D exrAlpha", "vec2 uv"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\texture\\texture-misc.chunk", "line": 16, "name": "SampleTextureExrWithAlpha", "type": "vec4", "usage": "function"}, {"args": ["float distSqr", "float invSqrAttRadius"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\attenuation.chunk", "line": 10, "name": "SmoothDistAtt", "type": "float", "usage": "function"}, {"args": ["float distSqr", "float invSqrAttRadius"], "column": 6, "comment": " base", "file": "editor\\assets\\chunks\\common\\lighting\\attenuation.chunk", "line": 2, "name": "SmoothDistAtt2", "type": "float", "usage": "function"}, {"args": ["vec2 center", "vec2 point", "float radius", "float hardness"], "column": 6, "comment": " return 0.0 when out of range\r\n hardness: 0 - 1", "file": "editor\\assets\\chunks\\common\\graph-expression\\base.chunk", "line": 36, "name": "SphereMask", "type": "float", "usage": "function"}, {"args": ["vec3 center", "vec3 point", "float radius", "float hardness"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\graph-expression\\base.chunk", "line": 42, "name": "SphereMask", "type": "float", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\legacy\\shading-standard-base.chunk", "line": 3, "name": "StandardSurface", "type": "", "usage": "variable"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_ALPHA_CLIP_ONLY\r\n for base shape without color usage, such as render-to-shadow", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 12, "name": "SurfacesFragmentAlphaClipOnly", "type": "void", "usage": "function"}, {"args": ["out float isRotation"], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_ANISOTROPY_PARAMS\r\n isRotation=1.0: shape(0~1), rotation(0~2PI), 0.0, 0.0\r\n or\r\n isRotation=0.0: shape(0~1), anisoDir.xyz(-1~1)", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 57, "name": "SurfacesFragmentModifyAnisotropyParams", "type": "vec4", "usage": "function"}, {"args": ["out vec4 baseColorAndTransparency", "out vec3 shade1", "out vec3 shade2", "in vec3 baseColor"], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_BASECOLOR_AND_TOONSHADE", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\toon-fs.chunk", "line": 5, "name": "SurfacesFragmentModifyBaseColorAndToonShade", "type": "void", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_BASECOLOR_AND_TRANSPARENCY", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 3, "name": "SurfacesFragmentModifyBaseColorAndTransparency", "type": "vec4", "usage": "function"}, {"args": ["float roughness"], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_DUAL_LOBE_SPECULAR_PARAMS", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 169, "name": "SurfacesFragmentModifyDualLobeSpecularParams", "type": "vec4", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_EMISSIVE", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 66, "name": "SurfacesFragmentModifyEmissive", "type": "vec3", "usage": "function"}, {"args": [], "column": 6, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_IOR", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 46, "name": "SurfacesFragmentModifyIOR", "type": "float", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_PBRPARAMS", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 74, "name": "SurfacesFragmentModifyPBRParams", "type": "vec4", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_SSS_PARAMS", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 179, "name": "SurfacesFragmentModifySSSParams", "type": "vec4", "usage": "function"}, {"args": ["inout SurfacesMaterialData surfaceData"], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_SHARED_DATA\r\n some material datas use shared raw data, avoid sample / calculate same raw data multiply times, use this function for better performance\r\n this function invokes at last\r\n should use corresponding shading-model header: #include <surfaces/data-structures/XXX> before function define", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 194, "name": "SurfacesFragmentModifySharedData", "type": "void", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_TRT_COLOR", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 142, "name": "SurfacesFragmentModifyTRTColor", "type": "vec3", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_TRT_PARAMS", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 131, "name": "SurfacesFragmentModifyTRTParams", "type": "vec4", "usage": "function"}, {"args": ["in vec3 baseColor"], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_TT_COLOR", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 160, "name": "SurfacesFragmentModifyTTColor", "type": "vec3", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_TT_PARAMS", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 150, "name": "SurfacesFragmentModifyTTParams", "type": "vec4", "usage": "function"}, {"args": [], "column": 6, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_TOON_SHADOW_COVER", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\toon-fs.chunk", "line": 22, "name": "SurfacesFragmentModifyToonShadowCover", "type": "float", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_TOON_SPECULAR", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\toon-fs.chunk", "line": 30, "name": "SurfacesFragmentModifyToonSpecular", "type": "vec4", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_TOON_STEP_AND_FEATHER", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\toon-fs.chunk", "line": 14, "name": "SurfacesFragmentModifyToonStepAndFeather", "type": "vec4", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_TRANSMIT_DIFFUSE_PARAMS", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 115, "name": "SurfacesFragmentModifyTransmitDiffuseParams", "type": "vec4", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_TRANSMIT_IN_SCATTERING_COLOR", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 98, "name": "SurfacesFragmentModifyTransmitInScatteringColor", "type": "vec3", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_TRANSMIT_OUT_SCATTERING_COLOR", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 106, "name": "SurfacesFragmentModifyTransmitOutScatteringColor", "type": "vec3", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_TRANSMIT_SCATTERING_PARAMS\r\n scattering related parameters and colors", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 84, "name": "SurfacesFragmentModifyTransmitScatteringParams", "type": "vec4", "usage": "function"}, {"args": [], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_WORLD_NORMAL", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 20, "name": "SurfacesFragmentModifyWorldNormal", "type": "vec3", "usage": "function"}, {"args": ["inout vec3 worldTangent", "inout vec3 worldBinormal", "vec3 worldNormal"], "column": 5, "comment": " depends on CC_SURFACES_FRAGMENT_MODIFY_WORLD_TANGENT_AND_BINORMAL", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\standard-fs.chunk", "line": 28, "name": "SurfacesFragmentModifyWorldTangentAndBinormal", "type": "void", "usage": "function"}, {"args": ["inout LightingResult result", "in LightingIntermediateData lightingData", "in SurfacesMaterialData surfaceData", "in LightingMiscData miscData"], "column": 7, "comment": "", "file": "editor\\assets\\chunks\\lighting-models\\default-functions\\simple-skin.chunk", "line": 9, "name": "SurfacesLightingModifyFinalResult", "type": "void", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\surfaces\\data-structures\\standard.chunk", "line": 3, "name": "SurfacesMaterialData", "type": "", "usage": "variable"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-intermediate.chunk", "line": 3, "name": "SurfacesStandardVertexIntermediate", "type": "", "usage": "variable"}, {"args": ["in SurfacesStandardVertexIntermediate In"], "column": 5, "comment": " depends on CC_SURFACES_VERTEX_MODIFY_CLIP_POS", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\common-vs.chunk", "line": 47, "name": "SurfacesVertexModifyClipPos", "type": "vec4", "usage": "function"}, {"args": ["in SurfacesStandardVertexIntermediate In"], "column": 5, "comment": " depends on CC_SURFACES_VERTEX_MODIFY_LOCAL_NORMAL", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\common-vs.chunk", "line": 11, "name": "SurfacesVertexModifyLocalNormal", "type": "vec3", "usage": "function"}, {"args": ["in SurfacesStandardVertexIntermediate In"], "column": 5, "comment": " depends on CC_SURFACES_VERTEX_MODIFY_LOCAL_POS", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\common-vs.chunk", "line": 3, "name": "SurfacesVertexModifyLocalPos", "type": "vec3", "usage": "function"}, {"args": ["inout SurfacesStandardVertexIntermediate In"], "column": 5, "comment": " depends on CC_SURFACES_VERTEX_MODIFY_LOCAL_SHARED_DATA\r\n some vertex datas use shared raw data, avoid sample / calculate same raw data multiply times, use this function for better performance\r\n this function invokes before world transform", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\common-vs.chunk", "line": 31, "name": "SurfacesVertexModifyLocalSharedData", "type": "void", "usage": "function"}, {"args": ["in SurfacesStandardVertexIntermediate In"], "column": 7, "comment": " depends on CC_SURFACES_VERTEX_MODIFY_LOCAL_TANGENT", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\common-vs.chunk", "line": 20, "name": "SurfacesVertexModifyLocalTangent", "type": "vec4", "usage": "function"}, {"args": ["in SurfacesStandardVertexIntermediate In", "vec2 originShadowBias"], "column": 5, "comment": " depends on CC_SURFACES_VERTEX_MODIFY_SHADOW_BIAS\r\n shadow bias for submesh-level", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\common-vs.chunk", "line": 79, "name": "SurfacesVertexModifyShadowBias", "type": "vec2", "usage": "function"}, {"args": ["inout SurfacesStandardVertexIntermediate In"], "column": 5, "comment": " depends on CC_SURFACES_VERTEX_MODIFY_SHARED_DATA\r\n some vertex datas use shared raw data, avoid sample / calculate same raw data multiply times, use this function for better performance\r\n this function invokes at last", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\common-vs.chunk", "line": 89, "name": "SurfacesVertexModifySharedData", "type": "void", "usage": "function"}, {"args": ["inout SurfacesStandardVertexIntermediate In"], "column": 5, "comment": " depends on CC_SURFACES_VERTEX_MODIFY_UV", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\common-vs.chunk", "line": 55, "name": "SurfacesVertexModifyUV", "type": "void", "usage": "function"}, {"args": ["in SurfacesStandardVertexIntermediate In"], "column": 5, "comment": " depends on CC_SURFACES_VERTEX_MODIFY_WORLD_NORMAL", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\common-vs.chunk", "line": 66, "name": "SurfacesVertexModifyWorldNormal", "type": "vec3", "usage": "function"}, {"args": ["in SurfacesStandardVertexIntermediate In"], "column": 5, "comment": " depends on CC_SURFACES_VERTEX_MODIFY_WORLD_POS", "file": "editor\\assets\\chunks\\surfaces\\default-functions\\common-vs.chunk", "line": 39, "name": "SurfacesVertexModifyWorldPos", "type": "vec3", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 72, "name": "TONE_MAPPING_ACES", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 73, "name": "TONE_MAPPING_LINEAR", "type": "", "usage": "macro"}, {"args": ["vec3 vectorFromNormalMap"], "column": 5, "comment": " tangent space -> world space\r\n same as TransformVector", "file": "editor\\assets\\chunks\\common\\graph-expression\\base.chunk", "line": 23, "name": "TransformNormalMap", "type": "vec3", "usage": "function"}, {"args": ["value", "bit"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 15, "name": "UnpackBitFromFloat", "type": "", "usage": "macro"}, {"args": ["vec3 vatBoundingBoxMin", "vec3 vatBoundingBoxMax", "vec3 localPos"], "column": 5, "comment": " calculate simulation voxel coordinates", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 105, "name": "VATCalculateFluidVoxelUV", "type": "vec3", "usage": "function"}, {"args": ["vec2 lutTexResolution", "float meshVertexCount"], "column": 6, "comment": "/////////////////////////////////////////// public functions\r\n auto calculation frame count for fluid", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 44, "name": "VATCalculateFrameCount", "type": "float", "usage": "function"}, {"args": ["out vec2 thisFrameUV", "out vec2 nextFrameUV", "vec2 meshUV", "float frameCount", "float animSpeed", "float elapseTime"], "column": 6, "comment": " meshUV use texCoord0 for fluid\r\n meshUV use texCoord1 for rigid-body and soft-body", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 52, "name": "VATGetAnimUV", "type": "float", "usage": "function"}, {"args": ["out vec2 thisFrameUV", "out vec2 nextFrameUV", "vec2 meshUV", "float frameCount", "float animSpeed", "float elapseTime", "sampler2D lutTexture"], "column": 6, "comment": " VAT with LUT, fluid only", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 61, "name": "VATGetAnimUV", "type": "float", "usage": "function"}, {"args": ["vec3 meshNormal", "vec2 thisFrameUV", "sampler2D vatRotationTexture", "sampler2D vatRotationSignTexture", "sampler2D vatRotationAlphaTexture"], "column": 5, "comment": " meshNormal is up-axis for fluid", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 80, "name": "VATGetLocalNormal", "type": "vec3", "usage": "function"}, {"args": ["vec3 meshNormal", "vec2 thisFrameUV", "vec2 nextFrameUV", "float frameLerp", "sampler2D vatRotationTexture", "sampler2D vatRotationSignTexture", "sampler2D vatRotationAlphaTexture"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 94, "name": "VATGetLocalNormal", "type": "vec3", "usage": "function"}, {"args": ["vec2 thisFrameUV", "sampler2D vatPositionTexture", "sampler2D vatPositionSignTexture"], "column": 5, "comment": " return absolute position for fluid\r\n return position offset for soft-body", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 74, "name": "VATGetLocalPosition", "type": "vec3", "usage": "function"}, {"args": ["vec2 thisFrameUV", "vec2 nextFrameUV", "float frameLerp", "sampler2D vatPositionTexture", "sampler2D vatPositionSignTexture"], "column": 5, "comment": " for smooth animation", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 88, "name": "VATGetLocalPosition", "type": "vec3", "usage": "function"}, {"args": ["inout vec3 meshLocalPos", "inout vec3 meshLocalNormal", "inout vec3 meshLocalTangent", "in vec4 meshVertexColor", "vec2 thisFrameUV", "float pivMax", "float pivMin", "float posMax", "float posMin", "sampler2D vatPositionTexture", "sampler2D vatPositionSignTexture", "sampler2D vatPositionAlphaTexture", "sampler2D vatRotationTexture", "sampler2D vatRotationSignTexture", "sampler2D vatRotationAlphaTexture"], "column": 5, "comment": "//////////////////////////////////////////////////////////////Rigid-body", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 121, "name": "VATGetLocalPositionRigidBody20", "type": "void", "usage": "function"}, {"args": ["inout vec3 meshLocalPos", "inout vec3 meshLocalNormal", "inout vec3 meshLocalTangent", "in vec4 meshVertexColor", "vec2 thisFrameUV", "float pivMax", "float pivMin", "float posMax", "float posMin", "sampler2D vatPositionTexture", "sampler2D vatPositionSignTexture", "sampler2D vatPositionAlphaTexture", "sampler2D vatRotationTexture", "sampler2D vatRotationSignTexture", "sampler2D vatRotationAlphaTexture"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 151, "name": "VATGetLocalPositionRigidBody20_UE", "type": "void", "usage": "function"}, {"args": ["inout vec3 meshLocalPos", "inout vec3 meshLocalNormal", "inout vec3 meshLocalTangent", "in vec2 meshUV2", "in vec2 meshUV3", "vec2 thisFrameUV", "sampler2D vatPositionTexture", "sampler2D vatPositionSignTexture", "sampler2D vatPositionAlphaTexture", "sampler2D vatRotationTexture", "sampler2D vatRotationSignTexture", "sampler2D vatRotationAlphaTexture", "bool isZUp"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 186, "name": "VATGetLocalPositionRigidBody30", "type": "void", "usage": "function"}, {"args": ["inout vec3 meshLocalPos", "inout vec3 meshLocalNormal", "inout vec3 meshLocalTangent", "in vec2 meshUV2", "in vec2 meshUV3", "vec2 thisFrameUV", "sampler2D vatPositionTexture", "sampler2D vatPositionSignTexture", "sampler2D vatPositionAlphaTexture", "sampler2D vatRotationTexture", "sampler2D vatRotationSignTexture", "sampler2D vatRotationAlphaTexture"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 259, "name": "VATGetLocalPositionRigidBody30_Cocos", "type": "void", "usage": "function"}, {"args": ["inout vec3 meshLocalPos", "inout vec3 meshLocalNormal", "inout vec3 meshLocalTangent", "in vec2 meshUV2", "in vec2 meshUV3", "vec2 thisFrameUV", "sampler2D vatPositionTexture", "sampler2D vatPositionSignTexture", "sampler2D vatPositionAlphaTexture", "sampler2D vatRotationTexture", "sampler2D vatRotationSignTexture", "sampler2D vatRotationAlphaTexture"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\mesh\\vat-animation.chunk", "line": 245, "name": "VATGetLocalPositionRigidBody30_UE", "type": "void", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 49, "name": "VSOutput_clipPos", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 13, "name": "VSOutput_faceSideSign", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 41, "name": "VSOutput_fogFactor", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 30, "name": "VSOutput_lightMapUV", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 45, "name": "VSOutput_localPos", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 22, "name": "VSOutput_mirrorNormal", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 37, "name": "VSOutput_reflectionProbeId", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 34, "name": "VSOutput_shadowBias", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 14, "name": "VSOutput_texcoord", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 26, "name": "VSOutput_texcoord1", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 17, "name": "VSOutput_vertexColor", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 12, "name": "VSOutput_worldNormal", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 11, "name": "VSOutput_worldPos", "type": "", "usage": "macro"}, {"args": [], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\shading-entries\\data-structures\\vs-output.chunk", "line": 21, "name": "VSOutput_worldTangent", "type": "", "usage": "macro"}, {"args": ["anytype Value_Radian"], "column": 1, "comment": "", "file": "", "line": 1, "name": "acos", "type": "anytype", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": " TRUE if all elements are non-zero", "file": "", "line": 1, "name": "all", "type": "bool", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": " Returns TRUE if any one element is non-zero", "file": "", "line": 1, "name": "any", "type": "bool", "usage": "function"}, {"args": ["anytype Value_Radian"], "column": 1, "comment": "", "file": "", "line": 1, "name": "asin", "type": "anytype", "usage": "function"}, {"args": ["anytype y_Div_x"], "column": 1, "comment": " Calculates the arctangent of a given value, no quadrant can be determined", "file": "", "line": 1, "name": "atan", "type": "anytype", "usage": "function"}, {"args": ["float y", "float x"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 243, "name": "atan2", "type": "float", "usage": "function"}, {"args": ["anytype y", "anytype x"], "column": 1, "comment": " Computes the arctangent of y/x and can determine the quadrant", "file": "", "line": 1, "name": "atan2", "type": "anytype", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "attribute", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "bool", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "break", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "bvec2", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "bvec3", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "bvec4", "type": "", "usage": "keyword"}, {"args": [], "column": 26, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 15, "name": "cc_ambientGround", "type": "vec4", "usage": "variable"}, {"args": [], "column": 23, "comment": " view matrix", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 14, "name": "cc_ambientSky", "type": "vec4", "usage": "variable"}, {"args": [], "column": 24, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 8, "name": "cc_cameraPos", "type": "vec4", "usage": "variable"}, {"args": [], "column": 28, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 7, "name": "cc_debug_view_mode", "type": "vec4", "usage": "variable"}, {"args": [], "column": 21, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 11, "name": "cc_exposure", "type": "vec4", "usage": "variable"}, {"args": [], "column": 19, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 18, "name": "cc_fogAdd", "type": "vec4", "usage": "variable"}, {"args": [], "column": 20, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 17, "name": "cc_fogBase", "type": "vec4", "usage": "variable"}, {"args": [], "column": 21, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 16, "name": "cc_fogColor", "type": "vec4", "usage": "variable"}, {"args": ["LIGHTS_PER_PASS"], "column": 39, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-forward-light.chunk", "line": 3, "name": "cc_lightColor", "type": "vec4", "usage": "variable"}, {"args": ["LIGHTS_PER_PASS"], "column": 37, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-forward-light.chunk", "line": 5, "name": "cc_lightDir", "type": "vec4", "usage": "variable"}, {"args": ["LIGHTS_PER_PASS"], "column": 38, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-forward-light.chunk", "line": 2, "name": "cc_lightPos", "type": "vec4", "usage": "variable"}, {"args": ["LIGHTS_PER_PASS"], "column": 48, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-forward-light.chunk", "line": 4, "name": "cc_lightSizeRangeAngle", "type": "vec4", "usage": "variable"}, {"args": [], "column": 31, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-local.chunk", "line": 4, "name": "cc_lightingMapUVParam", "type": "vec4", "usage": "variable"}, {"args": [], "column": 28, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-local.chunk", "line": 5, "name": "cc_localShadowBias", "type": "vec4", "usage": "variable"}, {"args": [], "column": 25, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 13, "name": "cc_mainLitColor", "type": "vec4", "usage": "variable"}, {"args": [], "column": 23, "comment": " x: single mode, yzw: composite and misc flags combination value", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 12, "name": "cc_mainLitDir", "type": "vec4", "usage": "variable"}, {"args": [], "column": 25, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-shadow.chunk", "line": 2, "name": "cc_matLightView", "type": "mat4", "usage": "variable"}, {"args": [], "column": 29, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-shadow.chunk", "line": 3, "name": "cc_matLightViewProj", "type": "mat4", "usage": "variable"}, {"args": [], "column": 22, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 4, "name": "cc_matProj", "type": "mat4", "usage": "variable"}, {"args": [], "column": 25, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 5, "name": "cc_matProjInv", "type": "mat4", "usage": "variable"}, {"args": [], "column": 22, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 2, "name": "cc_matView", "type": "mat4", "usage": "variable"}, {"args": [], "column": 25, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 3, "name": "cc_matViewInv", "type": "mat4", "usage": "variable"}, {"args": [], "column": 26, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 6, "name": "cc_matViewProj", "type": "mat4", "usage": "variable"}, {"args": [], "column": 29, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 7, "name": "cc_matViewProjInv", "type": "mat4", "usage": "variable"}, {"args": [], "column": 21, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-local.chunk", "line": 2, "name": "cc_matWorld", "type": "mat4", "usage": "variable"}, {"args": [], "column": 23, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-local.chunk", "line": 3, "name": "cc_matWorldIT", "type": "mat4", "usage": "variable"}, {"args": [], "column": 23, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 4, "name": "cc_nativeSize", "type": "vec4", "usage": "variable"}, {"args": [], "column": 20, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 19, "name": "cc_nearFar", "type": "vec4", "usage": "variable"}, {"args": [], "column": 25, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-shadow.chunk", "line": 11, "name": "cc_planarNDInfo", "type": "vec4", "usage": "variable"}, {"args": [], "column": 22, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 5, "name": "cc_probeInfo", "type": "vec4", "usage": "variable"}, {"args": [], "column": 33, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-local.chunk", "line": 6, "name": "cc_reflectionProbeData1", "type": "vec4", "usage": "variable"}, {"args": [], "column": 33, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-local.chunk", "line": 7, "name": "cc_reflectionProbeData2", "type": "vec4", "usage": "variable"}, {"args": [], "column": 24, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 10, "name": "cc_screenScale", "type": "vec4", "usage": "variable"}, {"args": [], "column": 23, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 3, "name": "cc_screenSize", "type": "vec4", "usage": "variable"}, {"args": [], "column": 24, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-shadow.chunk", "line": 10, "name": "cc_shadowColor", "type": "vec4", "usage": "variable"}, {"args": [], "column": 35, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-shadow.chunk", "line": 4, "name": "cc_shadowInvProjDepthInfo", "type": "vec4", "usage": "variable"}, {"args": [], "column": 27, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-shadow.chunk", "line": 9, "name": "cc_shadowLPNNInfo", "type": "vec4", "usage": "variable"}, {"args": [], "column": 27, "comment": " x -> cc_matLightProj[0];             y -> cc_matLightProj[5];             z -> 1.0 / cc_matLightProj[0];        w -> 1.0 / cc_matLightProj[5];", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-shadow.chunk", "line": 7, "name": "cc_shadowNFLSInfo", "type": "vec4", "usage": "variable"}, {"args": [], "column": 32, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-shadow.chunk", "line": 5, "name": "cc_shadowProjDepthInfo", "type": "vec4", "usage": "variable"}, {"args": [], "column": 27, "comment": " x -> cc_matLightProj[10]([2][2]);    y -> cc_matLightProj[14]([2][3]);    z -> cc_matLightProj[11]([3][2]);    w -> cc_matLightProj[15]([3][3]);", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-shadow.chunk", "line": 6, "name": "cc_shadowProjInfo", "type": "vec4", "usage": "variable"}, {"args": [], "column": 27, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-shadow.chunk", "line": 8, "name": "cc_shadowWHPBInfo", "type": "vec4", "usage": "variable"}, {"args": [], "column": 29, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 9, "name": "cc_surfaceTransform", "type": "vec4", "usage": "variable"}, {"args": [], "column": 19, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 2, "name": "cc_time", "type": "vec4", "usage": "variable"}, {"args": [], "column": 21, "comment": " xyz: camera position w: flip NDC sign", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-global.chunk", "line": 20, "name": "cc_viewPort", "type": "vec4", "usage": "variable"}, {"args": [], "column": 29, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-world-bound.chunk", "line": 2, "name": "cc_worldBoundCenter", "type": "vec4", "usage": "variable"}, {"args": [], "column": 34, "comment": "", "file": "editor\\assets\\chunks\\builtin\\uniforms\\cc-world-bound.chunk", "line": 3, "name": "cc_worldBoundHalfExtents", "type": "vec4", "usage": "variable"}, {"args": ["anytype Data"], "column": 1, "comment": " Take the integer that is greater than or equal to it and closest to it", "file": "", "line": 1, "name": "ceil", "type": "UINTVECType", "usage": "function"}, {"args": ["anytype Data", "anytype MinValue", "anytype MaxValue"], "column": 1, "comment": " Be especially careful with UINT variables, which must be converted to int before being passed in", "file": "", "line": 1, "name": "clamp", "type": "anytype", "usage": "function"}, {"args": ["value"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 31, "name": "clip", "type": "", "usage": "macro"}, {"args": ["anytype AnyElementIsLessThanZeroThenClip"], "column": 1, "comment": "", "file": "", "line": 1, "name": "clip", "type": "void", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "const", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "continue", "type": "", "usage": "keyword"}, {"args": ["anytype Value_Radian"], "column": 1, "comment": "", "file": "", "line": 1, "name": "cos", "type": "anytype", "usage": "function"}, {"args": ["anytype Value_Radian"], "column": 1, "comment": " Hyperbolic sine", "file": "", "line": 1, "name": "cosh", "type": "anytype", "usage": "function"}, {"args": ["VECType Data1", "VECType Data2"], "column": 1, "comment": " Left-hand rule", "file": "", "line": 1, "name": "cross", "type": "VECType", "usage": "function"}, {"args": ["anytype ShaderInputData"], "column": 1, "comment": "", "file": "", "line": 1, "name": "dFdx", "type": "anytype", "usage": "function"}, {"args": ["anytype ShaderInputData"], "column": 1, "comment": " Low precision, not supported by GLES", "file": "", "line": 1, "name": "dFdxCoarse", "type": "anytype", "usage": "function"}, {"args": ["anytype ShaderInputData"], "column": 1, "comment": " High precision, not supported by GLES", "file": "", "line": 1, "name": "dFdxFine", "type": "anytype", "usage": "function"}, {"args": ["anytype ShaderInputData"], "column": 1, "comment": "", "file": "", "line": 1, "name": "dFdy", "type": "anytype", "usage": "function"}, {"args": ["anytype ShaderInputData"], "column": 1, "comment": " Low precision, not supported by GLES", "file": "", "line": 1, "name": "dFdyCoarse", "type": "anytype", "usage": "function"}, {"args": ["anytype ShaderInputData"], "column": 1, "comment": " High precision, not supported by GLES", "file": "", "line": 1, "name": "dFdyFine", "type": "anytype", "usage": "function"}, {"args": ["highp vec4 rgba"], "column": 12, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\unpack.chunk", "line": 6, "name": "decode32", "type": "float", "usage": "function"}, {"args": ["floatVECType Value_Radian"], "column": 1, "comment": " Turn radians into degrees", "file": "", "line": 1, "name": "degrees", "type": "floatVECType", "usage": "function"}, {"args": ["MAT4 Data"], "column": 1, "comment": " Matrix ranking", "file": "", "line": 1, "name": "determinant", "type": "float", "usage": "function"}, {"args": ["anytype AnyElementIsLessThanZeroThenClip"], "column": 1, "comment": "", "file": "", "line": 1, "name": "discard", "type": "void", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "discard", "type": "", "usage": "keyword"}, {"args": ["VECType Point1", "VECType Point2"], "column": 1, "comment": "", "file": "", "line": 1, "name": "distance", "type": "float", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "do", "type": "", "usage": "keyword"}, {"args": ["VECType Data1", "VECType Data2"], "column": 1, "comment": "", "file": "", "line": 1, "name": "dot", "type": "float", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "else", "type": "", "usage": "keyword"}, {"args": ["anytype Data1", "anytype Data2"], "column": 1, "comment": " Make a judgment for each element of Data1 and Data2", "file": "", "line": 1, "name": "equal", "type": "boolVECType", "usage": "function"}, {"args": ["data1", "data2"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 28, "name": "equalf", "type": "", "usage": "macro"}, {"args": ["data1", "data2", "epsilonValue"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 30, "name": "equalf_epsilon", "type": "", "usage": "macro"}, {"args": ["data1", "data2"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 29, "name": "equalf_lowp", "type": "", "usage": "macro"}, {"args": ["vec3 euler"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 224, "name": "eulerToQuat", "type": "vec4", "usage": "function"}, {"args": ["anytype Power"], "column": 1, "comment": " Exponent with natural logarithm base", "file": "", "line": 1, "name": "exp", "type": "anytype", "usage": "function"}, {"args": ["anytype Power"], "column": 1, "comment": " Exponent with a base of 2", "file": "", "line": 1, "name": "exp2", "type": "anytype", "usage": "function"}, {"args": ["floatVECType Normal", "floatVECType Incident", "floatVECType Normal_referenced"], "column": 1, "comment": "", "file": "", "line": 1, "name": "faceforward", "type": "floatVECType", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "false", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "float", "type": "", "usage": "keyword"}, {"args": ["in vec3 v"], "column": 5, "comment": " Assume normalized input. Output is on [-1, 1] for each component.", "file": "editor\\assets\\chunks\\common\\math\\octahedron-transform.chunk", "line": 7, "name": "float32x3_to_oct", "type": "vec2", "usage": "function"}, {"args": ["floatVECType value"], "column": 1, "comment": " equivalent to asint, reads the floating point value as INT bit by bit, without changing the bit value", "file": "", "line": 1, "name": "floatBitsToInt", "type": "UINTVECType", "usage": "function"}, {"args": ["floatVECType value"], "column": 1, "comment": " equivalent to asuint, reads the floating point value as a UINT number bit by bit, without changing the bit value", "file": "", "line": 1, "name": "floatBitsToUint", "type": "UINTVECType", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": " Take the integer that is less than or equal to it and closest to it", "file": "", "line": 1, "name": "floor", "type": "UINTVECType", "usage": "function"}, {"args": ["double a", "double b", "double c"], "column": 1, "comment": " Double type and safe calculation of a*b+c", "file": "", "line": 1, "name": "fma", "type": "double", "usage": "function"}, {"args": ["floatVECType Data", "floatVECType multiplier", "floatVECType bias"], "column": 1, "comment": " Equivalent to mad", "file": "", "line": 1, "name": "fma", "type": "floatVECType", "usage": "function"}, {"args": ["floatVECType Data", "floatVECType Divisor"], "column": 1, "comment": " Floating-point special remainder function, equivalent to Data % Divisor", "file": "", "line": 1, "name": "fmod", "type": "floatVECType", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "for", "type": "", "usage": "keyword"}, {"args": ["anytype Data"], "column": 1, "comment": " Take its fractional part", "file": "", "line": 1, "name": "fract", "type": "anytype", "usage": "function"}, {"args": ["sampler2D tex", "vec2 P", "vec2 dPdx", "vec2 dPdy"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\texture\\texture-lod.chunk", "line": 29, "name": "fragTextureGrad", "type": "vec4", "usage": "function"}, {"args": ["samplerCube tex", "vec3 P", "vec3 dPdx", "vec3 dPdy"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\texture\\texture-lod.chunk", "line": 41, "name": "fragTextureGrad", "type": "vec4", "usage": "function"}, {"args": ["sampler2D tex", "vec2 coord", "float lod"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\texture\\texture-lod.chunk", "line": 5, "name": "fragTextureLod", "type": "vec4", "usage": "function"}, {"args": ["samplerCube tex", "vec3 coord", "float lod"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\texture\\texture-lod.chunk", "line": 17, "name": "fragTextureLod", "type": "vec4", "usage": "function"}, {"args": ["anytype Data", "out anytype OutData"], "column": 1, "comment": " decompose a number and return its tail, parameter 2 is the exponent of the output, with a base of 10", "file": "", "line": 1, "name": "frexp", "type": "anytype", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": " Returns the sum of the absolute values of the partial derivatives of the input data, i.e., the sum of the absolute values of each of the two partial derivatives, which is equivalent to taking the absolute values internally before adding the gradients return abs(ddx)+abs(ddy)", "file": "", "line": 1, "name": "fwidth", "type": "anytype", "usage": "function"}, {"args": ["anytype ShaderInputData"], "column": 1, "comment": " return abs(dFdx) + abs(dFdy)", "file": "", "line": 1, "name": "fwidth", "type": "anytype", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "gl_FragColor", "type": "", "usage": "variable"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "gl_<PERSON>ag<PERSON><PERSON>h", "type": "", "usage": "variable"}, {"args": ["anytype Data1", "anytype Data2"], "column": 1, "comment": " Judgment for each element of Data1 and Data2", "file": "", "line": 1, "name": "greaterThan", "type": "boolVECType", "usage": "function"}, {"args": ["anytype Data1", "anytype Data2"], "column": 1, "comment": " Judgment for each element of Data1 and Data2", "file": "", "line": 1, "name": "greaterThanEqual", "type": "boolVECType", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "highp", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "if", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "in", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "inout", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "int", "type": "", "usage": "keyword"}, {"args": ["INTVECType value"], "column": 1, "comment": " Equivalent to asfloat, reads int numbers as floating point values bit by bit, without changing the bit value", "file": "", "line": 1, "name": "intBitsToFloat", "type": "floatVECType", "usage": "function"}, {"args": ["floatVECType interpolant"], "column": 1, "comment": "", "file": "", "line": 1, "name": "interpolateAtCentroid", "type": "floatVECType", "usage": "function"}, {"args": ["floatVECType interpolant", "vec2 offset"], "column": 1, "comment": "", "file": "", "line": 1, "name": "interpolateAtOffset", "type": "floatVECType", "usage": "function"}, {"args": ["floatVECType interpolant", "int sampleIndex"], "column": 1, "comment": "", "file": "", "line": 1, "name": "interpolateAtSample", "type": "floatVECType", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": " 1 / sqrt(Data)", "file": "", "line": 1, "name": "inversesqrt", "type": "anytype", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": " whether it is finite, remove the possibility of irrational numbers and infinite size values, mainly used to judge the validity", "file": "", "line": 1, "name": "isfinite", "type": "bool", "usage": "function"}, {"args": ["float x"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 5, "name": "isinf", "type": "bool", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": " whether it is infinitely large or infinitely small", "file": "", "line": 1, "name": "isinf", "type": "bool", "usage": "function"}, {"args": ["vec2 val"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 20, "name": "isinfs", "type": "bool", "usage": "function"}, {"args": ["vec3 val"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 23, "name": "isinfs", "type": "bool", "usage": "function"}, {"args": ["vec4 val"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 26, "name": "isinfs", "type": "bool", "usage": "function"}, {"args": ["float val"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 2, "name": "isnan", "type": "bool", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": " whether it is an irrational number", "file": "", "line": 1, "name": "isnan", "type": "bool", "usage": "function"}, {"args": ["vec2 val"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 10, "name": "isnans", "type": "bool", "usage": "function"}, {"args": ["vec3 val"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 13, "name": "isnans", "type": "bool", "usage": "function"}, {"args": ["vec4 val"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 16, "name": "isnans", "type": "bool", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "ivec2", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "ivec3", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "ivec4", "type": "", "usage": "keyword"}, {"args": ["anytype Multiplier", "anytype Power"], "column": 1, "comment": " return Multiplier * 2^Power", "file": "", "line": 1, "name": "ldexp", "type": "anytype", "usage": "function"}, {"args": ["VECType Data"], "column": 1, "comment": "", "file": "", "line": 1, "name": "length", "type": "float", "usage": "function"}, {"args": ["value1", "value2", "value2Multiplier"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\common-define.chunk", "line": 32, "name": "lerp", "type": "", "usage": "macro"}, {"args": ["anytype Data1_Left", "anytype Data2_Right", "anytype Coef_DirectMulToData2"], "column": 1, "comment": "", "file": "", "line": 1, "name": "lerp", "type": "anytype", "usage": "function"}, {"args": ["anytype Data1", "anytype Data2"], "column": 1, "comment": " Judge each element of Data1 and Data2", "file": "", "line": 1, "name": "lessThan", "type": "boolVECType", "usage": "function"}, {"args": ["anytype Data1", "anytype Data2"], "column": 1, "comment": " Make a judgment for each element of Data1 and Data2", "file": "", "line": 1, "name": "lessThanEqual", "type": "boolVECType", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": " Base on the natural logarithm e", "file": "", "line": 1, "name": "log", "type": "anytype", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": " Base on 10", "file": "", "line": 1, "name": "log10", "type": "anytype", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": " Base on 2", "file": "", "line": 1, "name": "log2", "type": "anytype", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "lowp", "type": "", "usage": "keyword"}, {"args": ["anytype A", "anytype B", "anytype C"], "column": 1, "comment": " Returns A*B+C", "file": "", "line": 1, "name": "mad", "type": "anytype", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "mat2", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "mat3", "type": "", "usage": "keyword"}, {"args": ["mat3 mat"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 189, "name": "mat3ToQuat", "type": "vec4", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "mat4", "type": "", "usage": "keyword"}, {"args": ["vec4 q", "vec3 t", "vec3 s"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 98, "name": "matFromRTS", "type": "mat4", "usage": "function"}, {"args": ["MATType m1", "MATType m2"], "column": 1, "comment": " Calculate the product of two matrices one by one for each element", "file": "", "line": 1, "name": "matrixCompMult", "type": "MATType", "usage": "function"}, {"args": ["vec4 q", "vec3 p"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 76, "name": "matrixFromRT", "type": "mat4", "usage": "function"}, {"args": ["anytype A", "anytype B"], "column": 1, "comment": "", "file": "", "line": 1, "name": "max", "type": "anytype", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "mediump", "type": "", "usage": "keyword"}, {"args": ["anytype A", "anytype B"], "column": 1, "comment": "", "file": "", "line": 1, "name": "min", "type": "anytype", "usage": "function"}, {"args": ["anytype Data1_Left", "anytype Data2_Right", "anytype Coef_DirectMulToData2"], "column": 1, "comment": "", "file": "", "line": 1, "name": "mix", "type": "anytype", "usage": "function"}, {"args": ["floatVECType Data", "floatVECType Divisor"], "column": 1, "comment": " Floating-point-specific remainder function, equivalent to Data % divide", "file": "", "line": 1, "name": "mod", "type": "floatVECType", "usage": "function"}, {"args": ["anytype Data", "out INTVECType OutData_IntegerPortion"], "column": 1, "comment": " return the fractional part, parameter 2 is the output of the integer part, the sign of these two parts will be the same as the input value", "file": "", "line": 1, "name": "modf", "type": "floatVECType", "usage": "function"}, {"args": ["anytype Data1", "anytype Data2"], "column": 1, "comment": "", "file": "", "line": 1, "name": "mul", "type": "anytype", "usage": "function"}, {"args": ["VECType param"], "column": 1, "comment": " Can only be used in ShaderModel1", "file": "", "line": 1, "name": "noise", "type": "float", "usage": "function"}, {"args": ["VECType Data"], "column": 1, "comment": "", "file": "", "line": 1, "name": "normalize", "type": "VECType", "usage": "function"}, {"args": ["anytype Data1", "anytype Data2"], "column": 1, "comment": " Judgment for each element of Data1 and Data2", "file": "", "line": 1, "name": "notEqual", "type": "boolVECType", "usage": "function"}, {"args": ["vec2 e"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\octahedron-transform.chunk", "line": 14, "name": "oct_to_float32x3", "type": "vec3", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "out", "type": "", "usage": "keyword"}, {"args": ["float depth"], "column": 5, "comment": " float <--> RGBA8", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 4, "name": "packDepthToRGBA", "type": "vec4", "usage": "function"}, {"args": ["out type mainPart", "out type modPart", "highp type data"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 24, "name": "packHighpData", "type": "void", "usage": "function"}, {"args": ["out type mainPart", "out type modPart", "highp type data", "const float modValue"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 32, "name": "packHighpData", "type": "void", "usage": "function"}, {"args": ["vec3 rgb"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\unpack.chunk", "line": 15, "name": "packRGBE", "type": "vec4", "usage": "function"}, {"args": ["anytype Base", "anytype Power"], "column": 1, "comment": "", "file": "", "line": 1, "name": "pow", "type": "anytype", "usage": "function"}, {"args": ["vec4 a", "vec4 b"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 129, "name": "quat<PERSON><PERSON><PERSON><PERSON>", "type": "vec4", "usage": "function"}, {"args": ["vec4 q"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 247, "name": "quatToEuler", "type": "vec3", "usage": "function"}, {"args": ["vec4 q"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 168, "name": "quatToMat3", "type": "mat3", "usage": "function"}, {"args": ["vec4 q"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 184, "name": "quatToMat4", "type": "mat4", "usage": "function"}, {"args": ["vec3 xAxis", "vec3 yAxis", "vec3 zAxis"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 11, "name": "quaternionFromAxis", "type": "vec4", "usage": "function"}, {"args": ["float angle", "vec3 axis"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 2, "name": "quaternionFromAxisAngle", "type": "vec4", "usage": "function"}, {"args": ["vec3 angle"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 54, "name": "quaternionFromEuler", "type": "vec4", "usage": "function"}, {"args": ["floatVECType Value_Degree"], "column": 1, "comment": " Turn degrees into radians", "file": "", "line": 1, "name": "radians", "type": "floatVECType", "usage": "function"}, {"args": ["vec2 seeds_zero_to_one"], "column": 6, "comment": " random number\r\n seeds value must be between zero to one, otherwise get 0\r\n such as fract(cc_time.z * FSInput_texcoord)", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 60, "name": "rand", "type": "float", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": "", "file": "", "line": 1, "name": "rcp", "type": "anytype", "usage": "function"}, {"args": ["VECType IncidentDirection", "VECType Normal"], "column": 1, "comment": " IncidentDirection is from elsewhere to pixel", "file": "", "line": 1, "name": "reflect", "type": "VECType", "usage": "function"}, {"args": ["VECType IncidentDirection", "VECType Normal", "float ior"], "column": 1, "comment": " IncidentDirection is from elsewhere to pixel", "file": "", "line": 1, "name": "refract", "type": "VECType", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "return", "type": "", "usage": "keyword"}, {"args": ["inout vec2 corner", "float angle"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 161, "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "void", "usage": "function"}, {"args": ["vec3 pos", "vec3 xAxis", "vec3 yAxis", "vec3 zAxis", "vec4 q"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 154, "name": "rotateInLocalSpace", "type": "vec3", "usage": "function"}, {"args": ["vec3 v", "vec3 axis", "float theta"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 150, "name": "rotateVecFromAxis", "type": "vec3", "usage": "function"}, {"args": ["inout vec3 v", "vec4 q"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 138, "name": "rotateVecFromQuat", "type": "void", "usage": "function"}, {"args": ["float value"], "column": 8, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 33, "name": "round", "type": "float", "usage": "function"}, {"args": ["vec2 value"], "column": 7, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 37, "name": "round", "type": "vec2", "usage": "function"}, {"args": ["vec3 value"], "column": 7, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 38, "name": "round", "type": "vec3", "usage": "function"}, {"args": ["vec4 value"], "column": 7, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 39, "name": "round", "type": "vec4", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": " Rounding", "file": "", "line": 1, "name": "round", "type": "anytype", "usage": "function"}, {"args": ["float value"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 42, "name": "rsqrt", "type": "float", "usage": "function"}, {"args": ["vec2 value"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 45, "name": "rsqrt", "type": "vec2", "usage": "function"}, {"args": ["vec3 value"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 46, "name": "rsqrt", "type": "vec3", "usage": "function"}, {"args": ["vec4 value"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 47, "name": "rsqrt", "type": "vec4", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "sampler1D", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "sampler1DShadow", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "sampler2D", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "sampler2DShadow", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "sampler3D", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "samplerCube", "type": "", "usage": "keyword"}, {"args": ["float value"], "column": 6, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 49, "name": "saturate", "type": "float", "usage": "function"}, {"args": ["vec2 value"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 52, "name": "saturate", "type": "vec2", "usage": "function"}, {"args": ["vec3 value"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 53, "name": "saturate", "type": "vec3", "usage": "function"}, {"args": ["vec4 value"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\number.chunk", "line": 54, "name": "saturate", "type": "vec4", "usage": "function"}, {"args": ["anytype Data_ClampTo_0_and_1"], "column": 1, "comment": "", "file": "", "line": 1, "name": "saturate", "type": "anytype", "usage": "function"}, {"args": ["inout mat4 m", "float s"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\math\\transform.chunk", "line": 123, "name": "scaleMatrix", "type": "void", "usage": "function"}, {"args": ["anytype GetSign_LessThanZero_Or_MoreThanZero_Or_Zero"], "column": 1, "comment": "", "file": "", "line": 1, "name": "sign", "type": "INTVECType", "usage": "function"}, {"args": ["vec2 v"], "column": 5, "comment": " Returns +/-1", "file": "editor\\assets\\chunks\\common\\math\\octahedron-transform.chunk", "line": 2, "name": "signNotZero", "type": "vec2", "usage": "function"}, {"args": ["anytype Value_Radian"], "column": 1, "comment": "", "file": "", "line": 1, "name": "sin", "type": "anytype", "usage": "function"}, {"args": ["anytype Value_Radian", "out anytype SinValue", "out anytype CosValue"], "column": 1, "comment": " Returns both sine and cosine", "file": "", "line": 1, "name": "sincos", "type": "void", "usage": "function"}, {"args": ["anytype min", "anytype max", "anytype interpolate"], "column": 1, "comment": " Do Hermite smoothing for the specified parameter 3, so that it changes from linear to curved, if the parameter 3 falls outside the min and max range, then <PERSON><PERSON>, note that it is not a real Hermite curve, does not involve the tangent vector, there is not much controllability, can only be seen as a typical Hermite curve smoothing only", "file": "", "line": 1, "name": "smoothstep", "type": "anytype", "usage": "function"}, {"args": ["anytype Data"], "column": 1, "comment": "", "file": "", "line": 1, "name": "sqrt", "type": "anytype", "usage": "function"}, {"args": ["anytype A", "anytype B"], "column": 1, "comment": " return A <= B or B >= A floating point number, remove the bool value conversion", "file": "", "line": 1, "name": "step", "type": "float", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "struct", "type": "", "usage": "keyword"}, {"args": ["anytype Value_Radian"], "column": 1, "comment": "", "file": "", "line": 1, "name": "tan", "type": "anytype", "usage": "function"}, {"args": ["sampler any_type_tex", "floatVECType vTexelCoord", "int lod"], "column": 1, "comment": "", "file": "", "line": 1, "name": "texelFetch", "type": "anytype", "usage": "function"}, {"args": ["sampler any_type_tex", "floatVECType vTexelCoord", "int lod", "INTVECType vOffset"], "column": 1, "comment": "", "file": "", "line": 1, "name": "texelFetchOffset", "type": "anytype", "usage": "function"}, {"args": ["sampler any_type_tex", "floatVECType vTextureCoord", "__opt float bias"], "column": 1, "comment": "", "file": "", "line": 1, "name": "texture", "type": "anytype", "usage": "function"}, {"args": ["sampler2D ss", "float2 v2TextureCoord"], "column": 1, "comment": "", "file": "", "line": 1, "name": "texture2D", "type": "anytype", "usage": "function"}, {"args": ["sampler2D ss", "float2 v2TextureCoord", "float lod"], "column": 1, "comment": "", "file": "", "line": 1, "name": "texture2DLodEXT", "type": "anytype", "usage": "function"}, {"args": ["samplerCUBE ss", "float3 v3TextureCoord"], "column": 1, "comment": "", "file": "", "line": 1, "name": "textureCUBE", "type": "anytype", "usage": "function"}, {"args": ["samplerCUBE ss", "float3 v3TextureCoord", "float lod"], "column": 1, "comment": "", "file": "", "line": 1, "name": "textureCUBELodEXT", "type": "anytype", "usage": "function"}, {"args": ["sampler any_type_tex", "floatVECType vTextureCoord", "float lod"], "column": 1, "comment": "", "file": "", "line": 1, "name": "textureGrad", "type": "anytype", "usage": "function"}, {"args": ["sampler any_type_tex", "floatVECType vTextureCoord", "float lod", "INTVECType vOffset"], "column": 1, "comment": "", "file": "", "line": 1, "name": "textureGradOffset", "type": "anytype", "usage": "function"}, {"args": ["sampler any_type_tex", "floatVECType vTextureCoord", "float lod"], "column": 1, "comment": "", "file": "", "line": 1, "name": "textureLod", "type": "anytype", "usage": "function"}, {"args": ["sampler any_type_tex", "floatVECType vTextureCoord", "float lod", "INTVECType vOffset"], "column": 1, "comment": "", "file": "", "line": 1, "name": "textureLodOffset", "type": "anytype", "usage": "function"}, {"args": ["sampler any_type_tex", "floatVECType vTextureCoord", "INTVECType vOffset", "__opt float bias"], "column": 1, "comment": "", "file": "", "line": 1, "name": "textureOffset", "type": "anytype", "usage": "function"}, {"args": ["sampler any_type_tex"], "column": 1, "comment": " Query the number of mip, __VERSION__>=430", "file": "", "line": 1, "name": "textureQueryLevels", "type": "int", "usage": "function"}, {"args": ["sampler any_type_tex", "floatVECType vTextureCoord"], "column": 1, "comment": "", "file": "", "line": 1, "name": "textureQueryLod", "type": "vec2", "usage": "function"}, {"args": ["sampler MSAAtex"], "column": 1, "comment": " Query the number of MSAA sampling points, __VERSION__>=450", "file": "", "line": 1, "name": "textureSamples", "type": "int", "usage": "function"}, {"args": ["sampler any_type_tex", "int lod"], "column": 1, "comment": "", "file": "", "line": 1, "name": "textureSize", "type": "INTVECType", "usage": "function"}, {"args": ["MAT4 Data"], "column": 1, "comment": "", "file": "", "line": 1, "name": "transpose", "type": "MAT4", "usage": "function"}, {"args": ["mat3 v"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\lighting\\rect-area-light.chunk", "line": 3, "name": "transposeMat3", "type": "mat3", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "true", "type": "", "usage": "keyword"}, {"args": ["anytype Data"], "column": 1, "comment": " Fetching integer parts", "file": "", "line": 1, "name": "trunc", "type": "anytype", "usage": "function"}, {"args": ["UINTVECType value"], "column": 1, "comment": " equivalent to asfloat, which reads UINT numbers as floating point values bit by bit, without changing the bit value", "file": "", "line": 1, "name": "uintBitsToFloat", "type": "floatVECType", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "uniform", "type": "", "usage": "keyword"}, {"args": ["type mainPart", "type modPart"], "column": 11, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 20, "name": "unpackHighpData", "type": "type", "usage": "function"}, {"args": ["type mainPart", "type modPart", "const float modValue"], "column": 11, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 28, "name": "unpackHighpData", "type": "type", "usage": "function"}, {"args": ["color"], "column": 1, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\packing.chunk", "line": 11, "name": "unpackRGBAToDepth", "type": "", "usage": "macro"}, {"args": ["vec4 rgbe"], "column": 5, "comment": "", "file": "editor\\assets\\chunks\\common\\data\\unpack.chunk", "line": 29, "name": "unpackRGBE", "type": "vec3", "usage": "function"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "varying", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "vec2", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "vec3", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "vec4", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "void", "type": "", "usage": "keyword"}, {"args": [], "column": 1, "comment": "", "file": "", "line": 1, "name": "while", "type": "", "usage": "keyword"}]}}