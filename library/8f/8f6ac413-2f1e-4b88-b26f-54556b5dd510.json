{"__type__": "cc.EffectAsset", "_name": "pipeline/float-output-process", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "techniques": [{"passes": [{"pass": "copy-pass", "depthTest": false, "depthWrite": false, "program": "pipeline/float-output-process|tonemap-vs|copy-fs"}, {"pass": "tone-mapping", "rasterizerState": {"cullMode": 0}, "program": "pipeline/float-output-process|tonemap-vs|tonemap-fs", "depthStencilState": {"depthTest": false, "depthWrite": false}}]}], "shaders": [{"blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_normal", "defines": [], "format": 32, "location": 1}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 2}, {"name": "a_tangent", "defines": [], "format": 44, "location": 3}, {"name": "a_joints", "defines": ["CC_USE_SKINNING"], "location": 4}, {"name": "a_weights", "defines": ["CC_USE_SKINNING"], "format": 44, "location": 5}, {"name": "a_jointAnimInfo", "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"], "format": 44, "isInstanced": true, "location": 6}, {"name": "a_matWorld0", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 7}, {"name": "a_matWorld1", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 8}, {"name": "a_matWorld2", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 9}, {"name": "a_lightingMapUVParam", "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"], "format": 44, "isInstanced": true, "location": 10}, {"name": "a_localShadowBiasAndProbeId", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 11}, {"name": "a_reflectionProbeData", "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"], "format": 44, "isInstanced": true, "location": 12}, {"name": "a_sh_linear_const_r", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 13}, {"name": "a_sh_linear_const_g", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 14}, {"name": "a_sh_linear_const_b", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 15}, {"name": "a_vertexId", "defines": ["CC_USE_MORPH"], "format": 11, "location": 16}], "varyings": [{"name": "v_uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}], "fragColors": [{"name": "fragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [{"name": "depthRaw", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 0}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 3626846176, "glsl4": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec3 a_normal;\nlayout(location = 2) in vec2 a_texCoord;\nlayout(location = 3) in vec4 a_tangent;\n#if CC_USE_SKINNING\n    layout(location = 4) in u32vec4 a_joints;\n  layout(location = 5) in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    layout(location = 6) in highp vec4 a_jointAnimInfo;\n  #endif\n  layout(location = 7) in vec4 a_matWorld0;\n  layout(location = 8) in vec4 a_matWorld1;\n  layout(location = 9) in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    layout(location = 10) in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    layout(location = 11) in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    layout(location = 12) in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    layout(location = 13) in vec4 a_sh_linear_const_r;\n    layout(location = 14) in vec4 a_sh_linear_const_g;\n    layout(location = 15) in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n#endif\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(location = 0) out vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 packDepthToRGBA (float depth) {\n  vec4 ret = vec4(1.0, 255.0, 65025.0, 16581375.0) * depth;\n  ret = fract(ret);\n  ret -= vec4(ret.yzw, 0.0) / 255.0;\n  return ret;\n}\n#define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\nlayout(set = 1, binding = 0) uniform sampler2D depthRaw;\nlayout(location = 0) in vec2 v_uv;\nlayout(location = 0) out vec4 fragColor;\nvoid main() {\n  float depth = texture(depthRaw, v_uv).r;\n  fragColor = packDepthToRGBA(depth);\n}"}, "glsl3": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\nin vec4 a_tangent;\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n#endif\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nout vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 packDepthToRGBA (float depth) {\n  vec4 ret = vec4(1.0, 255.0, 65025.0, 16581375.0) * depth;\n  ret = fract(ret);\n  ret -= vec4(ret.yzw, 0.0) / 255.0;\n  return ret;\n}\n#define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\nuniform sampler2D depthRaw;\nin vec2 v_uv;\nlayout(location = 0) out vec4 fragColor;\nvoid main() {\n  float depth = texture(depthRaw, v_uv).r;\n  fragColor = packDepthToRGBA(depth);\n}"}, "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n#endif\nuniform highp vec4 cc_cameraPos;\nvarying vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nvec4 packDepthToRGBA (float depth) {\n  vec4 ret = vec4(1.0, 255.0, 65025.0, 16581375.0) * depth;\n  ret = fract(ret);\n  ret -= vec4(ret.yzw, 0.0) / 255.0;\n  return ret;\n}\n#define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\nuniform sampler2D depthRaw;\nvarying vec2 v_uv;\nvoid main() {\n  float depth = texture2D(depthRaw, v_uv).r;\n  gl_FragColor = packDepthToRGBA(depth);\n}"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 42, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 42}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean", "defines": [], "editor": {"elevated": true}}, {"name": "CC_USE_SKINNING", "type": "boolean", "defines": []}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHTMAP", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_MORPH", "type": "boolean", "defines": []}], "name": "pipeline/float-output-process|tonemap-vs|copy-fs"}, {"blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_normal", "defines": [], "format": 32, "location": 1}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 2}, {"name": "a_tangent", "defines": [], "format": 44, "location": 3}, {"name": "a_joints", "defines": ["CC_USE_SKINNING"], "location": 4}, {"name": "a_weights", "defines": ["CC_USE_SKINNING"], "format": 44, "location": 5}, {"name": "a_jointAnimInfo", "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"], "format": 44, "isInstanced": true, "location": 6}, {"name": "a_matWorld0", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 7}, {"name": "a_matWorld1", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 8}, {"name": "a_matWorld2", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 9}, {"name": "a_lightingMapUVParam", "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"], "format": 44, "isInstanced": true, "location": 10}, {"name": "a_localShadowBiasAndProbeId", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 11}, {"name": "a_reflectionProbeData", "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"], "format": 44, "isInstanced": true, "location": 12}, {"name": "a_sh_linear_const_r", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 13}, {"name": "a_sh_linear_const_g", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 14}, {"name": "a_sh_linear_const_b", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 15}, {"name": "a_vertexId", "defines": ["CC_USE_MORPH"], "format": 11, "location": 16}], "varyings": [{"name": "v_uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}], "fragColors": [{"name": "fragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [{"name": "u_texSampler", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 0}, {"name": "DepthTex", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 1}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 537169463, "glsl4": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec3 a_normal;\nlayout(location = 2) in vec2 a_texCoord;\nlayout(location = 3) in vec4 a_tangent;\n#if CC_USE_SKINNING\n    layout(location = 4) in u32vec4 a_joints;\n  layout(location = 5) in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    layout(location = 6) in highp vec4 a_jointAnimInfo;\n  #endif\n  layout(location = 7) in vec4 a_matWorld0;\n  layout(location = 8) in vec4 a_matWorld1;\n  layout(location = 9) in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    layout(location = 10) in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    layout(location = 11) in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    layout(location = 12) in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    layout(location = 13) in vec4 a_sh_linear_const_r;\n    layout(location = 14) in vec4 a_sh_linear_const_g;\n    layout(location = 15) in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n#endif\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(location = 0) out vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\n#define CC_SURFACES_ENABLE_DEBUG_VIEW 1\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_COLOR 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_NORMAL CC_SURFACES_DEBUG_VIEW_VERTEX_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_TANGENT CC_SURFACES_DEBUG_VIEW_VERTEX_NORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_WORLD_POS CC_SURFACES_DEBUG_VIEW_VERTEX_TANGENT + 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_MIRROR CC_SURFACES_DEBUG_VIEW_WORLD_POS + 1\n#define CC_SURFACES_DEBUG_VIEW_FACE_SIDE CC_SURFACES_DEBUG_VIEW_VERTEX_MIRROR + 1\n#define CC_SURFACES_DEBUG_VIEW_UV0 CC_SURFACES_DEBUG_VIEW_FACE_SIDE + 1\n#define CC_SURFACES_DEBUG_VIEW_UV1 CC_SURFACES_DEBUG_VIEW_UV0 + 1\n#define CC_SURFACES_DEBUG_VIEW_UVLIGHTMAP CC_SURFACES_DEBUG_VIEW_UV1 + 1\n#define CC_SURFACES_DEBUG_VIEW_PROJ_DEPTH CC_SURFACES_DEBUG_VIEW_UVLIGHTMAP + 1\n#define CC_SURFACES_DEBUG_VIEW_LINEAR_DEPTH CC_SURFACES_DEBUG_VIEW_PROJ_DEPTH + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL CC_SURFACES_DEBUG_VIEW_LINEAR_DEPTH + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT + 1\n#define CC_SURFACES_DEBUG_VIEW_BASE_COLOR CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR CC_SURFACES_DEBUG_VIEW_BASE_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSPARENCY CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_METALLIC CC_SURFACES_DEBUG_VIEW_TRANSPARENCY + 1\n#define CC_SURFACES_DEBUG_VIEW_ROUGHNESS CC_SURFACES_DEBUG_VIEW_METALLIC + 1\n#define CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY CC_SURFACES_DEBUG_VIEW_ROUGHNESS + 1\n#define CC_SURFACES_DEBUG_VIEW_IOR CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_DIFFUSE CC_SURFACES_DEBUG_VIEW_IOR + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_SPECULAR CC_SURFACES_DEBUG_VIEW_DIRECT_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_ALL CC_SURFACES_DEBUG_VIEW_DIRECT_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_DIFFUSE CC_SURFACES_DEBUG_VIEW_DIRECT_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_SPECULAR CC_SURFACES_DEBUG_VIEW_ENV_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_ALL CC_SURFACES_DEBUG_VIEW_ENV_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_EMISSIVE CC_SURFACES_DEBUG_VIEW_ENV_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_LIGHT_MAP CC_SURFACES_DEBUG_VIEW_EMISSIVE + 1\n#define CC_SURFACES_DEBUG_VIEW_SHADOW CC_SURFACES_DEBUG_VIEW_LIGHT_MAP + 1\n#define CC_SURFACES_DEBUG_VIEW_AO CC_SURFACES_DEBUG_VIEW_SHADOW + 1\n#define CC_SURFACES_DEBUG_VIEW_FRESNEL CC_SURFACES_DEBUG_VIEW_AO + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_DIFFUSE CC_SURFACES_DEBUG_VIEW_FRESNEL + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_DIFFUSE CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ALL CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_2ND_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_ENVIRONMENT_2ND_SPECULAR CC_SURFACES_DEBUG_VIEW_DIRECT_2ND_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_2ND_SPECULAR_ALL CC_SURFACES_DEBUG_VIEW_ENVIRONMENT_2ND_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_FOG CC_SURFACES_DEBUG_VIEW_2ND_SPECULAR_ALL + 1\n#define IS_DEBUG_VIEW_ENABLE_WITH_CAMERA (cc_surfaceTransform.y != 3.0)\nbool equalf_mode(float data1, float data2) { return abs(float(data1) - float(data2)) < 0.001; }\n#define IS_DEBUG_VIEW_LIGHTING_ENABLE_WITH_ALBEDO (UnpackBitFromFloat(cc_debug_view_mode.w, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_MISC_ENABLE_CSM_LAYER_COLORATION (UnpackBitFromFloat(cc_debug_view_mode.w, 7) && IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_DIRECT_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.y, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_DIRECT_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.y, 1) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_ENV_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.y, 2) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_ENV_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.y, 3) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_EMISSIVE (UnpackBitFromFloat(cc_debug_view_mode.y, 4) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_LIGHT_MAP (UnpackBitFromFloat(cc_debug_view_mode.y, 5) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_SHADOW (UnpackBitFromFloat(cc_debug_view_mode.y, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_AO (UnpackBitFromFloat(cc_debug_view_mode.y, 7) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_NORMAL_MAP (UnpackBitFromFloat(cc_debug_view_mode.z, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_FOG (UnpackBitFromFloat(cc_debug_view_mode.z, 1) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TONE_MAPPING (UnpackBitFromFloat(cc_debug_view_mode.z, 2) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION (UnpackBitFromFloat(cc_debug_view_mode.z, 3) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_FRESNEL (UnpackBitFromFloat(cc_debug_view_mode.z, 4) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TRANSMIT_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.z, 5) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TRANSMIT_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.z, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_2ND_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.z, 7) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TT (UnpackBitFromFloat(cc_debug_view_mode.w, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\nbool DebugViewNeedDisplayOriginalData() {\n    return\n    (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL)) && (cc_surfaceTransform.y != 3.0)) ||\n    (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_TRANSPARENCY)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_BASE_COLOR)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR)) && (cc_surfaceTransform.y != 3.0)) ||\n    (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_ROUGHNESS)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_METALLIC)) && (cc_surfaceTransform.y != 3.0)) ||\n    (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_IOR)) && (cc_surfaceTransform.y != 3.0))\n    ;\n}\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec3 HDRToLDR(vec3 color)\n{\n  #if CC_USE_HDR\n    #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n      if (IS_DEBUG_VIEW_COMPOSITE_ENABLE_TONE_MAPPING)\n    #endif\n    {\n    #if CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    }\n  #endif\n  return color;\n}\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetCameraDepthRH(float depthHS, mat4 matProj)\n{\n    return -matProj[3][2] / (depthHS + matProj[2][2]);\n}\nfloat GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n{\n    return -matProj32 / (depthHS + matProj22);\n}\nvec4 GetWorldPosFromNDCPosRH(vec3 posHS, mat4 matProj, mat4 matViewProjInv)\n{\n    float w = -GetCameraDepthRH(posHS.z, matProj);\n    return matViewProjInv * vec4(posHS * w, w);\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\nvoid CC_APPLY_FOG_BASE(inout vec4 color, float factor) {\n\tcolor = vec4(mix(cc_fogColor.rgb, color.rgb, factor), color.a);\n}\nlayout(location = 0) in vec2 v_uv;\nlayout(set = 1, binding = 0) uniform sampler2D u_texSampler;\nlayout(set = 1, binding = 1) uniform sampler2D DepthTex;\nlayout(location = 0) out vec4 fragColor;\nvec4 CCFragOutput_PostProcess(vec4 color) {\n  vec4 worldPos = vec4(0.0);\n  #if CC_USE_FOG != 4\n    float depth = dot(texture(DepthTex, v_uv), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0));\n    vec3 posHS = vec3(v_uv, depth) * 2.0 - vec3(1.0);\n    posHS.xy = cc_cameraPos.w == 0.0 ? vec2(posHS.xy.x, -posHS.xy.y) : posHS.xy;\n    worldPos = GetWorldPosFromNDCPosRH(posHS, cc_matProj, cc_matViewProjInv);\n  #endif\n  #if CC_USE_FOG != 4\n    float fogFactor = 1.0;\n    CC_TRANSFER_FOG_BASE(worldPos, fogFactor);\n    CC_APPLY_FOG_BASE(color, fogFactor);\n  #endif\n  if (!DebugViewNeedDisplayOriginalData()) {\n    #if CC_USE_FLOAT_OUTPUT\n      color.rgb = HDRToLDR(color.rgb);\n      color.rgb = LinearToSRGB(color.rgb);\n    #endif\n  }\n  return color;\n}\nvoid main () {\n  vec4 o = texture(u_texSampler, v_uv);\n  fragColor = CCFragOutput_PostProcess(o);\n}"}, "glsl3": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\nin vec4 a_tangent;\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n#endif\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nout vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\n#define CC_SURFACES_ENABLE_DEBUG_VIEW 1\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_COLOR 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_NORMAL CC_SURFACES_DEBUG_VIEW_VERTEX_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_TANGENT CC_SURFACES_DEBUG_VIEW_VERTEX_NORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_WORLD_POS CC_SURFACES_DEBUG_VIEW_VERTEX_TANGENT + 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_MIRROR CC_SURFACES_DEBUG_VIEW_WORLD_POS + 1\n#define CC_SURFACES_DEBUG_VIEW_FACE_SIDE CC_SURFACES_DEBUG_VIEW_VERTEX_MIRROR + 1\n#define CC_SURFACES_DEBUG_VIEW_UV0 CC_SURFACES_DEBUG_VIEW_FACE_SIDE + 1\n#define CC_SURFACES_DEBUG_VIEW_UV1 CC_SURFACES_DEBUG_VIEW_UV0 + 1\n#define CC_SURFACES_DEBUG_VIEW_UVLIGHTMAP CC_SURFACES_DEBUG_VIEW_UV1 + 1\n#define CC_SURFACES_DEBUG_VIEW_PROJ_DEPTH CC_SURFACES_DEBUG_VIEW_UVLIGHTMAP + 1\n#define CC_SURFACES_DEBUG_VIEW_LINEAR_DEPTH CC_SURFACES_DEBUG_VIEW_PROJ_DEPTH + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL CC_SURFACES_DEBUG_VIEW_LINEAR_DEPTH + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT + 1\n#define CC_SURFACES_DEBUG_VIEW_BASE_COLOR CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR CC_SURFACES_DEBUG_VIEW_BASE_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSPARENCY CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_METALLIC CC_SURFACES_DEBUG_VIEW_TRANSPARENCY + 1\n#define CC_SURFACES_DEBUG_VIEW_ROUGHNESS CC_SURFACES_DEBUG_VIEW_METALLIC + 1\n#define CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY CC_SURFACES_DEBUG_VIEW_ROUGHNESS + 1\n#define CC_SURFACES_DEBUG_VIEW_IOR CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_DIFFUSE CC_SURFACES_DEBUG_VIEW_IOR + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_SPECULAR CC_SURFACES_DEBUG_VIEW_DIRECT_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_ALL CC_SURFACES_DEBUG_VIEW_DIRECT_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_DIFFUSE CC_SURFACES_DEBUG_VIEW_DIRECT_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_SPECULAR CC_SURFACES_DEBUG_VIEW_ENV_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_ALL CC_SURFACES_DEBUG_VIEW_ENV_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_EMISSIVE CC_SURFACES_DEBUG_VIEW_ENV_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_LIGHT_MAP CC_SURFACES_DEBUG_VIEW_EMISSIVE + 1\n#define CC_SURFACES_DEBUG_VIEW_SHADOW CC_SURFACES_DEBUG_VIEW_LIGHT_MAP + 1\n#define CC_SURFACES_DEBUG_VIEW_AO CC_SURFACES_DEBUG_VIEW_SHADOW + 1\n#define CC_SURFACES_DEBUG_VIEW_FRESNEL CC_SURFACES_DEBUG_VIEW_AO + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_DIFFUSE CC_SURFACES_DEBUG_VIEW_FRESNEL + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_DIFFUSE CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ALL CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_2ND_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_ENVIRONMENT_2ND_SPECULAR CC_SURFACES_DEBUG_VIEW_DIRECT_2ND_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_2ND_SPECULAR_ALL CC_SURFACES_DEBUG_VIEW_ENVIRONMENT_2ND_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_FOG CC_SURFACES_DEBUG_VIEW_2ND_SPECULAR_ALL + 1\n#define IS_DEBUG_VIEW_ENABLE_WITH_CAMERA (cc_surfaceTransform.y != 3.0)\nbool equalf_mode(float data1, float data2) { return abs(float(data1) - float(data2)) < 0.001; }\n#define IS_DEBUG_VIEW_LIGHTING_ENABLE_WITH_ALBEDO (UnpackBitFromFloat(cc_debug_view_mode.w, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_MISC_ENABLE_CSM_LAYER_COLORATION (UnpackBitFromFloat(cc_debug_view_mode.w, 7) && IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_DIRECT_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.y, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_DIRECT_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.y, 1) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_ENV_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.y, 2) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_ENV_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.y, 3) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_EMISSIVE (UnpackBitFromFloat(cc_debug_view_mode.y, 4) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_LIGHT_MAP (UnpackBitFromFloat(cc_debug_view_mode.y, 5) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_SHADOW (UnpackBitFromFloat(cc_debug_view_mode.y, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_AO (UnpackBitFromFloat(cc_debug_view_mode.y, 7) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_NORMAL_MAP (UnpackBitFromFloat(cc_debug_view_mode.z, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_FOG (UnpackBitFromFloat(cc_debug_view_mode.z, 1) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TONE_MAPPING (UnpackBitFromFloat(cc_debug_view_mode.z, 2) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION (UnpackBitFromFloat(cc_debug_view_mode.z, 3) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_FRESNEL (UnpackBitFromFloat(cc_debug_view_mode.z, 4) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TRANSMIT_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.z, 5) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TRANSMIT_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.z, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_2ND_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.z, 7) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TT (UnpackBitFromFloat(cc_debug_view_mode.w, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\nbool DebugViewNeedDisplayOriginalData() {\n    return\n    (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL)) && (cc_surfaceTransform.y != 3.0)) ||\n    (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_TRANSPARENCY)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_BASE_COLOR)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR)) && (cc_surfaceTransform.y != 3.0)) ||\n    (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_ROUGHNESS)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_METALLIC)) && (cc_surfaceTransform.y != 3.0)) ||\n    (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_IOR)) && (cc_surfaceTransform.y != 3.0))\n    ;\n}\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec3 HDRToLDR(vec3 color)\n{\n  #if CC_USE_HDR\n    #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n      if (IS_DEBUG_VIEW_COMPOSITE_ENABLE_TONE_MAPPING)\n    #endif\n    {\n    #if CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    }\n  #endif\n  return color;\n}\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetCameraDepthRH(float depthHS, mat4 matProj)\n{\n    return -matProj[3][2] / (depthHS + matProj[2][2]);\n}\nfloat GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n{\n    return -matProj32 / (depthHS + matProj22);\n}\nvec4 GetWorldPosFromNDCPosRH(vec3 posHS, mat4 matProj, mat4 matViewProjInv)\n{\n    float w = -GetCameraDepthRH(posHS.z, matProj);\n    return matViewProjInv * vec4(posHS * w, w);\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\nvoid CC_APPLY_FOG_BASE(inout vec4 color, float factor) {\n\tcolor = vec4(mix(cc_fogColor.rgb, color.rgb, factor), color.a);\n}\nin vec2 v_uv;\nuniform sampler2D u_texSampler;\nuniform sampler2D DepthTex;\nlayout(location = 0) out vec4 fragColor;\nvec4 CCFragOutput_PostProcess(vec4 color) {\n  vec4 worldPos = vec4(0.0);\n  #if CC_USE_FOG != 4\n    float depth = dot(texture(DepthTex, v_uv), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0));\n    vec3 posHS = vec3(v_uv, depth) * 2.0 - vec3(1.0);\n    posHS.xy = cc_cameraPos.w == 0.0 ? vec2(posHS.xy.x, -posHS.xy.y) : posHS.xy;\n    worldPos = GetWorldPosFromNDCPosRH(posHS, cc_matProj, cc_matViewProjInv);\n  #endif\n  #if CC_USE_FOG != 4\n    float fogFactor = 1.0;\n    CC_TRANSFER_FOG_BASE(worldPos, fogFactor);\n    CC_APPLY_FOG_BASE(color, fogFactor);\n  #endif\n  if (!DebugViewNeedDisplayOriginalData()) {\n    #if CC_USE_FLOAT_OUTPUT\n      color.rgb = HDRToLDR(color.rgb);\n      color.rgb = LinearToSRGB(color.rgb);\n    #endif\n  }\n  return color;\n}\nvoid main () {\n  vec4 o = texture(u_texSampler, v_uv);\n  fragColor = CCFragOutput_PostProcess(o);\n}"}, "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n#endif\nuniform highp vec4 cc_cameraPos;\nvarying vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  In.position.xy = cc_cameraPos.w == 0.0 ? vec2(In.position.xy.x, -In.position.xy.y) : In.position.xy;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\n#define CC_SURFACES_ENABLE_DEBUG_VIEW 1\nuniform mediump vec4 cc_debug_view_mode;\nuniform highp mat4 cc_matProj;\n  uniform highp mat4 cc_matViewProjInv;\n  uniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_surfaceTransform;\n  uniform mediump vec4 cc_fogColor;\n  uniform mediump vec4 cc_fogBase;\n  uniform mediump vec4 cc_fogAdd;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\n#define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_COLOR 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_NORMAL CC_SURFACES_DEBUG_VIEW_VERTEX_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_TANGENT CC_SURFACES_DEBUG_VIEW_VERTEX_NORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_WORLD_POS CC_SURFACES_DEBUG_VIEW_VERTEX_TANGENT + 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_MIRROR CC_SURFACES_DEBUG_VIEW_WORLD_POS + 1\n#define CC_SURFACES_DEBUG_VIEW_FACE_SIDE CC_SURFACES_DEBUG_VIEW_VERTEX_MIRROR + 1\n#define CC_SURFACES_DEBUG_VIEW_UV0 CC_SURFACES_DEBUG_VIEW_FACE_SIDE + 1\n#define CC_SURFACES_DEBUG_VIEW_UV1 CC_SURFACES_DEBUG_VIEW_UV0 + 1\n#define CC_SURFACES_DEBUG_VIEW_UVLIGHTMAP CC_SURFACES_DEBUG_VIEW_UV1 + 1\n#define CC_SURFACES_DEBUG_VIEW_PROJ_DEPTH CC_SURFACES_DEBUG_VIEW_UVLIGHTMAP + 1\n#define CC_SURFACES_DEBUG_VIEW_LINEAR_DEPTH CC_SURFACES_DEBUG_VIEW_PROJ_DEPTH + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL CC_SURFACES_DEBUG_VIEW_LINEAR_DEPTH + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT + 1\n#define CC_SURFACES_DEBUG_VIEW_BASE_COLOR CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR CC_SURFACES_DEBUG_VIEW_BASE_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSPARENCY CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_METALLIC CC_SURFACES_DEBUG_VIEW_TRANSPARENCY + 1\n#define CC_SURFACES_DEBUG_VIEW_ROUGHNESS CC_SURFACES_DEBUG_VIEW_METALLIC + 1\n#define CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY CC_SURFACES_DEBUG_VIEW_ROUGHNESS + 1\n#define CC_SURFACES_DEBUG_VIEW_IOR CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_DIFFUSE CC_SURFACES_DEBUG_VIEW_IOR + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_SPECULAR CC_SURFACES_DEBUG_VIEW_DIRECT_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_ALL CC_SURFACES_DEBUG_VIEW_DIRECT_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_DIFFUSE CC_SURFACES_DEBUG_VIEW_DIRECT_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_SPECULAR CC_SURFACES_DEBUG_VIEW_ENV_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_ALL CC_SURFACES_DEBUG_VIEW_ENV_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_EMISSIVE CC_SURFACES_DEBUG_VIEW_ENV_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_LIGHT_MAP CC_SURFACES_DEBUG_VIEW_EMISSIVE + 1\n#define CC_SURFACES_DEBUG_VIEW_SHADOW CC_SURFACES_DEBUG_VIEW_LIGHT_MAP + 1\n#define CC_SURFACES_DEBUG_VIEW_AO CC_SURFACES_DEBUG_VIEW_SHADOW + 1\n#define CC_SURFACES_DEBUG_VIEW_FRESNEL CC_SURFACES_DEBUG_VIEW_AO + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_DIFFUSE CC_SURFACES_DEBUG_VIEW_FRESNEL + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_DIFFUSE CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ALL CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_2ND_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_ENVIRONMENT_2ND_SPECULAR CC_SURFACES_DEBUG_VIEW_DIRECT_2ND_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_2ND_SPECULAR_ALL CC_SURFACES_DEBUG_VIEW_ENVIRONMENT_2ND_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_FOG CC_SURFACES_DEBUG_VIEW_2ND_SPECULAR_ALL + 1\n#define IS_DEBUG_VIEW_ENABLE_WITH_CAMERA (cc_surfaceTransform.y != 3.0)\nbool equalf_mode(float data1, float data2) { return abs(float(data1) - float(data2)) < 0.001; }\n#define IS_DEBUG_VIEW_LIGHTING_ENABLE_WITH_ALBEDO (UnpackBitFromFloat(cc_debug_view_mode.w, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_MISC_ENABLE_CSM_LAYER_COLORATION (UnpackBitFromFloat(cc_debug_view_mode.w, 7) && IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_DIRECT_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.y, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_DIRECT_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.y, 1) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_ENV_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.y, 2) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_ENV_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.y, 3) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_EMISSIVE (UnpackBitFromFloat(cc_debug_view_mode.y, 4) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_LIGHT_MAP (UnpackBitFromFloat(cc_debug_view_mode.y, 5) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_SHADOW (UnpackBitFromFloat(cc_debug_view_mode.y, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_AO (UnpackBitFromFloat(cc_debug_view_mode.y, 7) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_NORMAL_MAP (UnpackBitFromFloat(cc_debug_view_mode.z, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_FOG (UnpackBitFromFloat(cc_debug_view_mode.z, 1) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TONE_MAPPING (UnpackBitFromFloat(cc_debug_view_mode.z, 2) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION (UnpackBitFromFloat(cc_debug_view_mode.z, 3) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_FRESNEL (UnpackBitFromFloat(cc_debug_view_mode.z, 4) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TRANSMIT_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.z, 5) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TRANSMIT_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.z, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_2ND_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.z, 7) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TT (UnpackBitFromFloat(cc_debug_view_mode.w, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\nbool DebugViewNeedDisplayOriginalData() {\n    return\n    (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL)) && (cc_surfaceTransform.y != 3.0)) ||\n    (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_TRANSPARENCY)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_BASE_COLOR)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR)) && (cc_surfaceTransform.y != 3.0)) ||\n    (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_ROUGHNESS)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_METALLIC)) && (cc_surfaceTransform.y != 3.0)) ||\n    (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_IOR)) && (cc_surfaceTransform.y != 3.0))\n    ;\n}\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec3 HDRToLDR(vec3 color)\n{\n  #if CC_USE_HDR\n    #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n      if (IS_DEBUG_VIEW_COMPOSITE_ENABLE_TONE_MAPPING)\n    #endif\n    {\n    #if CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    }\n  #endif\n  return color;\n}\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetCameraDepthRH(float depthHS, mat4 matProj)\n{\n    return -matProj[3][2] / (depthHS + matProj[2][2]);\n}\nfloat GetCameraDepthRH(float depthHS, float matProj32, float matProj22)\n{\n    return -matProj32 / (depthHS + matProj22);\n}\nvec4 GetWorldPosFromNDCPosRH(vec3 posHS, mat4 matProj, mat4 matViewProjInv)\n{\n    float w = -GetCameraDepthRH(posHS.z, matProj);\n    return matViewProjInv * vec4(posHS * w, w);\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\nvoid CC_APPLY_FOG_BASE(inout vec4 color, float factor) {\n\tcolor = vec4(mix(cc_fogColor.rgb, color.rgb, factor), color.a);\n}\nvarying vec2 v_uv;\nuniform sampler2D u_texSampler;\nuniform sampler2D DepthTex;\nvec4 CCFragOutput_PostProcess(vec4 color) {\n  vec4 worldPos = vec4(0.0);\n  #if CC_USE_FOG != 4\n    float depth = dot(texture2D(DepthTex, v_uv), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0));\n    vec3 posHS = vec3(v_uv, depth) * 2.0 - vec3(1.0);\n    posHS.xy = cc_cameraPos.w == 0.0 ? vec2(posHS.xy.x, -posHS.xy.y) : posHS.xy;\n    worldPos = GetWorldPosFromNDCPosRH(posHS, cc_matProj, cc_matViewProjInv);\n  #endif\n  #if CC_USE_FOG != 4\n    float fogFactor = 1.0;\n    CC_TRANSFER_FOG_BASE(worldPos, fogFactor);\n    CC_APPLY_FOG_BASE(color, fogFactor);\n  #endif\n  if (!DebugViewNeedDisplayOriginalData()) {\n    #if CC_USE_FLOAT_OUTPUT\n      color.rgb = HDRToLDR(color.rgb);\n      color.rgb = LinearToSRGB(color.rgb);\n    #endif\n  }\n  return color;\n}\nvoid main () {\n  vec4 o = texture2D(u_texSampler, v_uv);\n  gl_FragColor = CCFragOutput_PostProcess(o);\n}"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 42, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 42}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean", "defines": [], "editor": {"elevated": true}}, {"name": "CC_USE_SKINNING", "type": "boolean", "defines": []}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHTMAP", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_MORPH", "type": "boolean", "defines": []}, {"name": "CC_USE_DEBUG_VIEW", "type": "number", "defines": [], "range": [0, 3]}, {"name": "CC_USE_HDR", "type": "boolean", "defines": []}, {"name": "CC_TONE_MAPPING_TYPE", "type": "number", "defines": ["CC_USE_HDR"], "range": [0, 3]}, {"name": "HDR_TONE_MAPPING_ACES", "type": "boolean", "defines": ["CC_TONE_MAPPING_TYPE", "CC_USE_HDR"]}, {"name": "CC_USE_FOG", "type": "number", "defines": [], "range": [0, 4]}, {"name": "CC_USE_FLOAT_OUTPUT", "type": "boolean", "defines": []}], "name": "pipeline/float-output-process|tonemap-vs|tonemap-fs"}], "combinations": [], "hideInEditor": false}