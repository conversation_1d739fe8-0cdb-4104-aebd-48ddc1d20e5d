[{"__type__": "cc.Prefab", "_name": "ToggleContainer", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "persistent": false}, {"__type__": "cc.Node", "_name": "ToggleContainer", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 18}, {"__id__": 32}], "_active": true, "_level": 2, "_components": [{"__id__": 14}], "_prefab": {"__id__": 46}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Toggle1", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_level": 2, "_components": [{"__id__": 9}, {"__id__": 11}, {"__id__": 13}], "_prefab": {"__id__": 17}, "_lpos": {"__type__": "cc.Vec3", "x": -62, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Checkmark", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 4}, {"__id__": 6}], "_prefab": {"__id__": 8}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "", "_priority": 0, "__prefab": {"__id__": 5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "769r8PnWVBcKxoQ/tBgxxJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_sharedMaterial": null, "_spriteFrame": {"__uuid__": "45828f25-b50d-4c52-a591-e19491a62b8c@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "", "__prefab": {"__id__": 7}}, {"__type__": "cc.CompPrefabInfo", "fileId": "feLMgPl9lI7psUYhi9QZnd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2af73429-41d1-4346-9062-7798e42945dd"}, "fileId": "1dpH2l0LVHMo74pGALaWwb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "", "_priority": 0, "__prefab": {"__id__": 10}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7eWgLqN4BJYKFxZT9fh0Dg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_sharedMaterial": null, "_spriteFrame": {"__uuid__": "f12a23c4-b924-4322-a260-3d982428f1e8@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "", "__prefab": {"__id__": 12}}, {"__type__": "cc.CompPrefabInfo", "fileId": "05YCOqjrpMLJpWYS4SFuRX"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "f12a23c4-b924-4322-a260-3d982428f1e8@f9941"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 2}, "checkEvents": [], "_isChecked": true, "_toggleGroup": {"__id__": 14}, "_checkMark": {"__id__": 6}, "_id": "", "__prefab": {"__id__": 16}}, {"__type__": "cc.ToggleContainer", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "checkEvents": [], "_allowSwitchOff": false, "_id": "", "__prefab": {"__id__": 15}}, {"__type__": "cc.CompPrefabInfo", "fileId": "352Lg0yJ1CEY+vaR56K/O8"}, {"__type__": "cc.CompPrefabInfo", "fileId": "9b8IW1c0BB3rLtFXFfDwtM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2af73429-41d1-4346-9062-7798e42945dd"}, "fileId": "21XFZBsytE2bK6WHJkITcc"}, {"__type__": "cc.Node", "_name": "Toggle2", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 19}], "_active": true, "_level": 2, "_components": [{"__id__": 25}, {"__id__": 27}, {"__id__": 29}], "_prefab": {"__id__": 31}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Checkmark", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [], "_active": false, "_level": 3, "_components": [{"__id__": 20}, {"__id__": 22}], "_prefab": {"__id__": 24}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "Checkmark<UITransformComponent>", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "", "_priority": 0, "__prefab": {"__id__": 21}}, {"__type__": "cc.CompPrefabInfo", "fileId": "689DN305dFGJTCMmQOfwAD"}, {"__type__": "cc.Sprite", "_name": "Checkmark<SpriteComponent>", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_sharedMaterial": null, "_spriteFrame": {"__uuid__": "45828f25-b50d-4c52-a591-e19491a62b8c@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "", "__prefab": {"__id__": 23}}, {"__type__": "cc.CompPrefabInfo", "fileId": "faDtNLU2xM7aWAk+Ipet86"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2af73429-41d1-4346-9062-7798e42945dd"}, "fileId": "eb4g26NFpGh4wdfLABIdYV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "", "_priority": 0, "__prefab": {"__id__": 26}}, {"__type__": "cc.CompPrefabInfo", "fileId": "2408xx9KlDjpgL7oVDU/d8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_sharedMaterial": null, "_spriteFrame": {"__uuid__": "f12a23c4-b924-4322-a260-3d982428f1e8@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "", "__prefab": {"__id__": 28}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5MaVV0MdBMIwcasBnu/Ri"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "f12a23c4-b924-4322-a260-3d982428f1e8@f9941"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 18}, "checkEvents": [], "_isChecked": false, "_toggleGroup": {"__id__": 14}, "_checkMark": {"__id__": 22}, "_id": "", "__prefab": {"__id__": 30}}, {"__type__": "cc.CompPrefabInfo", "fileId": "62dIBI9rRNWoWNm6Z4N1Xu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2af73429-41d1-4346-9062-7798e42945dd"}, "fileId": "02WTSZFlRJ24FoUt5WpMow"}, {"__type__": "cc.Node", "_name": "Toggle3", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 33}], "_active": true, "_level": 2, "_components": [{"__id__": 39}, {"__id__": 41}, {"__id__": 43}], "_prefab": {"__id__": 45}, "_lpos": {"__type__": "cc.Vec3", "x": 62, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Checkmark", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_active": false, "_level": 3, "_components": [{"__id__": 34}, {"__id__": 36}], "_prefab": {"__id__": 38}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "Checkmark<UITransformComponent>", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "", "_priority": 0, "__prefab": {"__id__": 35}}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeq2vO+dJBn5KOb1dAA5F3"}, {"__type__": "cc.Sprite", "_name": "Checkmark<SpriteComponent>", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_sharedMaterial": null, "_spriteFrame": {"__uuid__": "45828f25-b50d-4c52-a591-e19491a62b8c@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "", "__prefab": {"__id__": 37}}, {"__type__": "cc.CompPrefabInfo", "fileId": "70FX1MVxdNeLTwMFd50cKW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2af73429-41d1-4346-9062-7798e42945dd"}, "fileId": "d9N37MSTRFepYAaUzF+wlO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "", "_priority": 0, "__prefab": {"__id__": 40}}, {"__type__": "cc.CompPrefabInfo", "fileId": "32AvB2MYZMpaTyU7uqXecV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_sharedMaterial": null, "_spriteFrame": {"__uuid__": "f12a23c4-b924-4322-a260-3d982428f1e8@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "", "__prefab": {"__id__": 42}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1abJcRoRFGWLtnvEy6fR2T"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "f12a23c4-b924-4322-a260-3d982428f1e8@f9941"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 32}, "checkEvents": [], "_isChecked": false, "_toggleGroup": {"__id__": 14}, "_checkMark": {"__id__": 36}, "_id": "", "__prefab": {"__id__": 44}}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbO+2TKn5Ku4d/ME4S+Grh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2af73429-41d1-4346-9062-7798e42945dd"}, "fileId": "b9MEAVEjRMUbpyfVJTxhUS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2af73429-41d1-4346-9062-7798e42945dd"}, "fileId": "0abpGdRJZFr6tOhit0x6yB"}]