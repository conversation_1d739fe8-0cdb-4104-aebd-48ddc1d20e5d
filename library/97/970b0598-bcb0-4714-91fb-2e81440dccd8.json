{"__type__": "cc.EffectAsset", "_name": "util/splash-screen", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "techniques": [{"name": "default", "passes": [{"blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "program": "util/splash-screen|splash-screen-vs:vert|splash-screen-fs:frag", "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "resolution": {"value": [640, 960], "type": 14, "handleInfo": ["u_buffer0", 0, 14]}, "percent": {"value": [0.5], "type": 13, "handleInfo": ["u_percent", 0, 13]}, "scale": {"value": [200, 500], "type": 14, "handleInfo": ["u_buffer1", 0, 14]}, "translate": {"value": [320, 480], "type": 14, "handleInfo": ["u_buffer1", 2, 14]}, "u_buffer0": {"type": 16, "editor": {"visible": false, "deprecated": true}, "value": [640, 960, 0, 0]}, "u_percent": {"type": 13, "editor": {"visible": false, "deprecated": true}, "value": [0.5]}, "u_buffer1": {"type": 16, "editor": {"visible": false, "deprecated": true}, "value": [200, 500, 320, 480]}}}]}], "shaders": [{"blocks": [{"name": "Constant", "members": [{"name": "u_buffer0", "type": 16, "count": 1}, {"name": "u_buffer1", "type": 16, "count": 1}, {"name": "u_projection", "type": 25, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "Factor", "members": [{"name": "u_percent", "type": 13, "count": 1}], "defines": [], "stageFlags": 16, "binding": 1}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 21, "location": 0}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 1}], "varyings": [{"name": "v_uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constant", "members": [{"name": "u_buffer0", "type": 16, "count": 1}, {"name": "u_buffer1", "type": 16, "count": 1}, {"name": "u_projection", "type": 25, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "Factor", "members": [{"name": "u_percent", "type": 13, "count": 1}], "defines": [], "stageFlags": 16, "binding": 1}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 3189094080, "glsl4": {"vert": "\nprecision mediump float;\nlayout(location = 0) in vec2 a_position;\nlayout(location = 1) in vec2 a_texCoord;\nlayout(location = 0) out vec2 v_uv;\nlayout(set = 1, binding = 0) uniform Constant {\n  vec4 u_buffer0;\n  vec4 u_buffer1;\n  mat4 u_projection;\n};\nvec4 vert () {\n  vec2 worldPos = a_position * u_buffer1.xy + u_buffer1.zw;\n  vec2 clipSpace = worldPos / u_buffer0.xy * 2.0 - 1.0;\n  vec4 screenPos = u_projection * vec4(clipSpace, 0.0, 1.0);\n  v_uv = a_texCoord;\n  return screenPos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nlayout(location = 0) in vec2 v_uv;\nlayout(set = 1, binding = 1) uniform Factor {\n  float u_percent;\n};\nlayout(set = 1, binding = 2) uniform sampler2D mainTexture;\nvec4 frag () {\n  vec4 color = texture(mainTexture, v_uv);\n  float percent = clamp(u_percent, 0.0, 1.0);\n  color.xyz *= percent;\n  return color;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl3": {"vert": "\nprecision mediump float;\nin vec2 a_position;\nin vec2 a_texCoord;\nout vec2 v_uv;\nlayout(std140) uniform Constant {\n  vec4 u_buffer0;\n  vec4 u_buffer1;\n  mat4 u_projection;\n};\nvec4 vert () {\n  vec2 worldPos = a_position * u_buffer1.xy + u_buffer1.zw;\n  vec2 clipSpace = worldPos / u_buffer0.xy * 2.0 - 1.0;\n  vec4 screenPos = u_projection * vec4(clipSpace, 0.0, 1.0);\n  v_uv = a_texCoord;\n  return screenPos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nin vec2 v_uv;\nlayout(std140) uniform Factor {\n  float u_percent;\n};\nuniform sampler2D mainTexture;\nvec4 frag () {\n  vec4 color = texture(mainTexture, v_uv);\n  float percent = clamp(u_percent, 0.0, 1.0);\n  color.xyz *= percent;\n  return color;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision mediump float;\nattribute vec2 a_position;\nattribute vec2 a_texCoord;\nvarying vec2 v_uv;\n  uniform vec4 u_buffer0;\n  uniform vec4 u_buffer1;\n  uniform mat4 u_projection;\nvec4 vert () {\n  vec2 worldPos = a_position * u_buffer1.xy + u_buffer1.zw;\n  vec2 clipSpace = worldPos / u_buffer0.xy * 2.0 - 1.0;\n  vec4 screenPos = u_projection * vec4(clipSpace, 0.0, 1.0);\n  v_uv = a_texCoord;\n  return screenPos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nvarying vec2 v_uv;\n  uniform float u_percent;\nuniform sampler2D mainTexture;\nvec4 frag () {\n  vec4 color = texture2D(mainTexture, v_uv);\n  float percent = clamp(u_percent, 0.0, 1.0);\n  color.xyz *= percent;\n  return color;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 6, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 1}}, "defines": [], "name": "util/splash-screen|splash-screen-vs:vert|splash-screen-fs:frag"}], "combinations": [], "hideInEditor": false}