{"__type__": "cc.EffectAsset", "_name": "pipeline/post-process/fxaa-hq1", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "techniques": [{"name": "fxaa1", "passes": [{"pass": "cc-fxaa", "rasterizerState": {"cullMode": 0}, "program": "pipeline/post-process/fxaa-hq1|fxaa-vs|fxaa-edge-fs:frag", "depthStencilState": {"depthTest": false, "depthWrite": false}}]}], "shaders": [{"blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_normal", "defines": [], "format": 32, "location": 1}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 2}, {"name": "a_tangent", "defines": [], "format": 44, "location": 3}, {"name": "a_joints", "defines": ["CC_USE_SKINNING"], "location": 4}, {"name": "a_weights", "defines": ["CC_USE_SKINNING"], "format": 44, "location": 5}, {"name": "a_jointAnimInfo", "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"], "format": 44, "isInstanced": true, "location": 6}, {"name": "a_matWorld0", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 7}, {"name": "a_matWorld1", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 8}, {"name": "a_matWorld2", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 9}, {"name": "a_lightingMapUVParam", "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"], "format": 44, "isInstanced": true, "location": 10}, {"name": "a_localShadowBiasAndProbeId", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 11}, {"name": "a_reflectionProbeData", "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"], "format": 44, "isInstanced": true, "location": 12}, {"name": "a_sh_linear_const_r", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 13}, {"name": "a_sh_linear_const_g", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 14}, {"name": "a_sh_linear_const_b", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 15}, {"name": "a_vertexId", "defines": ["CC_USE_MORPH"], "format": 11, "location": 16}], "varyings": [{"name": "v_uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "Pipeline", "members": [{"name": "g_platform", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "fxaaUBO", "members": [{"name": "texSize", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 1}], "samplerTextures": [{"name": "sceneColorMap", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 1811285614, "glsl4": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec3 a_normal;\nlayout(location = 2) in vec2 a_texCoord;\nlayout(location = 3) in vec4 a_tangent;\n#if CC_USE_SKINNING\n    layout(location = 4) in u32vec4 a_joints;\n  layout(location = 5) in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    layout(location = 6) in highp vec4 a_jointAnimInfo;\n  #endif\n  layout(location = 7) in vec4 a_matWorld0;\n  layout(location = 8) in vec4 a_matWorld1;\n  layout(location = 9) in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    layout(location = 10) in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    layout(location = 11) in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    layout(location = 12) in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    layout(location = 13) in vec4 a_sh_linear_const_r;\n    layout(location = 14) in vec4 a_sh_linear_const_g;\n    layout(location = 15) in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n#endif\nlayout(set = 1, binding = 0) uniform Pipeline {\n    vec4 g_platform;\n};\nlayout(location = 0) out vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  (In.position).y = g_platform.w == 0.0 ? -(In.position).y : (In.position).y;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision mediump int;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nvec4 fragTextureGrad (sampler2D tex, vec2 P, vec2 dPdx, vec2 dPdy) {\n    #if defined(CC_USE_WGPU)\n      return textureLod(tex, P, 0.0);\n    #else\n      return textureGrad(tex, P, dPdx, dPdy);\n    #endif\n}\nvec4 fragTextureGrad (samplerCube tex, vec3 P, vec3 dPdx, vec3 dPdy) {\n    #if defined(CC_USE_WGPU)\n      return textureLod(tex, P, 0.0);\n    #else\n      return textureGrad(tex, P, dPdx, dPdy);\n    #endif\n}\n#define int2 vec2\n#define float2 vec2\n#define float3 vec3\n#define float4 vec4\n#define FxaaBool3 bvec3\n#define FxaaInt2 vec2\n#define FxaaFloat2 vec2\n#define FxaaFloat3 vec3\n#define FxaaFloat4 vec4\n#define FxaaBool2Float(a) mix(0.0, 1.0, (a))\n#define FxaaPow3(x, y) pow(x, y)\n#define FxaaSel3(f, t, b) mix((f), (t), (b))\n#define FxaaToFloat3(a) FxaaFloat3((a), (a), (a))\nfloat4 FxaaTexLod0(sampler2D tex, float2 pos) {\n    #if defined(CC_USE_WGPU)\n        return textureLod(tex, pos.xy, 0.0);\n    #else\n        return texture(tex, pos.xy);\n    #endif\n}\nfloat4 FxaaTexGrad(sampler2D tex, float2 pos, float2 grad) {\n    return fragTextureGrad(tex, pos.xy, grad, grad);\n}\nfloat4 FxaaTexOff(sampler2D tex, float2 pos, int2 off, float2 rcpFrame) {\n    return FxaaTexLod0(tex, pos.xy + vec2(off.x, off.y) * rcpFrame);\n}\n#define FXAA_SRGB_ROP 0\n#ifndef FXAA_PRESET\n    #define FXAA_PRESET 3\n#endif\n#if (FXAA_PRESET == 0)\n    #define FXAA_EDGE_THRESHOLD      (1.0/4.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/12.0)\n    #define FXAA_SEARCH_STEPS        2\n    #define FXAA_SEARCH_ACCELERATION 4\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       1\n    #define FXAA_SUBPIX_CAP          (2.0/3.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 1)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/16.0)\n    #define FXAA_SEARCH_STEPS        4\n    #define FXAA_SEARCH_ACCELERATION 3\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 2)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/24.0)\n    #define FXAA_SEARCH_STEPS        8\n    #define FXAA_SEARCH_ACCELERATION 2\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 3)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/24.0)\n    #define FXAA_SEARCH_STEPS        16\n    #define FXAA_SEARCH_ACCELERATION 1\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 4)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/24.0)\n    #define FXAA_SEARCH_STEPS        24\n    #define FXAA_SEARCH_ACCELERATION 1\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 5)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/24.0)\n    #define FXAA_SEARCH_STEPS        32\n    #define FXAA_SEARCH_ACCELERATION 1\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#define FXAA_SUBPIX_TRIM_SCALE (1.0/(1.0 - FXAA_SUBPIX_TRIM))\nfloat FxaaLuma(float3 rgb) {\n    return rgb.y * (0.587/0.299) + rgb.x; }\nfloat3 FxaaLerp3(float3 a, float3 b, float amountOfA) {\n    return (FxaaToFloat3(-amountOfA) * b) +\n        ((a * FxaaToFloat3(amountOfA)) + b); }\nfloat3 FxaaFilterReturn(float3 rgb) {\n    #if FXAA_SRGB_ROP\n        return FxaaSel3(\n            rgb * FxaaToFloat3(1.0/12.92),\n            FxaaPow3(\n                rgb * FxaaToFloat3(1.0/1.055) + FxaaToFloat3(0.055/1.055),\n                FxaaToFloat3(2.4)),\n            rgb > FxaaToFloat3(0.04045));\n    #else\n        return rgb;\n    #endif\n}\nfloat3 FxaaPixelShader(float2 pos, sampler2D tex, float2 rcpFrame) {\n    float3 rgbN = FxaaTexOff(tex, pos.xy, FxaaInt2( 0,-1), rcpFrame).xyz;\n    float3 rgbW = FxaaTexOff(tex, pos.xy, FxaaInt2(-1, 0), rcpFrame).xyz;\n    float3 rgbM = FxaaTexOff(tex, pos.xy, FxaaInt2( 0, 0), rcpFrame).xyz;\n    float3 rgbE = FxaaTexOff(tex, pos.xy, FxaaInt2( 1, 0), rcpFrame).xyz;\n    float3 rgbS = FxaaTexOff(tex, pos.xy, FxaaInt2( 0, 1), rcpFrame).xyz;\n    float lumaN = FxaaLuma(rgbN);\n    float lumaW = FxaaLuma(rgbW);\n    float lumaM = FxaaLuma(rgbM);\n    float lumaE = FxaaLuma(rgbE);\n    float lumaS = FxaaLuma(rgbS);\n    float rangeMin = min(lumaM, min(min(lumaN, lumaW), min(lumaS, lumaE)));\n    float rangeMax = max(lumaM, max(max(lumaN, lumaW), max(lumaS, lumaE)));\n    float range = rangeMax - rangeMin;\n    #if FXAA_DEBUG\n        float lumaO = lumaM / (1.0 + (0.587/0.299));\n    #endif\n    if(range < max(FXAA_EDGE_THRESHOLD_MIN, rangeMax * FXAA_EDGE_THRESHOLD)) {\n        #if FXAA_DEBUG\n            return FxaaFilterReturn(FxaaToFloat3(lumaO));\n        #endif\n        return FxaaFilterReturn(rgbM); }\n    #if FXAA_SUBPIX > 0\n        #if FXAA_SUBPIX_FASTER\n            float3 rgbL = (rgbN + rgbW + rgbE + rgbS + rgbM) *\n                FxaaToFloat3(1.0/5.0);\n        #else\n            float3 rgbL = rgbN + rgbW + rgbM + rgbE + rgbS;\n        #endif\n    #endif\n    #if FXAA_SUBPIX != 0\n        float lumaL = (lumaN + lumaW + lumaE + lumaS) * 0.25;\n        float rangeL = abs(lumaL - lumaM);\n    #endif\n    #if FXAA_SUBPIX == 1\n        float blendL = max(0.0,\n            (rangeL / range) - FXAA_SUBPIX_TRIM) * FXAA_SUBPIX_TRIM_SCALE;\n        blendL = min(FXAA_SUBPIX_CAP, blendL);\n    #endif\n    #if FXAA_SUBPIX == 2\n        float blendL = rangeL / range;\n    #endif\n    float3 rgbNW = FxaaTexOff(tex, pos.xy, FxaaInt2(-1,-1), rcpFrame).xyz;\n    float3 rgbNE = FxaaTexOff(tex, pos.xy, FxaaInt2( 1,-1), rcpFrame).xyz;\n    float3 rgbSW = FxaaTexOff(tex, pos.xy, FxaaInt2(-1, 1), rcpFrame).xyz;\n    float3 rgbSE = FxaaTexOff(tex, pos.xy, FxaaInt2( 1, 1), rcpFrame).xyz;\n    #if (FXAA_SUBPIX_FASTER == 0) && (FXAA_SUBPIX > 0)\n        rgbL += (rgbNW + rgbNE + rgbSW + rgbSE);\n        rgbL *= FxaaToFloat3(1.0/9.0);\n    #endif\n    float lumaNW = FxaaLuma(rgbNW);\n    float lumaNE = FxaaLuma(rgbNE);\n    float lumaSW = FxaaLuma(rgbSW);\n    float lumaSE = FxaaLuma(rgbSE);\n    float edgeVert =\n        abs((0.25 * lumaNW) + (-0.5 * lumaN) + (0.25 * lumaNE)) +\n        abs((0.50 * lumaW ) + (-1.0 * lumaM) + (0.50 * lumaE )) +\n        abs((0.25 * lumaSW) + (-0.5 * lumaS) + (0.25 * lumaSE));\n    float edgeHorz =\n        abs((0.25 * lumaNW) + (-0.5 * lumaW) + (0.25 * lumaSW)) +\n        abs((0.50 * lumaN ) + (-1.0 * lumaM) + (0.50 * lumaS )) +\n        abs((0.25 * lumaNE) + (-0.5 * lumaE) + (0.25 * lumaSE));\n    bool horzSpan = edgeHorz >= edgeVert;\n    float lengthSign = horzSpan ? -rcpFrame.y : -rcpFrame.x;\n    if(!horzSpan) lumaN = lumaW;\n    if(!horzSpan) lumaS = lumaE;\n    float gradientN = abs(lumaN - lumaM);\n    float gradientS = abs(lumaS - lumaM);\n    lumaN = (lumaN + lumaM) * 0.5;\n    lumaS = (lumaS + lumaM) * 0.5;\n    bool pairN = gradientN >= gradientS;\n    if(!pairN) lumaN = lumaS;\n    if(!pairN) gradientN = gradientS;\n    if(!pairN) lengthSign *= -1.0;\n    float2 posN;\n    posN.x = pos.x + (horzSpan ? 0.0 : lengthSign * 0.5);\n    posN.y = pos.y + (horzSpan ? lengthSign * 0.5 : 0.0);\n    gradientN *= FXAA_SEARCH_THRESHOLD;\n    float2 posP = posN;\n    float2 offNP = horzSpan ?\n        FxaaFloat2(rcpFrame.x, 0.0) :\n        FxaaFloat2(0.0, rcpFrame.y);\n    float lumaEndN = lumaN;\n    float lumaEndP = lumaN;\n    bool doneN = false;\n    bool doneP = false;\n    #if FXAA_SEARCH_ACCELERATION == 1\n        posN += offNP * FxaaFloat2(-1.0, -1.0);\n        posP += offNP * FxaaFloat2( 1.0,  1.0);\n    #endif\n    #if FXAA_SEARCH_ACCELERATION == 2\n        posN += offNP * FxaaFloat2(-1.5, -1.5);\n        posP += offNP * FxaaFloat2( 1.5,  1.5);\n        offNP *= FxaaFloat2(2.0, 2.0);\n    #endif\n    #if FXAA_SEARCH_ACCELERATION == 3\n        posN += offNP * FxaaFloat2(-2.0, -2.0);\n        posP += offNP * FxaaFloat2( 2.0,  2.0);\n        offNP *= FxaaFloat2(3.0, 3.0);\n    #endif\n    #if FXAA_SEARCH_ACCELERATION == 4\n        posN += offNP * FxaaFloat2(-2.5, -2.5);\n        posP += offNP * FxaaFloat2( 2.5,  2.5);\n        offNP *= FxaaFloat2(4.0, 4.0);\n    #endif\n    for(int i = 0; i < FXAA_SEARCH_STEPS; i++) {\n        #if FXAA_SEARCH_ACCELERATION == 1\n            if(!doneN) lumaEndN =\n                FxaaLuma(FxaaTexLod0(tex, posN.xy).xyz);\n            if(!doneP) lumaEndP =\n                FxaaLuma(FxaaTexLod0(tex, posP.xy).xyz);\n        #else\n            if(!doneN) lumaEndN =\n                FxaaLuma(FxaaTexGrad(tex, posN.xy, offNP).xyz);\n            if(!doneP) lumaEndP =\n                FxaaLuma(FxaaTexGrad(tex, posP.xy, offNP).xyz);\n        #endif\n        doneN = doneN || (abs(lumaEndN - lumaN) >= gradientN);\n        doneP = doneP || (abs(lumaEndP - lumaN) >= gradientN);\n        if(doneN && doneP) break;\n        if(!doneN) posN -= offNP;\n        if(!doneP) posP += offNP; }\n    float dstN = horzSpan ? pos.x - posN.x : pos.y - posN.y;\n    float dstP = horzSpan ? posP.x - pos.x : posP.y - pos.y;\n    bool directionN = dstN < dstP;\n    lumaEndN = directionN ? lumaEndN : lumaEndP;\n    if(((lumaM - lumaN) < 0.0) == ((lumaEndN - lumaN) < 0.0))\n        lengthSign = 0.0;\n    float spanLength = (dstP + dstN);\n    dstN = directionN ? dstN : dstP;\n    float subPixelOffset = (0.5 + (dstN * (-1.0/spanLength))) * lengthSign;\n    float3 rgbF = FxaaTexLod0(tex, FxaaFloat2(\n        pos.x + (horzSpan ? 0.0 : subPixelOffset),\n        pos.y + (horzSpan ? subPixelOffset : 0.0))).xyz;\n    #if FXAA_SUBPIX == 0\n        return FxaaFilterReturn(rgbF);\n    #else\n        return FxaaFilterReturn(FxaaLerp3(rgbL, rgbF, blendL));\n    #endif\n}\nlayout(set = 1, binding = 1) uniform fxaaUBO {\n  vec4 texSize;\n};\nlayout(set = 1, binding = 2) uniform sampler2D sceneColorMap;\nlayout(location = 0) in vec2 v_uv;\nvec4 frag () {\n  vec3 color = FxaaPixelShader(v_uv, sceneColorMap, texSize.zw);\n  float alpha = texture(sceneColorMap, v_uv).a;\n  return vec4(color, alpha);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl3": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\nin vec4 a_tangent;\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n#endif\nlayout(std140) uniform Pipeline {\n    vec4 g_platform;\n};\nout vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  (In.position).y = g_platform.w == 0.0 ? -(In.position).y : (In.position).y;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\nprecision highp float;\nprecision mediump int;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nvec4 fragTextureGrad (sampler2D tex, vec2 P, vec2 dPdx, vec2 dPdy) {\n    #if defined(CC_USE_WGPU)\n      return textureLod(tex, P, 0.0);\n    #else\n      return textureGrad(tex, P, dPdx, dPdy);\n    #endif\n}\nvec4 fragTextureGrad (samplerCube tex, vec3 P, vec3 dPdx, vec3 dPdy) {\n    #if defined(CC_USE_WGPU)\n      return textureLod(tex, P, 0.0);\n    #else\n      return textureGrad(tex, P, dPdx, dPdy);\n    #endif\n}\n#define int2 vec2\n#define float2 vec2\n#define float3 vec3\n#define float4 vec4\n#define FxaaBool3 bvec3\n#define FxaaInt2 vec2\n#define FxaaFloat2 vec2\n#define FxaaFloat3 vec3\n#define FxaaFloat4 vec4\n#define FxaaBool2Float(a) mix(0.0, 1.0, (a))\n#define FxaaPow3(x, y) pow(x, y)\n#define FxaaSel3(f, t, b) mix((f), (t), (b))\n#define FxaaToFloat3(a) FxaaFloat3((a), (a), (a))\nfloat4 FxaaTexLod0(sampler2D tex, float2 pos) {\n    #if defined(CC_USE_WGPU)\n        return textureLod(tex, pos.xy, 0.0);\n    #else\n        return texture(tex, pos.xy);\n    #endif\n}\nfloat4 FxaaTexGrad(sampler2D tex, float2 pos, float2 grad) {\n    return fragTextureGrad(tex, pos.xy, grad, grad);\n}\nfloat4 FxaaTexOff(sampler2D tex, float2 pos, int2 off, float2 rcpFrame) {\n    return FxaaTexLod0(tex, pos.xy + vec2(off.x, off.y) * rcpFrame);\n}\n#define FXAA_SRGB_ROP 0\n#ifndef FXAA_PRESET\n    #define FXAA_PRESET 3\n#endif\n#if (FXAA_PRESET == 0)\n    #define FXAA_EDGE_THRESHOLD      (1.0/4.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/12.0)\n    #define FXAA_SEARCH_STEPS        2\n    #define FXAA_SEARCH_ACCELERATION 4\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       1\n    #define FXAA_SUBPIX_CAP          (2.0/3.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 1)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/16.0)\n    #define FXAA_SEARCH_STEPS        4\n    #define FXAA_SEARCH_ACCELERATION 3\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 2)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/24.0)\n    #define FXAA_SEARCH_STEPS        8\n    #define FXAA_SEARCH_ACCELERATION 2\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 3)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/24.0)\n    #define FXAA_SEARCH_STEPS        16\n    #define FXAA_SEARCH_ACCELERATION 1\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 4)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/24.0)\n    #define FXAA_SEARCH_STEPS        24\n    #define FXAA_SEARCH_ACCELERATION 1\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 5)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/24.0)\n    #define FXAA_SEARCH_STEPS        32\n    #define FXAA_SEARCH_ACCELERATION 1\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#define FXAA_SUBPIX_TRIM_SCALE (1.0/(1.0 - FXAA_SUBPIX_TRIM))\nfloat FxaaLuma(float3 rgb) {\n    return rgb.y * (0.587/0.299) + rgb.x; }\nfloat3 FxaaLerp3(float3 a, float3 b, float amountOfA) {\n    return (FxaaToFloat3(-amountOfA) * b) +\n        ((a * FxaaToFloat3(amountOfA)) + b); }\nfloat3 FxaaFilterReturn(float3 rgb) {\n    #if FXAA_SRGB_ROP\n        return FxaaSel3(\n            rgb * FxaaToFloat3(1.0/12.92),\n            FxaaPow3(\n                rgb * FxaaToFloat3(1.0/1.055) + FxaaToFloat3(0.055/1.055),\n                FxaaToFloat3(2.4)),\n            rgb > FxaaToFloat3(0.04045));\n    #else\n        return rgb;\n    #endif\n}\nfloat3 FxaaPixelShader(float2 pos, sampler2D tex, float2 rcpFrame) {\n    float3 rgbN = FxaaTexOff(tex, pos.xy, FxaaInt2( 0,-1), rcpFrame).xyz;\n    float3 rgbW = FxaaTexOff(tex, pos.xy, FxaaInt2(-1, 0), rcpFrame).xyz;\n    float3 rgbM = FxaaTexOff(tex, pos.xy, FxaaInt2( 0, 0), rcpFrame).xyz;\n    float3 rgbE = FxaaTexOff(tex, pos.xy, FxaaInt2( 1, 0), rcpFrame).xyz;\n    float3 rgbS = FxaaTexOff(tex, pos.xy, FxaaInt2( 0, 1), rcpFrame).xyz;\n    float lumaN = FxaaLuma(rgbN);\n    float lumaW = FxaaLuma(rgbW);\n    float lumaM = FxaaLuma(rgbM);\n    float lumaE = FxaaLuma(rgbE);\n    float lumaS = FxaaLuma(rgbS);\n    float rangeMin = min(lumaM, min(min(lumaN, lumaW), min(lumaS, lumaE)));\n    float rangeMax = max(lumaM, max(max(lumaN, lumaW), max(lumaS, lumaE)));\n    float range = rangeMax - rangeMin;\n    #if FXAA_DEBUG\n        float lumaO = lumaM / (1.0 + (0.587/0.299));\n    #endif\n    if(range < max(FXAA_EDGE_THRESHOLD_MIN, rangeMax * FXAA_EDGE_THRESHOLD)) {\n        #if FXAA_DEBUG\n            return FxaaFilterReturn(FxaaToFloat3(lumaO));\n        #endif\n        return FxaaFilterReturn(rgbM); }\n    #if FXAA_SUBPIX > 0\n        #if FXAA_SUBPIX_FASTER\n            float3 rgbL = (rgbN + rgbW + rgbE + rgbS + rgbM) *\n                FxaaToFloat3(1.0/5.0);\n        #else\n            float3 rgbL = rgbN + rgbW + rgbM + rgbE + rgbS;\n        #endif\n    #endif\n    #if FXAA_SUBPIX != 0\n        float lumaL = (lumaN + lumaW + lumaE + lumaS) * 0.25;\n        float rangeL = abs(lumaL - lumaM);\n    #endif\n    #if FXAA_SUBPIX == 1\n        float blendL = max(0.0,\n            (rangeL / range) - FXAA_SUBPIX_TRIM) * FXAA_SUBPIX_TRIM_SCALE;\n        blendL = min(FXAA_SUBPIX_CAP, blendL);\n    #endif\n    #if FXAA_SUBPIX == 2\n        float blendL = rangeL / range;\n    #endif\n    float3 rgbNW = FxaaTexOff(tex, pos.xy, FxaaInt2(-1,-1), rcpFrame).xyz;\n    float3 rgbNE = FxaaTexOff(tex, pos.xy, FxaaInt2( 1,-1), rcpFrame).xyz;\n    float3 rgbSW = FxaaTexOff(tex, pos.xy, FxaaInt2(-1, 1), rcpFrame).xyz;\n    float3 rgbSE = FxaaTexOff(tex, pos.xy, FxaaInt2( 1, 1), rcpFrame).xyz;\n    #if (FXAA_SUBPIX_FASTER == 0) && (FXAA_SUBPIX > 0)\n        rgbL += (rgbNW + rgbNE + rgbSW + rgbSE);\n        rgbL *= FxaaToFloat3(1.0/9.0);\n    #endif\n    float lumaNW = FxaaLuma(rgbNW);\n    float lumaNE = FxaaLuma(rgbNE);\n    float lumaSW = FxaaLuma(rgbSW);\n    float lumaSE = FxaaLuma(rgbSE);\n    float edgeVert =\n        abs((0.25 * lumaNW) + (-0.5 * lumaN) + (0.25 * lumaNE)) +\n        abs((0.50 * lumaW ) + (-1.0 * lumaM) + (0.50 * lumaE )) +\n        abs((0.25 * lumaSW) + (-0.5 * lumaS) + (0.25 * lumaSE));\n    float edgeHorz =\n        abs((0.25 * lumaNW) + (-0.5 * lumaW) + (0.25 * lumaSW)) +\n        abs((0.50 * lumaN ) + (-1.0 * lumaM) + (0.50 * lumaS )) +\n        abs((0.25 * lumaNE) + (-0.5 * lumaE) + (0.25 * lumaSE));\n    bool horzSpan = edgeHorz >= edgeVert;\n    float lengthSign = horzSpan ? -rcpFrame.y : -rcpFrame.x;\n    if(!horzSpan) lumaN = lumaW;\n    if(!horzSpan) lumaS = lumaE;\n    float gradientN = abs(lumaN - lumaM);\n    float gradientS = abs(lumaS - lumaM);\n    lumaN = (lumaN + lumaM) * 0.5;\n    lumaS = (lumaS + lumaM) * 0.5;\n    bool pairN = gradientN >= gradientS;\n    if(!pairN) lumaN = lumaS;\n    if(!pairN) gradientN = gradientS;\n    if(!pairN) lengthSign *= -1.0;\n    float2 posN;\n    posN.x = pos.x + (horzSpan ? 0.0 : lengthSign * 0.5);\n    posN.y = pos.y + (horzSpan ? lengthSign * 0.5 : 0.0);\n    gradientN *= FXAA_SEARCH_THRESHOLD;\n    float2 posP = posN;\n    float2 offNP = horzSpan ?\n        FxaaFloat2(rcpFrame.x, 0.0) :\n        FxaaFloat2(0.0, rcpFrame.y);\n    float lumaEndN = lumaN;\n    float lumaEndP = lumaN;\n    bool doneN = false;\n    bool doneP = false;\n    #if FXAA_SEARCH_ACCELERATION == 1\n        posN += offNP * FxaaFloat2(-1.0, -1.0);\n        posP += offNP * FxaaFloat2( 1.0,  1.0);\n    #endif\n    #if FXAA_SEARCH_ACCELERATION == 2\n        posN += offNP * FxaaFloat2(-1.5, -1.5);\n        posP += offNP * FxaaFloat2( 1.5,  1.5);\n        offNP *= FxaaFloat2(2.0, 2.0);\n    #endif\n    #if FXAA_SEARCH_ACCELERATION == 3\n        posN += offNP * FxaaFloat2(-2.0, -2.0);\n        posP += offNP * FxaaFloat2( 2.0,  2.0);\n        offNP *= FxaaFloat2(3.0, 3.0);\n    #endif\n    #if FXAA_SEARCH_ACCELERATION == 4\n        posN += offNP * FxaaFloat2(-2.5, -2.5);\n        posP += offNP * FxaaFloat2( 2.5,  2.5);\n        offNP *= FxaaFloat2(4.0, 4.0);\n    #endif\n    for(int i = 0; i < FXAA_SEARCH_STEPS; i++) {\n        #if FXAA_SEARCH_ACCELERATION == 1\n            if(!doneN) lumaEndN =\n                FxaaLuma(FxaaTexLod0(tex, posN.xy).xyz);\n            if(!doneP) lumaEndP =\n                FxaaLuma(FxaaTexLod0(tex, posP.xy).xyz);\n        #else\n            if(!doneN) lumaEndN =\n                FxaaLuma(FxaaTexGrad(tex, posN.xy, offNP).xyz);\n            if(!doneP) lumaEndP =\n                FxaaLuma(FxaaTexGrad(tex, posP.xy, offNP).xyz);\n        #endif\n        doneN = doneN || (abs(lumaEndN - lumaN) >= gradientN);\n        doneP = doneP || (abs(lumaEndP - lumaN) >= gradientN);\n        if(doneN && doneP) break;\n        if(!doneN) posN -= offNP;\n        if(!doneP) posP += offNP; }\n    float dstN = horzSpan ? pos.x - posN.x : pos.y - posN.y;\n    float dstP = horzSpan ? posP.x - pos.x : posP.y - pos.y;\n    bool directionN = dstN < dstP;\n    lumaEndN = directionN ? lumaEndN : lumaEndP;\n    if(((lumaM - lumaN) < 0.0) == ((lumaEndN - lumaN) < 0.0))\n        lengthSign = 0.0;\n    float spanLength = (dstP + dstN);\n    dstN = directionN ? dstN : dstP;\n    float subPixelOffset = (0.5 + (dstN * (-1.0/spanLength))) * lengthSign;\n    float3 rgbF = FxaaTexLod0(tex, FxaaFloat2(\n        pos.x + (horzSpan ? 0.0 : subPixelOffset),\n        pos.y + (horzSpan ? subPixelOffset : 0.0))).xyz;\n    #if FXAA_SUBPIX == 0\n        return FxaaFilterReturn(rgbF);\n    #else\n        return FxaaFilterReturn(FxaaLerp3(rgbL, rgbF, blendL));\n    #endif\n}\nlayout(std140) uniform fxaaUBO {\n  vec4 texSize;\n};\nuniform sampler2D sceneColorMap;\nin vec2 v_uv;\nvec4 frag () {\n  vec3 color = FxaaPixelShader(v_uv, sceneColorMap, texSize.zw);\n  float alpha = texture(sceneColorMap, v_uv).a;\n  return vec4(color, alpha);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n#endif\n  uniform vec4 g_platform;\nvarying vec2 v_uv;\nvoid main () {\n  StandardVertInput In;\n    In.position = vec4(a_position, 1.0);\n    In.normal = a_normal;\n    In.tangent = a_tangent;\n  (In.position).y = g_platform.w == 0.0 ? -(In.position).y : (In.position).y;\n  gl_Position = In.position;\n  v_uv = a_texCoord;\n}", "frag": "\n#ifdef GL_EXT_shader_texture_lod\n#extension GL_EXT_shader_texture_lod: enable\n#endif\nprecision highp float;\nprecision mediump int;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nvec4 fragTextureGrad (sampler2D tex, vec2 P, vec2 dPdx, vec2 dPdy) {\n    #ifdef GL_EXT_shader_texture_lod\n      return texture2DGradEXT(tex, P, dPdx, dPdy);\n    #else\n      return texture2D(tex, P);\n    #endif\n}\nvec4 fragTextureGrad (samplerCube tex, vec3 P, vec3 dPdx, vec3 dPdy) {\n    #ifdef GL_EXT_shader_texture_lod\n      return textureCubeGradEXT(tex, P, dPdx, dPdy);\n    #else\n      return textureCube(tex, P);\n    #endif\n}\n#define int2 vec2\n#define float2 vec2\n#define float3 vec3\n#define float4 vec4\n#define FxaaBool3 bvec3\n#define FxaaInt2 vec2\n#define FxaaFloat2 vec2\n#define FxaaFloat3 vec3\n#define FxaaFloat4 vec4\n#define FxaaBool2Float(a) mix(0.0, 1.0, (a))\n#define FxaaPow3(x, y) pow(x, y)\n#define FxaaSel3(f, t, b) mix((f), (t), (b))\n#define FxaaToFloat3(a) FxaaFloat3((a), (a), (a))\nfloat4 FxaaTexLod0(sampler2D tex, float2 pos) {\n    #if defined(CC_USE_WGPU)\n        return texture2DLod(tex, pos.xy, 0.0);\n    #else\n        return texture2D(tex, pos.xy);\n    #endif\n}\nfloat4 FxaaTexGrad(sampler2D tex, float2 pos, float2 grad) {\n    return fragTextureGrad(tex, pos.xy, grad, grad);\n}\nfloat4 FxaaTexOff(sampler2D tex, float2 pos, int2 off, float2 rcpFrame) {\n    return FxaaTexLod0(tex, pos.xy + vec2(off.x, off.y) * rcpFrame);\n}\n#define FXAA_SRGB_ROP 0\n#ifndef FXAA_PRESET\n    #define FXAA_PRESET 3\n#endif\n#if (FXAA_PRESET == 0)\n    #define FXAA_EDGE_THRESHOLD      (1.0/4.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/12.0)\n    #define FXAA_SEARCH_STEPS        2\n    #define FXAA_SEARCH_ACCELERATION 4\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       1\n    #define FXAA_SUBPIX_CAP          (2.0/3.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 1)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/16.0)\n    #define FXAA_SEARCH_STEPS        4\n    #define FXAA_SEARCH_ACCELERATION 3\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 2)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/24.0)\n    #define FXAA_SEARCH_STEPS        8\n    #define FXAA_SEARCH_ACCELERATION 2\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 3)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/24.0)\n    #define FXAA_SEARCH_STEPS        16\n    #define FXAA_SEARCH_ACCELERATION 1\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 4)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/24.0)\n    #define FXAA_SEARCH_STEPS        24\n    #define FXAA_SEARCH_ACCELERATION 1\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#if (FXAA_PRESET == 5)\n    #define FXAA_EDGE_THRESHOLD      (1.0/8.0)\n    #define FXAA_EDGE_THRESHOLD_MIN  (1.0/24.0)\n    #define FXAA_SEARCH_STEPS        32\n    #define FXAA_SEARCH_ACCELERATION 1\n    #define FXAA_SEARCH_THRESHOLD    (1.0/4.0)\n    #define FXAA_SUBPIX              1\n    #define FXAA_SUBPIX_FASTER       0\n    #define FXAA_SUBPIX_CAP          (3.0/4.0)\n    #define FXAA_SUBPIX_TRIM         (1.0/4.0)\n#endif\n#define FXAA_SUBPIX_TRIM_SCALE (1.0/(1.0 - FXAA_SUBPIX_TRIM))\nfloat FxaaLuma(float3 rgb) {\n    return rgb.y * (0.587/0.299) + rgb.x; }\nfloat3 FxaaLerp3(float3 a, float3 b, float amountOfA) {\n    return (FxaaToFloat3(-amountOfA) * b) +\n        ((a * FxaaToFloat3(amountOfA)) + b); }\nfloat3 FxaaFilterReturn(float3 rgb) {\n    #if FXAA_SRGB_ROP\n        return FxaaSel3(\n            rgb * FxaaToFloat3(1.0/12.92),\n            FxaaPow3(\n                rgb * FxaaToFloat3(1.0/1.055) + FxaaToFloat3(0.055/1.055),\n                FxaaToFloat3(2.4)),\n            rgb > FxaaToFloat3(0.04045));\n    #else\n        return rgb;\n    #endif\n}\nfloat3 FxaaPixelShader(float2 pos, sampler2D tex, float2 rcpFrame) {\n    float3 rgbN = FxaaTexOff(tex, pos.xy, FxaaInt2( 0,-1), rcpFrame).xyz;\n    float3 rgbW = FxaaTexOff(tex, pos.xy, FxaaInt2(-1, 0), rcpFrame).xyz;\n    float3 rgbM = FxaaTexOff(tex, pos.xy, FxaaInt2( 0, 0), rcpFrame).xyz;\n    float3 rgbE = FxaaTexOff(tex, pos.xy, FxaaInt2( 1, 0), rcpFrame).xyz;\n    float3 rgbS = FxaaTexOff(tex, pos.xy, FxaaInt2( 0, 1), rcpFrame).xyz;\n    float lumaN = FxaaLuma(rgbN);\n    float lumaW = FxaaLuma(rgbW);\n    float lumaM = FxaaLuma(rgbM);\n    float lumaE = FxaaLuma(rgbE);\n    float lumaS = FxaaLuma(rgbS);\n    float rangeMin = min(lumaM, min(min(lumaN, lumaW), min(lumaS, lumaE)));\n    float rangeMax = max(lumaM, max(max(lumaN, lumaW), max(lumaS, lumaE)));\n    float range = rangeMax - rangeMin;\n    #if FXAA_DEBUG\n        float lumaO = lumaM / (1.0 + (0.587/0.299));\n    #endif\n    if(range < max(FXAA_EDGE_THRESHOLD_MIN, rangeMax * FXAA_EDGE_THRESHOLD)) {\n        #if FXAA_DEBUG\n            return FxaaFilterReturn(FxaaToFloat3(lumaO));\n        #endif\n        return FxaaFilterReturn(rgbM); }\n    #if FXAA_SUBPIX > 0\n        #if FXAA_SUBPIX_FASTER\n            float3 rgbL = (rgbN + rgbW + rgbE + rgbS + rgbM) *\n                FxaaToFloat3(1.0/5.0);\n        #else\n            float3 rgbL = rgbN + rgbW + rgbM + rgbE + rgbS;\n        #endif\n    #endif\n    #if FXAA_SUBPIX != 0\n        float lumaL = (lumaN + lumaW + lumaE + lumaS) * 0.25;\n        float rangeL = abs(lumaL - lumaM);\n    #endif\n    #if FXAA_SUBPIX == 1\n        float blendL = max(0.0,\n            (rangeL / range) - FXAA_SUBPIX_TRIM) * FXAA_SUBPIX_TRIM_SCALE;\n        blendL = min(FXAA_SUBPIX_CAP, blendL);\n    #endif\n    #if FXAA_SUBPIX == 2\n        float blendL = rangeL / range;\n    #endif\n    float3 rgbNW = FxaaTexOff(tex, pos.xy, FxaaInt2(-1,-1), rcpFrame).xyz;\n    float3 rgbNE = FxaaTexOff(tex, pos.xy, FxaaInt2( 1,-1), rcpFrame).xyz;\n    float3 rgbSW = FxaaTexOff(tex, pos.xy, FxaaInt2(-1, 1), rcpFrame).xyz;\n    float3 rgbSE = FxaaTexOff(tex, pos.xy, FxaaInt2( 1, 1), rcpFrame).xyz;\n    #if (FXAA_SUBPIX_FASTER == 0) && (FXAA_SUBPIX > 0)\n        rgbL += (rgbNW + rgbNE + rgbSW + rgbSE);\n        rgbL *= FxaaToFloat3(1.0/9.0);\n    #endif\n    float lumaNW = FxaaLuma(rgbNW);\n    float lumaNE = FxaaLuma(rgbNE);\n    float lumaSW = FxaaLuma(rgbSW);\n    float lumaSE = FxaaLuma(rgbSE);\n    float edgeVert =\n        abs((0.25 * lumaNW) + (-0.5 * lumaN) + (0.25 * lumaNE)) +\n        abs((0.50 * lumaW ) + (-1.0 * lumaM) + (0.50 * lumaE )) +\n        abs((0.25 * lumaSW) + (-0.5 * lumaS) + (0.25 * lumaSE));\n    float edgeHorz =\n        abs((0.25 * lumaNW) + (-0.5 * lumaW) + (0.25 * lumaSW)) +\n        abs((0.50 * lumaN ) + (-1.0 * lumaM) + (0.50 * lumaS )) +\n        abs((0.25 * lumaNE) + (-0.5 * lumaE) + (0.25 * lumaSE));\n    bool horzSpan = edgeHorz >= edgeVert;\n    float lengthSign = horzSpan ? -rcpFrame.y : -rcpFrame.x;\n    if(!horzSpan) lumaN = lumaW;\n    if(!horzSpan) lumaS = lumaE;\n    float gradientN = abs(lumaN - lumaM);\n    float gradientS = abs(lumaS - lumaM);\n    lumaN = (lumaN + lumaM) * 0.5;\n    lumaS = (lumaS + lumaM) * 0.5;\n    bool pairN = gradientN >= gradientS;\n    if(!pairN) lumaN = lumaS;\n    if(!pairN) gradientN = gradientS;\n    if(!pairN) lengthSign *= -1.0;\n    float2 posN;\n    posN.x = pos.x + (horzSpan ? 0.0 : lengthSign * 0.5);\n    posN.y = pos.y + (horzSpan ? lengthSign * 0.5 : 0.0);\n    gradientN *= FXAA_SEARCH_THRESHOLD;\n    float2 posP = posN;\n    float2 offNP = horzSpan ?\n        FxaaFloat2(rcpFrame.x, 0.0) :\n        FxaaFloat2(0.0, rcpFrame.y);\n    float lumaEndN = lumaN;\n    float lumaEndP = lumaN;\n    bool doneN = false;\n    bool doneP = false;\n    #if FXAA_SEARCH_ACCELERATION == 1\n        posN += offNP * FxaaFloat2(-1.0, -1.0);\n        posP += offNP * FxaaFloat2( 1.0,  1.0);\n    #endif\n    #if FXAA_SEARCH_ACCELERATION == 2\n        posN += offNP * FxaaFloat2(-1.5, -1.5);\n        posP += offNP * FxaaFloat2( 1.5,  1.5);\n        offNP *= FxaaFloat2(2.0, 2.0);\n    #endif\n    #if FXAA_SEARCH_ACCELERATION == 3\n        posN += offNP * FxaaFloat2(-2.0, -2.0);\n        posP += offNP * FxaaFloat2( 2.0,  2.0);\n        offNP *= FxaaFloat2(3.0, 3.0);\n    #endif\n    #if FXAA_SEARCH_ACCELERATION == 4\n        posN += offNP * FxaaFloat2(-2.5, -2.5);\n        posP += offNP * FxaaFloat2( 2.5,  2.5);\n        offNP *= FxaaFloat2(4.0, 4.0);\n    #endif\n    for(int i = 0; i < FXAA_SEARCH_STEPS; i++) {\n        #if FXAA_SEARCH_ACCELERATION == 1\n            if(!doneN) lumaEndN =\n                FxaaLuma(FxaaTexLod0(tex, posN.xy).xyz);\n            if(!doneP) lumaEndP =\n                FxaaLuma(FxaaTexLod0(tex, posP.xy).xyz);\n        #else\n            if(!doneN) lumaEndN =\n                FxaaLuma(FxaaTexGrad(tex, posN.xy, offNP).xyz);\n            if(!doneP) lumaEndP =\n                FxaaLuma(FxaaTexGrad(tex, posP.xy, offNP).xyz);\n        #endif\n        doneN = doneN || (abs(lumaEndN - lumaN) >= gradientN);\n        doneP = doneP || (abs(lumaEndP - lumaN) >= gradientN);\n        if(doneN && doneP) break;\n        if(!doneN) posN -= offNP;\n        if(!doneP) posP += offNP; }\n    float dstN = horzSpan ? pos.x - posN.x : pos.y - posN.y;\n    float dstP = horzSpan ? posP.x - pos.x : posP.y - pos.y;\n    bool directionN = dstN < dstP;\n    lumaEndN = directionN ? lumaEndN : lumaEndP;\n    if(((lumaM - lumaN) < 0.0) == ((lumaEndN - lumaN) < 0.0))\n        lengthSign = 0.0;\n    float spanLength = (dstP + dstN);\n    dstN = directionN ? dstN : dstP;\n    float subPixelOffset = (0.5 + (dstN * (-1.0/spanLength))) * lengthSign;\n    float3 rgbF = FxaaTexLod0(tex, FxaaFloat2(\n        pos.x + (horzSpan ? 0.0 : subPixelOffset),\n        pos.y + (horzSpan ? subPixelOffset : 0.0))).xyz;\n    #if FXAA_SUBPIX == 0\n        return FxaaFilterReturn(rgbF);\n    #else\n        return FxaaFilterReturn(FxaaLerp3(rgbL, rgbF, blendL));\n    #endif\n}\n  uniform vec4 texSize;\nuniform sampler2D sceneColorMap;\nvarying vec2 v_uv;\nvec4 frag () {\n  vec3 color = FxaaPixelShader(v_uv, sceneColorMap, texSize.zw);\n  float alpha = texture2D(sceneColorMap, v_uv).a;\n  return vec4(color, alpha);\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 1, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 1}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean", "defines": [], "editor": {"elevated": true}}, {"name": "CC_USE_SKINNING", "type": "boolean", "defines": []}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHTMAP", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_MORPH", "type": "boolean", "defines": []}, {"name": "FXAA_DEBUG", "type": "boolean", "defines": []}], "name": "pipeline/post-process/fxaa-hq1|fxaa-vs|fxaa-edge-fs:frag"}], "combinations": [], "hideInEditor": false}