[{"__type__": "cc.Prefab", "_name": "Widget", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "persistent": false}, {"__type__": "cc.Node", "_name": "Widget", "_objFlags": 0, "_parent": null, "_children": [], "_active": true, "_level": 2, "_components": [{"__id__": 2}, {"__id__": 4}], "_prefab": {"__id__": 6}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "", "__prefab": {"__id__": 3}}, {"__type__": "cc.CompPrefabInfo", "fileId": "15MbuX4VNHPLpqzglWCixn"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_alignFlags": 0, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_id": "", "__prefab": {"__id__": 5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ac45cT6BHwqitY1b17iCp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "578WJabBZMg7E4k78zGTBW"}]