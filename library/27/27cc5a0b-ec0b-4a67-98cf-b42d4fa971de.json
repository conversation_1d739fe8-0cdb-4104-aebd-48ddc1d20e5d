{"__type__": "cc.EffectAsset", "_name": "util/sequence-anim", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "techniques": [{"name": "opaque", "passes": [{"program": "util/sequence-anim|unlit-vs:vert|unlit-fs:frag", "properties": {"mainTexture": {"value": "grey", "type": 28}, "tilingOffset": {"value": [1, 1, 0, 0], "type": 16}, "mainColor": {"value": [1, 1, 1, 1], "editor": {"type": "color"}, "type": 16}, "colorScale": {"value": [1, 1, 1], "type": 15, "handleInfo": ["colorScaleAndCutoff", 0, 15]}, "alphaThreshold": {"value": [0.5], "editor": {"parent": "USE_ALPHA_TEST"}, "type": 13, "handleInfo": ["colorScaleAndCutoff", 3, 13]}, "dimensions": {"value": [2, 2], "type": 14, "handleInfo": ["seqAnimParams", 0, 14]}, "frames": {"value": [4], "type": 13, "handleInfo": ["seqAnimParams", 2, 13]}, "speedOrProgress": {"value": [10], "type": 13, "handleInfo": ["seqAnimParams", 3, 13]}, "colorScaleAndCutoff": {"type": 16, "editor": {"visible": false, "deprecated": true}, "value": [1, 1, 1, 0.5]}, "seqAnimParams": {"type": 16, "editor": {"visible": false, "deprecated": true}, "value": [2, 2, 4, 10]}}, "migrations": {"properties": {"dimensions": {"formerlySerializedAs": "constants.xy"}, "frames": {"formerlySerializedAs": "constants.z"}, "speedOrProgress": {"formerlySerializedAs": "constants.w"}, "mainTexture": {"formerlySerializedAs": "seqTexture"}, "mainColor": {"formerlySerializedAs": "weight"}}}}]}, {"name": "transparent", "passes": [{"blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "program": "util/sequence-anim|unlit-vs:vert|unlit-fs:frag", "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "tilingOffset": {"value": [1, 1, 0, 0], "type": 16}, "mainColor": {"value": [1, 1, 1, 1], "editor": {"type": "color"}, "type": 16}, "colorScale": {"value": [1, 1, 1], "type": 15, "handleInfo": ["colorScaleAndCutoff", 0, 15]}, "alphaThreshold": {"value": [0.5], "editor": {"parent": "USE_ALPHA_TEST"}, "type": 13, "handleInfo": ["colorScaleAndCutoff", 3, 13]}, "dimensions": {"value": [2, 2], "type": 14, "handleInfo": ["seqAnimParams", 0, 14]}, "frames": {"value": [4], "type": 13, "handleInfo": ["seqAnimParams", 2, 13]}, "speedOrProgress": {"value": [10], "type": 13, "handleInfo": ["seqAnimParams", 3, 13]}, "colorScaleAndCutoff": {"type": 16, "editor": {"visible": false, "deprecated": true}, "value": [1, 1, 1, 0.5]}, "seqAnimParams": {"type": 16, "editor": {"visible": false, "deprecated": true}, "value": [2, 2, 4, 10]}}, "migrations": {"properties": {"dimensions": {"formerlySerializedAs": "constants.xy"}, "frames": {"formerlySerializedAs": "constants.z"}, "speedOrProgress": {"formerlySerializedAs": "constants.w"}, "mainTexture": {"formerlySerializedAs": "seqTexture"}, "mainColor": {"formerlySerializedAs": "weight"}}}}]}, {"name": "add", "passes": [{"rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 1, "blendSrcAlpha": 2, "blendDstAlpha": 1}]}, "program": "util/sequence-anim|unlit-vs:vert|unlit-fs:frag", "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "tilingOffset": {"value": [1, 1, 0, 0], "type": 16}, "mainColor": {"value": [1, 1, 1, 1], "editor": {"type": "color"}, "type": 16}, "colorScale": {"value": [1, 1, 1], "type": 15, "handleInfo": ["colorScaleAndCutoff", 0, 15]}, "alphaThreshold": {"value": [0.5], "editor": {"parent": "USE_ALPHA_TEST"}, "type": 13, "handleInfo": ["colorScaleAndCutoff", 3, 13]}, "dimensions": {"value": [2, 2], "type": 14, "handleInfo": ["seqAnimParams", 0, 14]}, "frames": {"value": [4], "type": 13, "handleInfo": ["seqAnimParams", 2, 13]}, "speedOrProgress": {"value": [10], "type": 13, "handleInfo": ["seqAnimParams", 3, 13]}, "colorScaleAndCutoff": {"type": 16, "editor": {"visible": false, "deprecated": true}, "value": [1, 1, 1, 0.5]}, "seqAnimParams": {"type": 16, "editor": {"visible": false, "deprecated": true}, "value": [2, 2, 4, 10]}}, "migrations": {"properties": {"dimensions": {"formerlySerializedAs": "constants.xy"}, "frames": {"formerlySerializedAs": "constants.z"}, "speedOrProgress": {"formerlySerializedAs": "constants.w"}, "mainTexture": {"formerlySerializedAs": "seqTexture"}, "mainColor": {"formerlySerializedAs": "weight"}}}}]}, {"name": "alpha-blend", "passes": [{"rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendSrcAlpha": 2, "blendDstAlpha": 4}]}, "program": "util/sequence-anim|unlit-vs:vert|unlit-fs:frag", "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "tilingOffset": {"value": [1, 1, 0, 0], "type": 16}, "mainColor": {"value": [1, 1, 1, 1], "editor": {"type": "color"}, "type": 16}, "colorScale": {"value": [1, 1, 1], "type": 15, "handleInfo": ["colorScaleAndCutoff", 0, 15]}, "alphaThreshold": {"value": [0.5], "editor": {"parent": "USE_ALPHA_TEST"}, "type": 13, "handleInfo": ["colorScaleAndCutoff", 3, 13]}, "dimensions": {"value": [2, 2], "type": 14, "handleInfo": ["seqAnimParams", 0, 14]}, "frames": {"value": [4], "type": 13, "handleInfo": ["seqAnimParams", 2, 13]}, "speedOrProgress": {"value": [10], "type": 13, "handleInfo": ["seqAnimParams", 3, 13]}, "colorScaleAndCutoff": {"type": 16, "editor": {"visible": false, "deprecated": true}, "value": [1, 1, 1, 0.5]}, "seqAnimParams": {"type": 16, "editor": {"visible": false, "deprecated": true}, "value": [2, 2, 4, 10]}}, "migrations": {"properties": {"dimensions": {"formerlySerializedAs": "constants.xy"}, "frames": {"formerlySerializedAs": "constants.z"}, "speedOrProgress": {"formerlySerializedAs": "constants.w"}, "mainTexture": {"formerlySerializedAs": "seqTexture"}, "mainColor": {"formerlySerializedAs": "weight"}}}}]}], "shaders": [{"blocks": [{"name": "TexCoords", "members": [{"name": "tilingOffset", "type": 16, "count": 1}], "defines": ["USE_TEXTURE"], "stageFlags": 1, "binding": 0}, {"name": "SeqAnimConstants", "members": [{"name": "seqAnimParams", "type": 16, "count": 1}], "defines": ["USE_TEXTURE", "USE_SEQUENCE_ANIM"], "stageFlags": 1, "binding": 1}, {"name": "Constant", "members": [{"name": "mainColor", "type": 16, "count": 1}, {"name": "colorScaleAndCutoff", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 2}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "defines": ["USE_TEXTURE"], "stageFlags": 16, "sampleType": 0, "binding": 3}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_normal", "defines": [], "format": 32, "location": 1}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 2}, {"name": "a_tangent", "defines": [], "format": 44, "location": 3}, {"name": "a_joints", "defines": ["CC_USE_SKINNING"], "location": 4}, {"name": "a_weights", "defines": ["CC_USE_SKINNING"], "format": 44, "location": 5}, {"name": "a_jointAnimInfo", "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"], "format": 44, "isInstanced": true, "location": 6}, {"name": "a_matWorld0", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 7}, {"name": "a_matWorld1", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 8}, {"name": "a_matWorld2", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 9}, {"name": "a_lightingMapUVParam", "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"], "format": 44, "isInstanced": true, "location": 10}, {"name": "a_localShadowBiasAndProbeId", "defines": ["USE_INSTANCING"], "format": 44, "isInstanced": true, "location": 11}, {"name": "a_reflectionProbeData", "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"], "format": 44, "isInstanced": true, "location": 12}, {"name": "a_sh_linear_const_r", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 13}, {"name": "a_sh_linear_const_g", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 14}, {"name": "a_sh_linear_const_b", "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"], "format": 44, "isInstanced": true, "location": 15}, {"name": "a_vertexId", "defines": ["CC_USE_MORPH"], "format": 11, "location": 16}, {"name": "a_color", "defines": ["USE_VERTEX_COLOR"], "format": 44, "location": 17}], "varyings": [{"name": "v_color", "type": 16, "count": 1, "defines": ["USE_VERTEX_COLOR"], "stageFlags": 17, "location": 0}, {"name": "v_uv", "type": 14, "count": 1, "defines": ["USE_TEXTURE"], "stageFlags": 17, "location": 1}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [{"tags": {"builtin": "local"}, "name": "CCMorph", "members": [{"name": "cc_displacementWeights", "typename": "vec4", "type": 16, "count": 15, "isArray": true}, {"name": "cc_displacementTextureInfo", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_MORPH"], "stageFlags": 1}, {"tags": {"builtin": "local"}, "name": "CCSkinningTexture", "members": [{"name": "cc_jointTextureInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"], "stageFlags": 1}, {"tags": {"builtin": "local"}, "name": "CCSkinningAnimation", "members": [{"name": "cc_jointAnimInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"], "stageFlags": 1}, {"tags": {"builtin": "local"}, "name": "CCSkinning", "members": [{"name": "cc_joints", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}], "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"], "stageFlags": 1}, {"tags": {"builtin": "local"}, "name": "CCLocal", "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["!USE_INSTANCING"], "stageFlags": 1}], "samplerTextures": [{"tags": {"builtin": "local"}, "name": "cc_PositionDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"], "stageFlags": 1, "sampleType": 0}, {"tags": {"builtin": "local"}, "name": "cc_NormalDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"], "stageFlags": 1, "sampleType": 0}, {"tags": {"builtin": "local"}, "name": "cc_TangentDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"], "stageFlags": 1, "sampleType": 0}, {"tags": {"builtin": "local"}, "name": "cc_jointTexture", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"], "stageFlags": 1, "sampleType": 0}, {"tags": {"builtin": "local"}, "name": "cc_realtimeJoint", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"], "stageFlags": 1, "sampleType": 0}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "TexCoords", "members": [{"name": "tilingOffset", "type": 16, "count": 1}], "defines": ["USE_TEXTURE"], "stageFlags": 1, "binding": 0}, {"name": "SeqAnimConstants", "members": [{"name": "seqAnimParams", "type": 16, "count": 1}], "defines": ["USE_TEXTURE", "USE_SEQUENCE_ANIM"], "stageFlags": 1, "binding": 1}, {"name": "Constant", "members": [{"name": "mainColor", "type": 16, "count": 1}, {"name": "colorScaleAndCutoff", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 2}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "defines": ["USE_TEXTURE"], "stageFlags": 16, "sampleType": 0, "binding": 3}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 2222124210, "glsl4": {"vert": "#extension GL_EXT_shader_explicit_arithmetic_types_int32: require\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec3 a_normal;\nlayout(location = 2) in vec2 a_texCoord;\nlayout(location = 3) in vec4 a_tangent;\n#if CC_USE_SKINNING\n    layout(location = 4) in u32vec4 a_joints;\n  layout(location = 5) in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    layout(location = 6) in highp vec4 a_jointAnimInfo;\n  #endif\n  layout(location = 7) in vec4 a_matWorld0;\n  layout(location = 8) in vec4 a_matWorld1;\n  layout(location = 9) in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    layout(location = 10) in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    layout(location = 11) in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    layout(location = 12) in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    layout(location = 13) in vec4 a_sh_linear_const_r;\n    layout(location = 14) in vec4 a_sh_linear_const_g;\n    layout(location = 15) in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    int getVertexId() {\n      return gl_VertexIndex;\n    }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if CC_USE_MORPH\n  layout(set = 2, binding = 4) uniform CCMorph {\n    vec4 cc_displacementWeights[15];\n    vec4 cc_displacementTextureInfo;\n  };\n  #if CC_MORPH_TARGET_HAS_POSITION\n    layout(set = 2, binding = 8) uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    layout(set = 2, binding = 9) uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    layout(set = 2, binding = 10) uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int pixelIndex) {\n        ivec2 texSize = textureSize(tex, 0);\n        return texelFetch(tex, ivec2(pixelIndex % texSize.x, pixelIndex / texSize.x), 0);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture(tex, x)),\n        decode32(texture(tex, y)),\n        decode32(texture(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    layout(set = 2, binding = 3) uniform CCSkinningTexture {\n      highp vec4 cc_jointTextureInfo;\n    };\n    layout(set = 2, binding = 2) uniform CCSkinningAnimation {\n      highp vec4 cc_jointAnimInfo;\n    };\n    layout(set = 2, binding = 7) uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      layout(set = 2, binding = 7) uniform highp sampler2D cc_realtimeJoint;\n    #else\n      layout(set = 2, binding = 3) uniform CCSkinning {\n        highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n      };\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\nvoid CCVertInput(inout vec4 In)\n{\n    In = vec4(a_position, 1.0);\n  #if CC_USE_MORPH\n    applyMorph(In);\n  #endif\n  #if CC_USE_SKINNING\n    CCSkin(In);\n  #endif\n}\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#if !USE_INSTANCING\n  layout(set = 2, binding = 0) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\nvoid CCGetWorldMatrix(out mat4 matWorld)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n  #endif\n}\n#if USE_VERTEX_COLOR\n  layout(location = 17) in lowp vec4 a_color;\n  layout(location = 0) out lowp vec4 v_color;\n#endif\n#if USE_TEXTURE\n  layout(location = 1) out vec2 v_uv;\n  layout(set = 1, binding = 0) uniform TexCoords {\n    vec4 tilingOffset;\n  };\n  #if USE_SEQUENCE_ANIM\n    layout(set = 1, binding = 1) uniform SeqAnimConstants {\n      vec4 seqAnimParams;\n    };\n  #endif\n#endif\nvec4 vert () {\n  vec4 position;\n  CCVertInput(position);\n  mat4 matWorld;\n  CCGetWorldMatrix(matWorld);\n  #if USE_TEXTURE\n    v_uv = a_texCoord;\n    #if FLIP_UV\n      v_uv.y = 1.0 - v_uv.y;\n    #endif\n    v_uv = v_uv * tilingOffset.xy + tilingOffset.zw;\n    #if USE_SEQUENCE_ANIM\n      #if MANUAL_PLAYBACK\n        float seqAnimCurFrame = clamp(seqAnimParams.w, 0.0, 0.999) * seqAnimParams.z;\n      #else\n        float seqAnimCurFrame = mod(cc_time.x, seqAnimParams.z / seqAnimParams.w) * seqAnimParams.w;\n      #endif\n      vec2 seqAnimOffset = floor(vec2(mod(seqAnimCurFrame, seqAnimParams.x), seqAnimCurFrame / seqAnimParams.x));\n      v_uv = (v_uv + seqAnimOffset) / seqAnimParams.xy;\n    #endif\n  #endif\n  #if USE_VERTEX_COLOR\n    v_color = a_color;\n  #endif\n  return cc_matProj * (cc_matView * matWorld) * position;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\n#if USE_ALPHA_TEST\n#endif\n#if USE_TEXTURE\n  layout(location = 1) in vec2 v_uv;\n  layout(set = 1, binding = 3) uniform sampler2D mainTexture;\n#endif\nlayout(set = 1, binding = 2) uniform Constant {\n  vec4 mainColor;\n  vec4 colorScaleAndCutoff;\n};\n#if USE_VERTEX_COLOR\n  layout(location = 0) in lowp vec4 v_color;\n#endif\nvec4 frag () {\n  vec4 o = mainColor;\n  o.rgb *= colorScaleAndCutoff.xyz;\n  #if USE_VERTEX_COLOR\n    o *= v_color;\n  #endif\n  #if USE_TEXTURE\n    o *= texture(mainTexture, v_uv);\n  #endif\n  #if USE_ALPHA_TEST\n    if (o.ALPHA_TEST_CHANNEL < colorScaleAndCutoff.w) discard;\n  #endif\n  return CCFragOutput(o);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl3": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\nin vec4 a_tangent;\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if CC_USE_MORPH\n  layout(std140) uniform CCMorph {\n    vec4 cc_displacementWeights[15];\n    vec4 cc_displacementTextureInfo;\n  };\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int pixelIndex) {\n        ivec2 texSize = textureSize(tex, 0);\n        return texelFetch(tex, ivec2(pixelIndex % texSize.x, pixelIndex / texSize.x), 0);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture(tex, x)),\n        decode32(texture(tex, y)),\n        decode32(texture(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    layout(std140) uniform CCSkinningTexture {\n      highp vec4 cc_jointTextureInfo;\n    };\n    layout(std140) uniform CCSkinningAnimation {\n      highp vec4 cc_jointAnimInfo;\n    };\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      layout(std140) uniform CCSkinning {\n        highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n      };\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\nvoid CCVertInput(inout vec4 In)\n{\n    In = vec4(a_position, 1.0);\n  #if CC_USE_MORPH\n    applyMorph(In);\n  #endif\n  #if CC_USE_SKINNING\n    CCSkin(In);\n  #endif\n}\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#if !USE_INSTANCING\n  layout(std140) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\nvoid CCGetWorldMatrix(out mat4 matWorld)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n  #endif\n}\n#if USE_VERTEX_COLOR\n  in lowp vec4 a_color;\n  out lowp vec4 v_color;\n#endif\n#if USE_TEXTURE\n  out vec2 v_uv;\n  layout(std140) uniform TexCoords {\n    vec4 tilingOffset;\n  };\n  #if USE_SEQUENCE_ANIM\n    layout(std140) uniform SeqAnimConstants {\n      vec4 seqAnimParams;\n    };\n  #endif\n#endif\nvec4 vert () {\n  vec4 position;\n  CCVertInput(position);\n  mat4 matWorld;\n  CCGetWorldMatrix(matWorld);\n  #if USE_TEXTURE\n    v_uv = a_texCoord;\n    #if FLIP_UV\n      v_uv.y = 1.0 - v_uv.y;\n    #endif\n    v_uv = v_uv * tilingOffset.xy + tilingOffset.zw;\n    #if USE_SEQUENCE_ANIM\n      #if MANUAL_PLAYBACK\n        float seqAnimCurFrame = clamp(seqAnimParams.w, 0.0, 0.999) * seqAnimParams.z;\n      #else\n        float seqAnimCurFrame = mod(cc_time.x, seqAnimParams.z / seqAnimParams.w) * seqAnimParams.w;\n      #endif\n      vec2 seqAnimOffset = floor(vec2(mod(seqAnimCurFrame, seqAnimParams.x), seqAnimCurFrame / seqAnimParams.x));\n      v_uv = (v_uv + seqAnimOffset) / seqAnimParams.xy;\n    #endif\n  #endif\n  #if USE_VERTEX_COLOR\n    v_color = a_color;\n  #endif\n  return cc_matProj * (cc_matView * matWorld) * position;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\n#if USE_ALPHA_TEST\n#endif\n#if USE_TEXTURE\n  in vec2 v_uv;\n  uniform sampler2D mainTexture;\n#endif\nlayout(std140) uniform Constant {\n  vec4 mainColor;\n  vec4 colorScaleAndCutoff;\n};\n#if USE_VERTEX_COLOR\n  in lowp vec4 v_color;\n#endif\nvec4 frag () {\n  vec4 o = mainColor;\n  o.rgb *= colorScaleAndCutoff.xyz;\n  #if USE_VERTEX_COLOR\n    o *= v_color;\n  #endif\n  #if USE_TEXTURE\n    o *= texture(mainTexture, v_uv);\n  #endif\n  #if USE_ALPHA_TEST\n    if (o.ALPHA_TEST_CHANNEL < colorScaleAndCutoff.w) discard;\n  #endif\n  return CCFragOutput(o);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if CC_USE_MORPH\n  uniform vec4 cc_displacementWeights[15];\n  uniform vec4 cc_displacementTextureInfo;\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n        int pixelIndex = elementIndex;\n        vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n        vec2 uv = getPixelCoordFromLocation(location, cc_displacementTextureInfo.xy);\n        return texture2D(tex, uv);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture2D(tex, x)),\n        decode32(texture2D(tex, y)),\n        decode32(texture2D(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    uniform highp vec4 cc_jointTextureInfo;\n    uniform highp vec4 cc_jointAnimInfo;\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      uniform highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\nvoid CCVertInput(inout vec4 In)\n{\n    In = vec4(a_position, 1.0);\n  #if CC_USE_MORPH\n    applyMorph(In);\n  #endif\n  #if CC_USE_SKINNING\n    CCSkin(In);\n  #endif\n}\nuniform highp vec4 cc_time;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n#if !USE_INSTANCING\n  uniform highp mat4 cc_matWorld;\n#endif\nvoid CCGetWorldMatrix(out mat4 matWorld)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n  #endif\n}\n#if USE_VERTEX_COLOR\n  attribute lowp vec4 a_color;\n  varying lowp vec4 v_color;\n#endif\n#if USE_TEXTURE\n  varying vec2 v_uv;\n      uniform vec4 tilingOffset;\n  #if USE_SEQUENCE_ANIM\n          uniform vec4 seqAnimParams;\n  #endif\n#endif\nvec4 vert () {\n  vec4 position;\n  CCVertInput(position);\n  mat4 matWorld;\n  CCGetWorldMatrix(matWorld);\n  #if USE_TEXTURE\n    v_uv = a_texCoord;\n    #if FLIP_UV\n      v_uv.y = 1.0 - v_uv.y;\n    #endif\n    v_uv = v_uv * tilingOffset.xy + tilingOffset.zw;\n    #if USE_SEQUENCE_ANIM\n      #if MANUAL_PLAYBACK\n        float seqAnimCurFrame = clamp(seqAnimParams.w, 0.0, 0.999) * seqAnimParams.z;\n      #else\n        float seqAnimCurFrame = mod(cc_time.x, seqAnimParams.z / seqAnimParams.w) * seqAnimParams.w;\n      #endif\n      vec2 seqAnimOffset = floor(vec2(mod(seqAnimCurFrame, seqAnimParams.x), seqAnimCurFrame / seqAnimParams.x));\n      v_uv = (v_uv + seqAnimOffset) / seqAnimParams.xy;\n    #endif\n  #endif\n  #if USE_VERTEX_COLOR\n    v_color = a_color;\n  #endif\n  return cc_matProj * (cc_matView * matWorld) * position;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\n#if USE_ALPHA_TEST\n#endif\n#if USE_TEXTURE\n  varying vec2 v_uv;\n  uniform sampler2D mainTexture;\n#endif\n   uniform vec4 mainColor;\n   uniform vec4 colorScaleAndCutoff;\n#if USE_VERTEX_COLOR\n  varying lowp vec4 v_color;\n#endif\nvec4 frag () {\n  vec4 o = mainColor;\n  o.rgb *= colorScaleAndCutoff.xyz;\n  #if USE_VERTEX_COLOR\n    o *= v_color;\n  #endif\n  #if USE_TEXTURE\n    o *= texture2D(mainTexture, v_uv);\n  #endif\n  #if USE_ALPHA_TEST\n    if (o.ALPHA_TEST_CHANNEL < colorScaleAndCutoff.w) discard;\n  #endif\n  return CCFragOutput(o);\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCMorph", "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 76, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 44}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean", "defines": [], "editor": {"elevated": true}}, {"name": "CC_USE_SKINNING", "type": "boolean", "defines": []}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHTMAP", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "defines": ["USE_INSTANCING"]}, {"name": "CC_USE_MORPH", "type": "boolean", "defines": []}, {"name": "CC_MORPH_TARGET_COUNT", "type": "number", "defines": ["CC_USE_MORPH"], "range": [2, 8]}, {"name": "CC_MORPH_TARGET_HAS_POSITION", "type": "boolean", "defines": ["CC_USE_MORPH"]}, {"name": "CC_MORPH_TARGET_HAS_NORMAL", "type": "boolean", "defines": ["CC_USE_MORPH"]}, {"name": "CC_MORPH_TARGET_HAS_TANGENT", "type": "boolean", "defines": ["CC_USE_MORPH"]}, {"name": "CC_MORPH_PRECOMPUTED", "type": "boolean", "defines": ["CC_USE_MORPH"]}, {"name": "CC_USE_REAL_TIME_JOINT_TEXTURE", "type": "boolean", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION"]}, {"name": "USE_VERTEX_COLOR", "type": "boolean", "defines": []}, {"name": "USE_TEXTURE", "type": "boolean", "defines": []}, {"name": "USE_SEQUENCE_ANIM", "type": "boolean", "defines": ["USE_TEXTURE"]}, {"name": "FLIP_UV", "type": "boolean", "defines": ["USE_TEXTURE"]}, {"name": "MANUAL_PLAYBACK", "type": "boolean", "defines": ["USE_TEXTURE", "USE_SEQUENCE_ANIM"]}, {"name": "USE_ALPHA_TEST", "type": "boolean", "defines": []}, {"name": "ALPHA_TEST_CHANNEL", "type": "string", "defines": ["USE_ALPHA_TEST"], "options": ["a", "r", "g", "b"]}], "name": "util/sequence-anim|unlit-vs:vert|unlit-fs:frag"}], "combinations": [], "hideInEditor": false}