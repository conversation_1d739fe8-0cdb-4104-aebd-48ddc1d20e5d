{"__type__": "cc.EffectAsset", "_name": "particles/builtin-billboard", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "techniques": [{"name": "add", "passes": [{"rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 1, "blendSrcAlpha": 2, "blendDstAlpha": 1}]}, "program": "particles/builtin-billboard|vert:vs_main|tinted-fs:add", "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "mainTiling_Offset": {"value": [1, 1, 0, 0], "type": 16}, "tintColor": {"value": [0.5, 0.5, 0.5, 0.5], "editor": {"type": "color"}, "type": 16}}}, {"phase": "deferred-forward", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 1, "blendSrcAlpha": 2, "blendDstAlpha": 1}]}, "propertyIndex": 0, "program": "particles/builtin-billboard|vert:vs_main|tinted-fs:add", "depthStencilState": {"depthTest": true, "depthWrite": false}}]}, {"name": "alpha-blend", "passes": [{"rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 4, "blendSrcAlpha": 1, "blendDstAlpha": 4}]}, "program": "particles/builtin-billboard|vert:vs_main|tinted-fs:add", "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "mainTiling_Offset": {"value": [1, 1, 0, 0], "type": 16}, "tintColor": {"value": [0.5, 0.5, 0.5, 0.5], "editor": {"type": "color"}, "type": 16}}}, {"phase": "deferred-forward", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 1, "blendSrcAlpha": 2, "blendDstAlpha": 1}]}, "propertyIndex": 0, "program": "particles/builtin-billboard|vert:vs_main|tinted-fs:add", "depthStencilState": {"depthTest": true, "depthWrite": false}}]}, {"name": "add-multiply", "passes": [{"rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 4, "blendSrcAlpha": 1, "blendDstAlpha": 4}]}, "program": "particles/builtin-billboard|vert:vs_main|tinted-fs:multiply", "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "mainTiling_Offset": {"value": [1, 1, 0, 0], "type": 16}, "tintColor": {"value": [0.5, 0.5, 0.5, 0.5], "editor": {"type": "color"}, "type": 16}}}, {"phase": "deferred-forward", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 4, "blendSrcAlpha": 1, "blendDstAlpha": 4}]}, "propertyIndex": 0, "program": "particles/builtin-billboard|vert:vs_main|tinted-fs:multiply", "depthStencilState": {"depthTest": true, "depthWrite": false}}]}, {"name": "add-smooth", "passes": [{"rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 4, "blendSrcAlpha": 1, "blendDstAlpha": 4}]}, "program": "particles/builtin-billboard|vert:vs_main|no-tint-fs:addSmooth", "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "mainTiling_Offset": {"value": [1, 1, 0, 0], "type": 16}}}, {"phase": "deferred-forward", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 4, "blendSrcAlpha": 1, "blendDstAlpha": 4}]}, "propertyIndex": 0, "program": "particles/builtin-billboard|vert:vs_main|no-tint-fs:addSmooth", "depthStencilState": {"depthTest": true, "depthWrite": false}}]}, {"name": "premultiply-blend", "passes": [{"rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 4, "blendSrcAlpha": 1, "blendDstAlpha": 4}]}, "program": "particles/builtin-billboard|vert:vs_main|no-tint-fs:premultiplied", "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "mainTiling_Offset": {"value": [1, 1, 0, 0], "type": 16}}}, {"phase": "deferred-forward", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 4, "blendSrcAlpha": 1, "blendDstAlpha": 4}]}, "propertyIndex": 0, "program": "particles/builtin-billboard|vert:vs_main|no-tint-fs:premultiplied", "depthStencilState": {"depthTest": true, "depthWrite": false}}]}], "shaders": [{"blocks": [{"name": "Constants", "members": [{"name": "mainTiling_Offset", "type": 16, "count": 1}, {"name": "frameTile_velLenScale", "type": 16, "count": 1}, {"name": "scale", "type": 16, "count": 1}, {"name": "nodeRotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "builtin", "members": [{"name": "cc_size_rotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 1}, {"name": "FragConstants", "members": [{"name": "tintColor", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 2}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 3}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 1}, {"name": "a_color", "defines": [], "format": 44, "location": 2}], "varyings": [{"name": "uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}, {"name": "color", "type": 16, "count": 1, "defines": [], "stageFlags": 17, "location": 1}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [{"tags": {"builtin": "local"}, "name": "CCLocal", "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": [], "stageFlags": 1}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constants", "members": [{"name": "mainTiling_Offset", "type": 16, "count": 1}, {"name": "frameTile_velLenScale", "type": 16, "count": 1}, {"name": "scale", "type": 16, "count": 1}, {"name": "nodeRotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "builtin", "members": [{"name": "cc_size_rotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 1}, {"name": "FragConstants", "members": [{"name": "tintColor", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 2}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 3}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 2281029704, "glsl4": {"vert": "\nprecision mediump float;\nlayout(set = 1, binding = 0) uniform Constants {\n  vec4 mainTiling_Offset;\n  vec4 frameTile_velLenScale;\n  vec4 scale;\n  vec4 nodeRotation;\n};\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(set = 2, binding = 0) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nvec4 quaternionFromAxis (vec3 xAxis,vec3 yAxis,vec3 zAxis){\n  mat3 m = mat3(xAxis,yAxis,zAxis);\n  float trace = m[0][0] + m[1][1] + m[2][2];\n  vec4 quat;\n  if (trace > 0.) {\n    float s = 0.5 / sqrt(trace + 1.0);\n    quat.w = 0.25 / s;\n    quat.x = (m[2][1] - m[1][2]) * s;\n    quat.y = (m[0][2] - m[2][0]) * s;\n    quat.z = (m[1][0] - m[0][1]) * s;\n  } else if ((m[0][0] > m[1][1]) && (m[0][0] > m[2][2])) {\n    float s = 2.0 * sqrt(1.0 + m[0][0] - m[1][1] - m[2][2]);\n    quat.w = (m[2][1] - m[1][2]) / s;\n    quat.x = 0.25 * s;\n    quat.y = (m[0][1] + m[1][0]) / s;\n    quat.z = (m[0][2] + m[2][0]) / s;\n  } else if (m[1][1] > m[2][2]) {\n    float s = 2.0 * sqrt(1.0 + m[1][1] - m[0][0] - m[2][2]);\n    quat.w = (m[0][2] - m[2][0]) / s;\n    quat.x = (m[0][1] + m[1][0]) / s;\n    quat.y = 0.25 * s;\n    quat.z = (m[1][2] + m[2][1]) / s;\n  } else {\n    float s = 2.0 * sqrt(1.0 + m[2][2] - m[0][0] - m[1][1]);\n    quat.w = (m[1][0] - m[0][1]) / s;\n    quat.x = (m[0][2] + m[2][0]) / s;\n    quat.y = (m[1][2] + m[2][1]) / s;\n    quat.z = 0.25 * s;\n  }\n  float len = quat.x * quat.x + quat.y * quat.y + quat.z * quat.z + quat.w * quat.w;\n  if (len > 0.) {\n    len = 1. / sqrt(len);\n    quat.x = quat.x * len;\n    quat.y = quat.y * len;\n    quat.z = quat.z * len;\n    quat.w = quat.w * len;\n  }\n  return quat;\n}\nvec4 quaternionFromEuler (vec3 angle){\n  float x = angle.x / 2.;\n  float y = angle.y / 2.;\n  float z = angle.z / 2.;\n  float sx = sin(x);\n  float cx = cos(x);\n  float sy = sin(y);\n  float cy = cos(y);\n  float sz = sin(z);\n  float cz = cos(z);\n  vec4 quat = vec4(0);\n  quat.x = sx * cy * cz + cx * sy * sz;\n  quat.y = cx * sy * cz + sx * cy * sz;\n  quat.z = cx * cy * sz - sx * sy * cz;\n  quat.w = cx * cy * cz - sx * sy * sz;\n  return quat;\n}\nvec4 quatMultiply (vec4 a, vec4 b){\n  vec4 quat;\n  quat.x = a.x * b.w + a.w * b.x + a.y * b.z - a.z * b.y;\n  quat.y = a.y * b.w + a.w * b.y + a.z * b.x - a.x * b.z;\n  quat.z = a.z * b.w + a.w * b.z + a.x * b.y - a.y * b.x;\n  quat.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;\n  return quat;\n}\nvoid rotateVecFromQuat (inout vec3 v, vec4 q){\n  float ix = q.w * v.x + q.y * v.z - q.z * v.y;\n  float iy = q.w * v.y + q.z * v.x - q.x * v.z;\n  float iz = q.w * v.z + q.x * v.y - q.y * v.x;\n  float iw = -q.x * v.x - q.y * v.y - q.z * v.z;\n  v.x = ix * q.w + iw * -q.x + iy * -q.z - iz * -q.y;\n  v.y = iy * q.w + iw * -q.y + iz * -q.x - ix * -q.z;\n  v.z = iz * q.w + iw * -q.z + ix * -q.y - iy * -q.x;\n}\nvec3 rotateInLocalSpace (vec3 pos, vec3 xAxis, vec3 yAxis, vec3 zAxis, vec4 q){\n  vec4 viewQuat = quaternionFromAxis(xAxis, yAxis, zAxis);\n  vec4 rotQuat = quatMultiply(viewQuat, q);\n  rotateVecFromQuat(pos, rotQuat);\n  return pos;\n}\nlayout(location = 0) out mediump vec2 uv;\nlayout(location = 1) out mediump vec4 color;\nvoid computeVertPos (inout vec4 pos, vec2 vertOffset, vec4 q, vec3 s\n  , mat4 viewInv\n) {\n  vec3 viewSpaceVert = vec3(vertOffset.x * s.x, vertOffset.y * s.y, 0.);\n  vec3 camX = normalize(vec3(viewInv[0][0], viewInv[1][0], viewInv[2][0]));\n  vec3 camY = normalize(vec3(viewInv[0][1], viewInv[1][1], viewInv[2][1]));\n  vec3 camZ = normalize(vec3(viewInv[0][2], viewInv[1][2], viewInv[2][2]));\n  pos.xyz += rotateInLocalSpace(viewSpaceVert, camX, camY, camZ, q);\n}\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec2 a_texCoord;\nlayout(location = 2) in vec4 a_color;\nlayout(set = 1, binding = 1) uniform builtin {\n  vec4 cc_size_rotation;\n};\nvec4 vs_main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matWorld * pos;\n  vec2 vertOffset = a_texCoord.xy - 0.5;\n  computeVertPos(pos, vertOffset, quaternionFromEuler(vec3(0., 0., cc_size_rotation.z)), vec3(cc_size_rotation.xy, 0.), cc_matViewInv);\n  pos = cc_matViewProj * pos;\n  uv = a_texCoord.xy;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vs_main(); }", "frag": "\nprecision mediump float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nlayout(location = 0) in vec2 uv;\nlayout(location = 1) in vec4 color;\nlayout(set = 1, binding = 3) uniform sampler2D mainTexture;\nlayout(set = 1, binding = 2) uniform FragConstants {\n  vec4 tintColor;\n};\nvec4 add () {\n  vec4 col = 2.0 * color * tintColor * texture(mainTexture, uv);\n  return CCFragOutput(col);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = add(); }"}, "glsl3": {"vert": "\nprecision mediump float;\nlayout(std140) uniform Constants {\n  vec4 mainTiling_Offset;\n  vec4 frameTile_velLenScale;\n  vec4 scale;\n  vec4 nodeRotation;\n};\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(std140) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nvec4 quaternionFromAxis (vec3 xAxis,vec3 yAxis,vec3 zAxis){\n  mat3 m = mat3(xAxis,yAxis,zAxis);\n  float trace = m[0][0] + m[1][1] + m[2][2];\n  vec4 quat;\n  if (trace > 0.) {\n    float s = 0.5 / sqrt(trace + 1.0);\n    quat.w = 0.25 / s;\n    quat.x = (m[2][1] - m[1][2]) * s;\n    quat.y = (m[0][2] - m[2][0]) * s;\n    quat.z = (m[1][0] - m[0][1]) * s;\n  } else if ((m[0][0] > m[1][1]) && (m[0][0] > m[2][2])) {\n    float s = 2.0 * sqrt(1.0 + m[0][0] - m[1][1] - m[2][2]);\n    quat.w = (m[2][1] - m[1][2]) / s;\n    quat.x = 0.25 * s;\n    quat.y = (m[0][1] + m[1][0]) / s;\n    quat.z = (m[0][2] + m[2][0]) / s;\n  } else if (m[1][1] > m[2][2]) {\n    float s = 2.0 * sqrt(1.0 + m[1][1] - m[0][0] - m[2][2]);\n    quat.w = (m[0][2] - m[2][0]) / s;\n    quat.x = (m[0][1] + m[1][0]) / s;\n    quat.y = 0.25 * s;\n    quat.z = (m[1][2] + m[2][1]) / s;\n  } else {\n    float s = 2.0 * sqrt(1.0 + m[2][2] - m[0][0] - m[1][1]);\n    quat.w = (m[1][0] - m[0][1]) / s;\n    quat.x = (m[0][2] + m[2][0]) / s;\n    quat.y = (m[1][2] + m[2][1]) / s;\n    quat.z = 0.25 * s;\n  }\n  float len = quat.x * quat.x + quat.y * quat.y + quat.z * quat.z + quat.w * quat.w;\n  if (len > 0.) {\n    len = 1. / sqrt(len);\n    quat.x = quat.x * len;\n    quat.y = quat.y * len;\n    quat.z = quat.z * len;\n    quat.w = quat.w * len;\n  }\n  return quat;\n}\nvec4 quaternionFromEuler (vec3 angle){\n  float x = angle.x / 2.;\n  float y = angle.y / 2.;\n  float z = angle.z / 2.;\n  float sx = sin(x);\n  float cx = cos(x);\n  float sy = sin(y);\n  float cy = cos(y);\n  float sz = sin(z);\n  float cz = cos(z);\n  vec4 quat = vec4(0);\n  quat.x = sx * cy * cz + cx * sy * sz;\n  quat.y = cx * sy * cz + sx * cy * sz;\n  quat.z = cx * cy * sz - sx * sy * cz;\n  quat.w = cx * cy * cz - sx * sy * sz;\n  return quat;\n}\nvec4 quatMultiply (vec4 a, vec4 b){\n  vec4 quat;\n  quat.x = a.x * b.w + a.w * b.x + a.y * b.z - a.z * b.y;\n  quat.y = a.y * b.w + a.w * b.y + a.z * b.x - a.x * b.z;\n  quat.z = a.z * b.w + a.w * b.z + a.x * b.y - a.y * b.x;\n  quat.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;\n  return quat;\n}\nvoid rotateVecFromQuat (inout vec3 v, vec4 q){\n  float ix = q.w * v.x + q.y * v.z - q.z * v.y;\n  float iy = q.w * v.y + q.z * v.x - q.x * v.z;\n  float iz = q.w * v.z + q.x * v.y - q.y * v.x;\n  float iw = -q.x * v.x - q.y * v.y - q.z * v.z;\n  v.x = ix * q.w + iw * -q.x + iy * -q.z - iz * -q.y;\n  v.y = iy * q.w + iw * -q.y + iz * -q.x - ix * -q.z;\n  v.z = iz * q.w + iw * -q.z + ix * -q.y - iy * -q.x;\n}\nvec3 rotateInLocalSpace (vec3 pos, vec3 xAxis, vec3 yAxis, vec3 zAxis, vec4 q){\n  vec4 viewQuat = quaternionFromAxis(xAxis, yAxis, zAxis);\n  vec4 rotQuat = quatMultiply(viewQuat, q);\n  rotateVecFromQuat(pos, rotQuat);\n  return pos;\n}\nout mediump vec2 uv;\nout mediump vec4 color;\nvoid computeVertPos (inout vec4 pos, vec2 vertOffset, vec4 q, vec3 s\n  , mat4 viewInv\n) {\n  vec3 viewSpaceVert = vec3(vertOffset.x * s.x, vertOffset.y * s.y, 0.);\n  vec3 camX = normalize(vec3(viewInv[0][0], viewInv[1][0], viewInv[2][0]));\n  vec3 camY = normalize(vec3(viewInv[0][1], viewInv[1][1], viewInv[2][1]));\n  vec3 camZ = normalize(vec3(viewInv[0][2], viewInv[1][2], viewInv[2][2]));\n  pos.xyz += rotateInLocalSpace(viewSpaceVert, camX, camY, camZ, q);\n}\nin vec3 a_position;\nin vec2 a_texCoord;\nin vec4 a_color;\nlayout(std140) uniform builtin {\n  vec4 cc_size_rotation;\n};\nvec4 vs_main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matWorld * pos;\n  vec2 vertOffset = a_texCoord.xy - 0.5;\n  computeVertPos(pos, vertOffset, quaternionFromEuler(vec3(0., 0., cc_size_rotation.z)), vec3(cc_size_rotation.xy, 0.), cc_matViewInv);\n  pos = cc_matViewProj * pos;\n  uv = a_texCoord.xy;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vs_main(); }", "frag": "\nprecision mediump float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nin vec2 uv;\nin vec4 color;\nuniform sampler2D mainTexture;\nlayout(std140) uniform FragConstants {\n  vec4 tintColor;\n};\nvec4 add () {\n  vec4 col = 2.0 * color * tintColor * texture(mainTexture, uv);\n  return CCFragOutput(col);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = add(); }"}, "glsl1": {"vert": "\nprecision mediump float;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matViewInv;\n  uniform highp mat4 cc_matViewProj;\nuniform highp mat4 cc_matWorld;\nvec4 quaternionFromAxis (vec3 xAxis,vec3 yAxis,vec3 zAxis){\n  mat3 m = mat3(xAxis,yAxis,zAxis);\n  float trace = m[0][0] + m[1][1] + m[2][2];\n  vec4 quat;\n  if (trace > 0.) {\n    float s = 0.5 / sqrt(trace + 1.0);\n    quat.w = 0.25 / s;\n    quat.x = (m[2][1] - m[1][2]) * s;\n    quat.y = (m[0][2] - m[2][0]) * s;\n    quat.z = (m[1][0] - m[0][1]) * s;\n  } else if ((m[0][0] > m[1][1]) && (m[0][0] > m[2][2])) {\n    float s = 2.0 * sqrt(1.0 + m[0][0] - m[1][1] - m[2][2]);\n    quat.w = (m[2][1] - m[1][2]) / s;\n    quat.x = 0.25 * s;\n    quat.y = (m[0][1] + m[1][0]) / s;\n    quat.z = (m[0][2] + m[2][0]) / s;\n  } else if (m[1][1] > m[2][2]) {\n    float s = 2.0 * sqrt(1.0 + m[1][1] - m[0][0] - m[2][2]);\n    quat.w = (m[0][2] - m[2][0]) / s;\n    quat.x = (m[0][1] + m[1][0]) / s;\n    quat.y = 0.25 * s;\n    quat.z = (m[1][2] + m[2][1]) / s;\n  } else {\n    float s = 2.0 * sqrt(1.0 + m[2][2] - m[0][0] - m[1][1]);\n    quat.w = (m[1][0] - m[0][1]) / s;\n    quat.x = (m[0][2] + m[2][0]) / s;\n    quat.y = (m[1][2] + m[2][1]) / s;\n    quat.z = 0.25 * s;\n  }\n  float len = quat.x * quat.x + quat.y * quat.y + quat.z * quat.z + quat.w * quat.w;\n  if (len > 0.) {\n    len = 1. / sqrt(len);\n    quat.x = quat.x * len;\n    quat.y = quat.y * len;\n    quat.z = quat.z * len;\n    quat.w = quat.w * len;\n  }\n  return quat;\n}\nvec4 quaternionFromEuler (vec3 angle){\n  float x = angle.x / 2.;\n  float y = angle.y / 2.;\n  float z = angle.z / 2.;\n  float sx = sin(x);\n  float cx = cos(x);\n  float sy = sin(y);\n  float cy = cos(y);\n  float sz = sin(z);\n  float cz = cos(z);\n  vec4 quat = vec4(0);\n  quat.x = sx * cy * cz + cx * sy * sz;\n  quat.y = cx * sy * cz + sx * cy * sz;\n  quat.z = cx * cy * sz - sx * sy * cz;\n  quat.w = cx * cy * cz - sx * sy * sz;\n  return quat;\n}\nvec4 quatMultiply (vec4 a, vec4 b){\n  vec4 quat;\n  quat.x = a.x * b.w + a.w * b.x + a.y * b.z - a.z * b.y;\n  quat.y = a.y * b.w + a.w * b.y + a.z * b.x - a.x * b.z;\n  quat.z = a.z * b.w + a.w * b.z + a.x * b.y - a.y * b.x;\n  quat.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;\n  return quat;\n}\nvoid rotateVecFromQuat (inout vec3 v, vec4 q){\n  float ix = q.w * v.x + q.y * v.z - q.z * v.y;\n  float iy = q.w * v.y + q.z * v.x - q.x * v.z;\n  float iz = q.w * v.z + q.x * v.y - q.y * v.x;\n  float iw = -q.x * v.x - q.y * v.y - q.z * v.z;\n  v.x = ix * q.w + iw * -q.x + iy * -q.z - iz * -q.y;\n  v.y = iy * q.w + iw * -q.y + iz * -q.x - ix * -q.z;\n  v.z = iz * q.w + iw * -q.z + ix * -q.y - iy * -q.x;\n}\nvec3 rotateInLocalSpace (vec3 pos, vec3 xAxis, vec3 yAxis, vec3 zAxis, vec4 q){\n  vec4 viewQuat = quaternionFromAxis(xAxis, yAxis, zAxis);\n  vec4 rotQuat = quatMultiply(viewQuat, q);\n  rotateVecFromQuat(pos, rotQuat);\n  return pos;\n}\nvarying mediump vec2 uv;\nvarying mediump vec4 color;\nvoid computeVertPos (inout vec4 pos, vec2 vertOffset, vec4 q, vec3 s\n  , mat4 viewInv\n) {\n  vec3 viewSpaceVert = vec3(vertOffset.x * s.x, vertOffset.y * s.y, 0.);\n  vec3 camX = normalize(vec3(viewInv[0][0], viewInv[1][0], viewInv[2][0]));\n  vec3 camY = normalize(vec3(viewInv[0][1], viewInv[1][1], viewInv[2][1]));\n  vec3 camZ = normalize(vec3(viewInv[0][2], viewInv[1][2], viewInv[2][2]));\n  pos.xyz += rotateInLocalSpace(viewSpaceVert, camX, camY, camZ, q);\n}\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\n   uniform vec4 cc_size_rotation;\nvec4 vs_main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matWorld * pos;\n  vec2 vertOffset = a_texCoord.xy - 0.5;\n  computeVertPos(pos, vertOffset, quaternionFromEuler(vec3(0., 0., cc_size_rotation.z)), vec3(cc_size_rotation.xy, 0.), cc_matViewInv);\n  pos = cc_matViewProj * pos;\n  uv = a_texCoord.xy;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vs_main(); }", "frag": "\nprecision mediump float;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nvarying vec2 uv;\nvarying vec4 color;\nuniform sampler2D mainTexture;\n  uniform vec4 tintColor;\nvec4 add () {\n  vec4 col = 2.0 * color * tintColor * texture2D(mainTexture, uv);\n  return CCFragOutput(col);\n}\nvoid main() { gl_FragColor = add(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 61, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 43}}, "defines": [], "name": "particles/builtin-billboard|vert:vs_main|tinted-fs:add"}, {"blocks": [{"name": "Constants", "members": [{"name": "mainTiling_Offset", "type": 16, "count": 1}, {"name": "frameTile_velLenScale", "type": 16, "count": 1}, {"name": "scale", "type": 16, "count": 1}, {"name": "nodeRotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "builtin", "members": [{"name": "cc_size_rotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 1}, {"name": "FragConstants", "members": [{"name": "tintColor", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 2}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 3}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 1}, {"name": "a_color", "defines": [], "format": 44, "location": 2}], "varyings": [{"name": "uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}, {"name": "color", "type": 16, "count": 1, "defines": [], "stageFlags": 17, "location": 1}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [{"tags": {"builtin": "local"}, "name": "CCLocal", "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": [], "stageFlags": 1}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constants", "members": [{"name": "mainTiling_Offset", "type": 16, "count": 1}, {"name": "frameTile_velLenScale", "type": 16, "count": 1}, {"name": "scale", "type": 16, "count": 1}, {"name": "nodeRotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "builtin", "members": [{"name": "cc_size_rotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 1}, {"name": "FragConstants", "members": [{"name": "tintColor", "type": 16, "count": 1}], "defines": [], "stageFlags": 16, "binding": 2}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 3}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 2715866145, "glsl4": {"vert": "\nprecision mediump float;\nlayout(set = 1, binding = 0) uniform Constants {\n  vec4 mainTiling_Offset;\n  vec4 frameTile_velLenScale;\n  vec4 scale;\n  vec4 nodeRotation;\n};\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(set = 2, binding = 0) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nvec4 quaternionFromAxis (vec3 xAxis,vec3 yAxis,vec3 zAxis){\n  mat3 m = mat3(xAxis,yAxis,zAxis);\n  float trace = m[0][0] + m[1][1] + m[2][2];\n  vec4 quat;\n  if (trace > 0.) {\n    float s = 0.5 / sqrt(trace + 1.0);\n    quat.w = 0.25 / s;\n    quat.x = (m[2][1] - m[1][2]) * s;\n    quat.y = (m[0][2] - m[2][0]) * s;\n    quat.z = (m[1][0] - m[0][1]) * s;\n  } else if ((m[0][0] > m[1][1]) && (m[0][0] > m[2][2])) {\n    float s = 2.0 * sqrt(1.0 + m[0][0] - m[1][1] - m[2][2]);\n    quat.w = (m[2][1] - m[1][2]) / s;\n    quat.x = 0.25 * s;\n    quat.y = (m[0][1] + m[1][0]) / s;\n    quat.z = (m[0][2] + m[2][0]) / s;\n  } else if (m[1][1] > m[2][2]) {\n    float s = 2.0 * sqrt(1.0 + m[1][1] - m[0][0] - m[2][2]);\n    quat.w = (m[0][2] - m[2][0]) / s;\n    quat.x = (m[0][1] + m[1][0]) / s;\n    quat.y = 0.25 * s;\n    quat.z = (m[1][2] + m[2][1]) / s;\n  } else {\n    float s = 2.0 * sqrt(1.0 + m[2][2] - m[0][0] - m[1][1]);\n    quat.w = (m[1][0] - m[0][1]) / s;\n    quat.x = (m[0][2] + m[2][0]) / s;\n    quat.y = (m[1][2] + m[2][1]) / s;\n    quat.z = 0.25 * s;\n  }\n  float len = quat.x * quat.x + quat.y * quat.y + quat.z * quat.z + quat.w * quat.w;\n  if (len > 0.) {\n    len = 1. / sqrt(len);\n    quat.x = quat.x * len;\n    quat.y = quat.y * len;\n    quat.z = quat.z * len;\n    quat.w = quat.w * len;\n  }\n  return quat;\n}\nvec4 quaternionFromEuler (vec3 angle){\n  float x = angle.x / 2.;\n  float y = angle.y / 2.;\n  float z = angle.z / 2.;\n  float sx = sin(x);\n  float cx = cos(x);\n  float sy = sin(y);\n  float cy = cos(y);\n  float sz = sin(z);\n  float cz = cos(z);\n  vec4 quat = vec4(0);\n  quat.x = sx * cy * cz + cx * sy * sz;\n  quat.y = cx * sy * cz + sx * cy * sz;\n  quat.z = cx * cy * sz - sx * sy * cz;\n  quat.w = cx * cy * cz - sx * sy * sz;\n  return quat;\n}\nvec4 quatMultiply (vec4 a, vec4 b){\n  vec4 quat;\n  quat.x = a.x * b.w + a.w * b.x + a.y * b.z - a.z * b.y;\n  quat.y = a.y * b.w + a.w * b.y + a.z * b.x - a.x * b.z;\n  quat.z = a.z * b.w + a.w * b.z + a.x * b.y - a.y * b.x;\n  quat.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;\n  return quat;\n}\nvoid rotateVecFromQuat (inout vec3 v, vec4 q){\n  float ix = q.w * v.x + q.y * v.z - q.z * v.y;\n  float iy = q.w * v.y + q.z * v.x - q.x * v.z;\n  float iz = q.w * v.z + q.x * v.y - q.y * v.x;\n  float iw = -q.x * v.x - q.y * v.y - q.z * v.z;\n  v.x = ix * q.w + iw * -q.x + iy * -q.z - iz * -q.y;\n  v.y = iy * q.w + iw * -q.y + iz * -q.x - ix * -q.z;\n  v.z = iz * q.w + iw * -q.z + ix * -q.y - iy * -q.x;\n}\nvec3 rotateInLocalSpace (vec3 pos, vec3 xAxis, vec3 yAxis, vec3 zAxis, vec4 q){\n  vec4 viewQuat = quaternionFromAxis(xAxis, yAxis, zAxis);\n  vec4 rotQuat = quatMultiply(viewQuat, q);\n  rotateVecFromQuat(pos, rotQuat);\n  return pos;\n}\nlayout(location = 0) out mediump vec2 uv;\nlayout(location = 1) out mediump vec4 color;\nvoid computeVertPos (inout vec4 pos, vec2 vertOffset, vec4 q, vec3 s\n  , mat4 viewInv\n) {\n  vec3 viewSpaceVert = vec3(vertOffset.x * s.x, vertOffset.y * s.y, 0.);\n  vec3 camX = normalize(vec3(viewInv[0][0], viewInv[1][0], viewInv[2][0]));\n  vec3 camY = normalize(vec3(viewInv[0][1], viewInv[1][1], viewInv[2][1]));\n  vec3 camZ = normalize(vec3(viewInv[0][2], viewInv[1][2], viewInv[2][2]));\n  pos.xyz += rotateInLocalSpace(viewSpaceVert, camX, camY, camZ, q);\n}\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec2 a_texCoord;\nlayout(location = 2) in vec4 a_color;\nlayout(set = 1, binding = 1) uniform builtin {\n  vec4 cc_size_rotation;\n};\nvec4 vs_main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matWorld * pos;\n  vec2 vertOffset = a_texCoord.xy - 0.5;\n  computeVertPos(pos, vertOffset, quaternionFromEuler(vec3(0., 0., cc_size_rotation.z)), vec3(cc_size_rotation.xy, 0.), cc_matViewInv);\n  pos = cc_matViewProj * pos;\n  uv = a_texCoord.xy;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vs_main(); }", "frag": "\nprecision mediump float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nlayout(location = 0) in vec2 uv;\nlayout(location = 1) in vec4 color;\nlayout(set = 1, binding = 3) uniform sampler2D mainTexture;\nlayout(set = 1, binding = 2) uniform FragConstants {\n  vec4 tintColor;\n};\nvec4 multiply () {\n  vec4 col;\n  vec4 texColor = texture(mainTexture, uv);\n  col.rgb = tintColor.rgb * texColor.rgb * color.rgb * vec3(2.0);\n  col.a = (1.0 - texColor.a) * (tintColor.a * color.a * 2.0);\n  return CCFragOutput(col);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = multiply(); }"}, "glsl3": {"vert": "\nprecision mediump float;\nlayout(std140) uniform Constants {\n  vec4 mainTiling_Offset;\n  vec4 frameTile_velLenScale;\n  vec4 scale;\n  vec4 nodeRotation;\n};\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(std140) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nvec4 quaternionFromAxis (vec3 xAxis,vec3 yAxis,vec3 zAxis){\n  mat3 m = mat3(xAxis,yAxis,zAxis);\n  float trace = m[0][0] + m[1][1] + m[2][2];\n  vec4 quat;\n  if (trace > 0.) {\n    float s = 0.5 / sqrt(trace + 1.0);\n    quat.w = 0.25 / s;\n    quat.x = (m[2][1] - m[1][2]) * s;\n    quat.y = (m[0][2] - m[2][0]) * s;\n    quat.z = (m[1][0] - m[0][1]) * s;\n  } else if ((m[0][0] > m[1][1]) && (m[0][0] > m[2][2])) {\n    float s = 2.0 * sqrt(1.0 + m[0][0] - m[1][1] - m[2][2]);\n    quat.w = (m[2][1] - m[1][2]) / s;\n    quat.x = 0.25 * s;\n    quat.y = (m[0][1] + m[1][0]) / s;\n    quat.z = (m[0][2] + m[2][0]) / s;\n  } else if (m[1][1] > m[2][2]) {\n    float s = 2.0 * sqrt(1.0 + m[1][1] - m[0][0] - m[2][2]);\n    quat.w = (m[0][2] - m[2][0]) / s;\n    quat.x = (m[0][1] + m[1][0]) / s;\n    quat.y = 0.25 * s;\n    quat.z = (m[1][2] + m[2][1]) / s;\n  } else {\n    float s = 2.0 * sqrt(1.0 + m[2][2] - m[0][0] - m[1][1]);\n    quat.w = (m[1][0] - m[0][1]) / s;\n    quat.x = (m[0][2] + m[2][0]) / s;\n    quat.y = (m[1][2] + m[2][1]) / s;\n    quat.z = 0.25 * s;\n  }\n  float len = quat.x * quat.x + quat.y * quat.y + quat.z * quat.z + quat.w * quat.w;\n  if (len > 0.) {\n    len = 1. / sqrt(len);\n    quat.x = quat.x * len;\n    quat.y = quat.y * len;\n    quat.z = quat.z * len;\n    quat.w = quat.w * len;\n  }\n  return quat;\n}\nvec4 quaternionFromEuler (vec3 angle){\n  float x = angle.x / 2.;\n  float y = angle.y / 2.;\n  float z = angle.z / 2.;\n  float sx = sin(x);\n  float cx = cos(x);\n  float sy = sin(y);\n  float cy = cos(y);\n  float sz = sin(z);\n  float cz = cos(z);\n  vec4 quat = vec4(0);\n  quat.x = sx * cy * cz + cx * sy * sz;\n  quat.y = cx * sy * cz + sx * cy * sz;\n  quat.z = cx * cy * sz - sx * sy * cz;\n  quat.w = cx * cy * cz - sx * sy * sz;\n  return quat;\n}\nvec4 quatMultiply (vec4 a, vec4 b){\n  vec4 quat;\n  quat.x = a.x * b.w + a.w * b.x + a.y * b.z - a.z * b.y;\n  quat.y = a.y * b.w + a.w * b.y + a.z * b.x - a.x * b.z;\n  quat.z = a.z * b.w + a.w * b.z + a.x * b.y - a.y * b.x;\n  quat.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;\n  return quat;\n}\nvoid rotateVecFromQuat (inout vec3 v, vec4 q){\n  float ix = q.w * v.x + q.y * v.z - q.z * v.y;\n  float iy = q.w * v.y + q.z * v.x - q.x * v.z;\n  float iz = q.w * v.z + q.x * v.y - q.y * v.x;\n  float iw = -q.x * v.x - q.y * v.y - q.z * v.z;\n  v.x = ix * q.w + iw * -q.x + iy * -q.z - iz * -q.y;\n  v.y = iy * q.w + iw * -q.y + iz * -q.x - ix * -q.z;\n  v.z = iz * q.w + iw * -q.z + ix * -q.y - iy * -q.x;\n}\nvec3 rotateInLocalSpace (vec3 pos, vec3 xAxis, vec3 yAxis, vec3 zAxis, vec4 q){\n  vec4 viewQuat = quaternionFromAxis(xAxis, yAxis, zAxis);\n  vec4 rotQuat = quatMultiply(viewQuat, q);\n  rotateVecFromQuat(pos, rotQuat);\n  return pos;\n}\nout mediump vec2 uv;\nout mediump vec4 color;\nvoid computeVertPos (inout vec4 pos, vec2 vertOffset, vec4 q, vec3 s\n  , mat4 viewInv\n) {\n  vec3 viewSpaceVert = vec3(vertOffset.x * s.x, vertOffset.y * s.y, 0.);\n  vec3 camX = normalize(vec3(viewInv[0][0], viewInv[1][0], viewInv[2][0]));\n  vec3 camY = normalize(vec3(viewInv[0][1], viewInv[1][1], viewInv[2][1]));\n  vec3 camZ = normalize(vec3(viewInv[0][2], viewInv[1][2], viewInv[2][2]));\n  pos.xyz += rotateInLocalSpace(viewSpaceVert, camX, camY, camZ, q);\n}\nin vec3 a_position;\nin vec2 a_texCoord;\nin vec4 a_color;\nlayout(std140) uniform builtin {\n  vec4 cc_size_rotation;\n};\nvec4 vs_main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matWorld * pos;\n  vec2 vertOffset = a_texCoord.xy - 0.5;\n  computeVertPos(pos, vertOffset, quaternionFromEuler(vec3(0., 0., cc_size_rotation.z)), vec3(cc_size_rotation.xy, 0.), cc_matViewInv);\n  pos = cc_matViewProj * pos;\n  uv = a_texCoord.xy;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vs_main(); }", "frag": "\nprecision mediump float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nin vec2 uv;\nin vec4 color;\nuniform sampler2D mainTexture;\nlayout(std140) uniform FragConstants {\n  vec4 tintColor;\n};\nvec4 multiply () {\n  vec4 col;\n  vec4 texColor = texture(mainTexture, uv);\n  col.rgb = tintColor.rgb * texColor.rgb * color.rgb * vec3(2.0);\n  col.a = (1.0 - texColor.a) * (tintColor.a * color.a * 2.0);\n  return CCFragOutput(col);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = multiply(); }"}, "glsl1": {"vert": "\nprecision mediump float;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matViewInv;\n  uniform highp mat4 cc_matViewProj;\nuniform highp mat4 cc_matWorld;\nvec4 quaternionFromAxis (vec3 xAxis,vec3 yAxis,vec3 zAxis){\n  mat3 m = mat3(xAxis,yAxis,zAxis);\n  float trace = m[0][0] + m[1][1] + m[2][2];\n  vec4 quat;\n  if (trace > 0.) {\n    float s = 0.5 / sqrt(trace + 1.0);\n    quat.w = 0.25 / s;\n    quat.x = (m[2][1] - m[1][2]) * s;\n    quat.y = (m[0][2] - m[2][0]) * s;\n    quat.z = (m[1][0] - m[0][1]) * s;\n  } else if ((m[0][0] > m[1][1]) && (m[0][0] > m[2][2])) {\n    float s = 2.0 * sqrt(1.0 + m[0][0] - m[1][1] - m[2][2]);\n    quat.w = (m[2][1] - m[1][2]) / s;\n    quat.x = 0.25 * s;\n    quat.y = (m[0][1] + m[1][0]) / s;\n    quat.z = (m[0][2] + m[2][0]) / s;\n  } else if (m[1][1] > m[2][2]) {\n    float s = 2.0 * sqrt(1.0 + m[1][1] - m[0][0] - m[2][2]);\n    quat.w = (m[0][2] - m[2][0]) / s;\n    quat.x = (m[0][1] + m[1][0]) / s;\n    quat.y = 0.25 * s;\n    quat.z = (m[1][2] + m[2][1]) / s;\n  } else {\n    float s = 2.0 * sqrt(1.0 + m[2][2] - m[0][0] - m[1][1]);\n    quat.w = (m[1][0] - m[0][1]) / s;\n    quat.x = (m[0][2] + m[2][0]) / s;\n    quat.y = (m[1][2] + m[2][1]) / s;\n    quat.z = 0.25 * s;\n  }\n  float len = quat.x * quat.x + quat.y * quat.y + quat.z * quat.z + quat.w * quat.w;\n  if (len > 0.) {\n    len = 1. / sqrt(len);\n    quat.x = quat.x * len;\n    quat.y = quat.y * len;\n    quat.z = quat.z * len;\n    quat.w = quat.w * len;\n  }\n  return quat;\n}\nvec4 quaternionFromEuler (vec3 angle){\n  float x = angle.x / 2.;\n  float y = angle.y / 2.;\n  float z = angle.z / 2.;\n  float sx = sin(x);\n  float cx = cos(x);\n  float sy = sin(y);\n  float cy = cos(y);\n  float sz = sin(z);\n  float cz = cos(z);\n  vec4 quat = vec4(0);\n  quat.x = sx * cy * cz + cx * sy * sz;\n  quat.y = cx * sy * cz + sx * cy * sz;\n  quat.z = cx * cy * sz - sx * sy * cz;\n  quat.w = cx * cy * cz - sx * sy * sz;\n  return quat;\n}\nvec4 quatMultiply (vec4 a, vec4 b){\n  vec4 quat;\n  quat.x = a.x * b.w + a.w * b.x + a.y * b.z - a.z * b.y;\n  quat.y = a.y * b.w + a.w * b.y + a.z * b.x - a.x * b.z;\n  quat.z = a.z * b.w + a.w * b.z + a.x * b.y - a.y * b.x;\n  quat.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;\n  return quat;\n}\nvoid rotateVecFromQuat (inout vec3 v, vec4 q){\n  float ix = q.w * v.x + q.y * v.z - q.z * v.y;\n  float iy = q.w * v.y + q.z * v.x - q.x * v.z;\n  float iz = q.w * v.z + q.x * v.y - q.y * v.x;\n  float iw = -q.x * v.x - q.y * v.y - q.z * v.z;\n  v.x = ix * q.w + iw * -q.x + iy * -q.z - iz * -q.y;\n  v.y = iy * q.w + iw * -q.y + iz * -q.x - ix * -q.z;\n  v.z = iz * q.w + iw * -q.z + ix * -q.y - iy * -q.x;\n}\nvec3 rotateInLocalSpace (vec3 pos, vec3 xAxis, vec3 yAxis, vec3 zAxis, vec4 q){\n  vec4 viewQuat = quaternionFromAxis(xAxis, yAxis, zAxis);\n  vec4 rotQuat = quatMultiply(viewQuat, q);\n  rotateVecFromQuat(pos, rotQuat);\n  return pos;\n}\nvarying mediump vec2 uv;\nvarying mediump vec4 color;\nvoid computeVertPos (inout vec4 pos, vec2 vertOffset, vec4 q, vec3 s\n  , mat4 viewInv\n) {\n  vec3 viewSpaceVert = vec3(vertOffset.x * s.x, vertOffset.y * s.y, 0.);\n  vec3 camX = normalize(vec3(viewInv[0][0], viewInv[1][0], viewInv[2][0]));\n  vec3 camY = normalize(vec3(viewInv[0][1], viewInv[1][1], viewInv[2][1]));\n  vec3 camZ = normalize(vec3(viewInv[0][2], viewInv[1][2], viewInv[2][2]));\n  pos.xyz += rotateInLocalSpace(viewSpaceVert, camX, camY, camZ, q);\n}\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\n   uniform vec4 cc_size_rotation;\nvec4 vs_main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matWorld * pos;\n  vec2 vertOffset = a_texCoord.xy - 0.5;\n  computeVertPos(pos, vertOffset, quaternionFromEuler(vec3(0., 0., cc_size_rotation.z)), vec3(cc_size_rotation.xy, 0.), cc_matViewInv);\n  pos = cc_matViewProj * pos;\n  uv = a_texCoord.xy;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vs_main(); }", "frag": "\nprecision mediump float;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nvarying vec2 uv;\nvarying vec4 color;\nuniform sampler2D mainTexture;\n  uniform vec4 tintColor;\nvec4 multiply () {\n  vec4 col;\n  vec4 texColor = texture2D(mainTexture, uv);\n  col.rgb = tintColor.rgb * texColor.rgb * color.rgb * vec3(2.0);\n  col.a = (1.0 - texColor.a) * (tintColor.a * color.a * 2.0);\n  return CCFragOutput(col);\n}\nvoid main() { gl_FragColor = multiply(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 61, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 43}}, "defines": [], "name": "particles/builtin-billboard|vert:vs_main|tinted-fs:multiply"}, {"blocks": [{"name": "Constants", "members": [{"name": "mainTiling_Offset", "type": 16, "count": 1}, {"name": "frameTile_velLenScale", "type": 16, "count": 1}, {"name": "scale", "type": 16, "count": 1}, {"name": "nodeRotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "builtin", "members": [{"name": "cc_size_rotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 1}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 1}, {"name": "a_color", "defines": [], "format": 44, "location": 2}], "varyings": [{"name": "uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}, {"name": "color", "type": 16, "count": 1, "defines": [], "stageFlags": 17, "location": 1}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [{"tags": {"builtin": "local"}, "name": "CCLocal", "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": [], "stageFlags": 1}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constants", "members": [{"name": "mainTiling_Offset", "type": 16, "count": 1}, {"name": "frameTile_velLenScale", "type": 16, "count": 1}, {"name": "scale", "type": 16, "count": 1}, {"name": "nodeRotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "builtin", "members": [{"name": "cc_size_rotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 1}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 3617975406, "glsl4": {"vert": "\nprecision mediump float;\nlayout(set = 1, binding = 0) uniform Constants {\n  vec4 mainTiling_Offset;\n  vec4 frameTile_velLenScale;\n  vec4 scale;\n  vec4 nodeRotation;\n};\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(set = 2, binding = 0) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nvec4 quaternionFromAxis (vec3 xAxis,vec3 yAxis,vec3 zAxis){\n  mat3 m = mat3(xAxis,yAxis,zAxis);\n  float trace = m[0][0] + m[1][1] + m[2][2];\n  vec4 quat;\n  if (trace > 0.) {\n    float s = 0.5 / sqrt(trace + 1.0);\n    quat.w = 0.25 / s;\n    quat.x = (m[2][1] - m[1][2]) * s;\n    quat.y = (m[0][2] - m[2][0]) * s;\n    quat.z = (m[1][0] - m[0][1]) * s;\n  } else if ((m[0][0] > m[1][1]) && (m[0][0] > m[2][2])) {\n    float s = 2.0 * sqrt(1.0 + m[0][0] - m[1][1] - m[2][2]);\n    quat.w = (m[2][1] - m[1][2]) / s;\n    quat.x = 0.25 * s;\n    quat.y = (m[0][1] + m[1][0]) / s;\n    quat.z = (m[0][2] + m[2][0]) / s;\n  } else if (m[1][1] > m[2][2]) {\n    float s = 2.0 * sqrt(1.0 + m[1][1] - m[0][0] - m[2][2]);\n    quat.w = (m[0][2] - m[2][0]) / s;\n    quat.x = (m[0][1] + m[1][0]) / s;\n    quat.y = 0.25 * s;\n    quat.z = (m[1][2] + m[2][1]) / s;\n  } else {\n    float s = 2.0 * sqrt(1.0 + m[2][2] - m[0][0] - m[1][1]);\n    quat.w = (m[1][0] - m[0][1]) / s;\n    quat.x = (m[0][2] + m[2][0]) / s;\n    quat.y = (m[1][2] + m[2][1]) / s;\n    quat.z = 0.25 * s;\n  }\n  float len = quat.x * quat.x + quat.y * quat.y + quat.z * quat.z + quat.w * quat.w;\n  if (len > 0.) {\n    len = 1. / sqrt(len);\n    quat.x = quat.x * len;\n    quat.y = quat.y * len;\n    quat.z = quat.z * len;\n    quat.w = quat.w * len;\n  }\n  return quat;\n}\nvec4 quaternionFromEuler (vec3 angle){\n  float x = angle.x / 2.;\n  float y = angle.y / 2.;\n  float z = angle.z / 2.;\n  float sx = sin(x);\n  float cx = cos(x);\n  float sy = sin(y);\n  float cy = cos(y);\n  float sz = sin(z);\n  float cz = cos(z);\n  vec4 quat = vec4(0);\n  quat.x = sx * cy * cz + cx * sy * sz;\n  quat.y = cx * sy * cz + sx * cy * sz;\n  quat.z = cx * cy * sz - sx * sy * cz;\n  quat.w = cx * cy * cz - sx * sy * sz;\n  return quat;\n}\nvec4 quatMultiply (vec4 a, vec4 b){\n  vec4 quat;\n  quat.x = a.x * b.w + a.w * b.x + a.y * b.z - a.z * b.y;\n  quat.y = a.y * b.w + a.w * b.y + a.z * b.x - a.x * b.z;\n  quat.z = a.z * b.w + a.w * b.z + a.x * b.y - a.y * b.x;\n  quat.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;\n  return quat;\n}\nvoid rotateVecFromQuat (inout vec3 v, vec4 q){\n  float ix = q.w * v.x + q.y * v.z - q.z * v.y;\n  float iy = q.w * v.y + q.z * v.x - q.x * v.z;\n  float iz = q.w * v.z + q.x * v.y - q.y * v.x;\n  float iw = -q.x * v.x - q.y * v.y - q.z * v.z;\n  v.x = ix * q.w + iw * -q.x + iy * -q.z - iz * -q.y;\n  v.y = iy * q.w + iw * -q.y + iz * -q.x - ix * -q.z;\n  v.z = iz * q.w + iw * -q.z + ix * -q.y - iy * -q.x;\n}\nvec3 rotateInLocalSpace (vec3 pos, vec3 xAxis, vec3 yAxis, vec3 zAxis, vec4 q){\n  vec4 viewQuat = quaternionFromAxis(xAxis, yAxis, zAxis);\n  vec4 rotQuat = quatMultiply(viewQuat, q);\n  rotateVecFromQuat(pos, rotQuat);\n  return pos;\n}\nlayout(location = 0) out mediump vec2 uv;\nlayout(location = 1) out mediump vec4 color;\nvoid computeVertPos (inout vec4 pos, vec2 vertOffset, vec4 q, vec3 s\n  , mat4 viewInv\n) {\n  vec3 viewSpaceVert = vec3(vertOffset.x * s.x, vertOffset.y * s.y, 0.);\n  vec3 camX = normalize(vec3(viewInv[0][0], viewInv[1][0], viewInv[2][0]));\n  vec3 camY = normalize(vec3(viewInv[0][1], viewInv[1][1], viewInv[2][1]));\n  vec3 camZ = normalize(vec3(viewInv[0][2], viewInv[1][2], viewInv[2][2]));\n  pos.xyz += rotateInLocalSpace(viewSpaceVert, camX, camY, camZ, q);\n}\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec2 a_texCoord;\nlayout(location = 2) in vec4 a_color;\nlayout(set = 1, binding = 1) uniform builtin {\n  vec4 cc_size_rotation;\n};\nvec4 vs_main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matWorld * pos;\n  vec2 vertOffset = a_texCoord.xy - 0.5;\n  computeVertPos(pos, vertOffset, quaternionFromEuler(vec3(0., 0., cc_size_rotation.z)), vec3(cc_size_rotation.xy, 0.), cc_matViewInv);\n  pos = cc_matViewProj * pos;\n  uv = a_texCoord.xy;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vs_main(); }", "frag": "\nprecision mediump float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nlayout(location = 0) in vec2 uv;\nlayout(location = 1) in vec4 color;\nlayout(set = 1, binding = 2) uniform sampler2D mainTexture;\nvec4 addSmooth () {\n  vec4 col = color * texture(mainTexture, uv);\n  col.rgb *= col.a;\n  return CCFragOutput(col);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = addSmooth(); }"}, "glsl3": {"vert": "\nprecision mediump float;\nlayout(std140) uniform Constants {\n  vec4 mainTiling_Offset;\n  vec4 frameTile_velLenScale;\n  vec4 scale;\n  vec4 nodeRotation;\n};\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(std140) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nvec4 quaternionFromAxis (vec3 xAxis,vec3 yAxis,vec3 zAxis){\n  mat3 m = mat3(xAxis,yAxis,zAxis);\n  float trace = m[0][0] + m[1][1] + m[2][2];\n  vec4 quat;\n  if (trace > 0.) {\n    float s = 0.5 / sqrt(trace + 1.0);\n    quat.w = 0.25 / s;\n    quat.x = (m[2][1] - m[1][2]) * s;\n    quat.y = (m[0][2] - m[2][0]) * s;\n    quat.z = (m[1][0] - m[0][1]) * s;\n  } else if ((m[0][0] > m[1][1]) && (m[0][0] > m[2][2])) {\n    float s = 2.0 * sqrt(1.0 + m[0][0] - m[1][1] - m[2][2]);\n    quat.w = (m[2][1] - m[1][2]) / s;\n    quat.x = 0.25 * s;\n    quat.y = (m[0][1] + m[1][0]) / s;\n    quat.z = (m[0][2] + m[2][0]) / s;\n  } else if (m[1][1] > m[2][2]) {\n    float s = 2.0 * sqrt(1.0 + m[1][1] - m[0][0] - m[2][2]);\n    quat.w = (m[0][2] - m[2][0]) / s;\n    quat.x = (m[0][1] + m[1][0]) / s;\n    quat.y = 0.25 * s;\n    quat.z = (m[1][2] + m[2][1]) / s;\n  } else {\n    float s = 2.0 * sqrt(1.0 + m[2][2] - m[0][0] - m[1][1]);\n    quat.w = (m[1][0] - m[0][1]) / s;\n    quat.x = (m[0][2] + m[2][0]) / s;\n    quat.y = (m[1][2] + m[2][1]) / s;\n    quat.z = 0.25 * s;\n  }\n  float len = quat.x * quat.x + quat.y * quat.y + quat.z * quat.z + quat.w * quat.w;\n  if (len > 0.) {\n    len = 1. / sqrt(len);\n    quat.x = quat.x * len;\n    quat.y = quat.y * len;\n    quat.z = quat.z * len;\n    quat.w = quat.w * len;\n  }\n  return quat;\n}\nvec4 quaternionFromEuler (vec3 angle){\n  float x = angle.x / 2.;\n  float y = angle.y / 2.;\n  float z = angle.z / 2.;\n  float sx = sin(x);\n  float cx = cos(x);\n  float sy = sin(y);\n  float cy = cos(y);\n  float sz = sin(z);\n  float cz = cos(z);\n  vec4 quat = vec4(0);\n  quat.x = sx * cy * cz + cx * sy * sz;\n  quat.y = cx * sy * cz + sx * cy * sz;\n  quat.z = cx * cy * sz - sx * sy * cz;\n  quat.w = cx * cy * cz - sx * sy * sz;\n  return quat;\n}\nvec4 quatMultiply (vec4 a, vec4 b){\n  vec4 quat;\n  quat.x = a.x * b.w + a.w * b.x + a.y * b.z - a.z * b.y;\n  quat.y = a.y * b.w + a.w * b.y + a.z * b.x - a.x * b.z;\n  quat.z = a.z * b.w + a.w * b.z + a.x * b.y - a.y * b.x;\n  quat.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;\n  return quat;\n}\nvoid rotateVecFromQuat (inout vec3 v, vec4 q){\n  float ix = q.w * v.x + q.y * v.z - q.z * v.y;\n  float iy = q.w * v.y + q.z * v.x - q.x * v.z;\n  float iz = q.w * v.z + q.x * v.y - q.y * v.x;\n  float iw = -q.x * v.x - q.y * v.y - q.z * v.z;\n  v.x = ix * q.w + iw * -q.x + iy * -q.z - iz * -q.y;\n  v.y = iy * q.w + iw * -q.y + iz * -q.x - ix * -q.z;\n  v.z = iz * q.w + iw * -q.z + ix * -q.y - iy * -q.x;\n}\nvec3 rotateInLocalSpace (vec3 pos, vec3 xAxis, vec3 yAxis, vec3 zAxis, vec4 q){\n  vec4 viewQuat = quaternionFromAxis(xAxis, yAxis, zAxis);\n  vec4 rotQuat = quatMultiply(viewQuat, q);\n  rotateVecFromQuat(pos, rotQuat);\n  return pos;\n}\nout mediump vec2 uv;\nout mediump vec4 color;\nvoid computeVertPos (inout vec4 pos, vec2 vertOffset, vec4 q, vec3 s\n  , mat4 viewInv\n) {\n  vec3 viewSpaceVert = vec3(vertOffset.x * s.x, vertOffset.y * s.y, 0.);\n  vec3 camX = normalize(vec3(viewInv[0][0], viewInv[1][0], viewInv[2][0]));\n  vec3 camY = normalize(vec3(viewInv[0][1], viewInv[1][1], viewInv[2][1]));\n  vec3 camZ = normalize(vec3(viewInv[0][2], viewInv[1][2], viewInv[2][2]));\n  pos.xyz += rotateInLocalSpace(viewSpaceVert, camX, camY, camZ, q);\n}\nin vec3 a_position;\nin vec2 a_texCoord;\nin vec4 a_color;\nlayout(std140) uniform builtin {\n  vec4 cc_size_rotation;\n};\nvec4 vs_main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matWorld * pos;\n  vec2 vertOffset = a_texCoord.xy - 0.5;\n  computeVertPos(pos, vertOffset, quaternionFromEuler(vec3(0., 0., cc_size_rotation.z)), vec3(cc_size_rotation.xy, 0.), cc_matViewInv);\n  pos = cc_matViewProj * pos;\n  uv = a_texCoord.xy;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vs_main(); }", "frag": "\nprecision mediump float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nin vec2 uv;\nin vec4 color;\nuniform sampler2D mainTexture;\nvec4 addSmooth () {\n  vec4 col = color * texture(mainTexture, uv);\n  col.rgb *= col.a;\n  return CCFragOutput(col);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = addSmooth(); }"}, "glsl1": {"vert": "\nprecision mediump float;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matViewInv;\n  uniform highp mat4 cc_matViewProj;\nuniform highp mat4 cc_matWorld;\nvec4 quaternionFromAxis (vec3 xAxis,vec3 yAxis,vec3 zAxis){\n  mat3 m = mat3(xAxis,yAxis,zAxis);\n  float trace = m[0][0] + m[1][1] + m[2][2];\n  vec4 quat;\n  if (trace > 0.) {\n    float s = 0.5 / sqrt(trace + 1.0);\n    quat.w = 0.25 / s;\n    quat.x = (m[2][1] - m[1][2]) * s;\n    quat.y = (m[0][2] - m[2][0]) * s;\n    quat.z = (m[1][0] - m[0][1]) * s;\n  } else if ((m[0][0] > m[1][1]) && (m[0][0] > m[2][2])) {\n    float s = 2.0 * sqrt(1.0 + m[0][0] - m[1][1] - m[2][2]);\n    quat.w = (m[2][1] - m[1][2]) / s;\n    quat.x = 0.25 * s;\n    quat.y = (m[0][1] + m[1][0]) / s;\n    quat.z = (m[0][2] + m[2][0]) / s;\n  } else if (m[1][1] > m[2][2]) {\n    float s = 2.0 * sqrt(1.0 + m[1][1] - m[0][0] - m[2][2]);\n    quat.w = (m[0][2] - m[2][0]) / s;\n    quat.x = (m[0][1] + m[1][0]) / s;\n    quat.y = 0.25 * s;\n    quat.z = (m[1][2] + m[2][1]) / s;\n  } else {\n    float s = 2.0 * sqrt(1.0 + m[2][2] - m[0][0] - m[1][1]);\n    quat.w = (m[1][0] - m[0][1]) / s;\n    quat.x = (m[0][2] + m[2][0]) / s;\n    quat.y = (m[1][2] + m[2][1]) / s;\n    quat.z = 0.25 * s;\n  }\n  float len = quat.x * quat.x + quat.y * quat.y + quat.z * quat.z + quat.w * quat.w;\n  if (len > 0.) {\n    len = 1. / sqrt(len);\n    quat.x = quat.x * len;\n    quat.y = quat.y * len;\n    quat.z = quat.z * len;\n    quat.w = quat.w * len;\n  }\n  return quat;\n}\nvec4 quaternionFromEuler (vec3 angle){\n  float x = angle.x / 2.;\n  float y = angle.y / 2.;\n  float z = angle.z / 2.;\n  float sx = sin(x);\n  float cx = cos(x);\n  float sy = sin(y);\n  float cy = cos(y);\n  float sz = sin(z);\n  float cz = cos(z);\n  vec4 quat = vec4(0);\n  quat.x = sx * cy * cz + cx * sy * sz;\n  quat.y = cx * sy * cz + sx * cy * sz;\n  quat.z = cx * cy * sz - sx * sy * cz;\n  quat.w = cx * cy * cz - sx * sy * sz;\n  return quat;\n}\nvec4 quatMultiply (vec4 a, vec4 b){\n  vec4 quat;\n  quat.x = a.x * b.w + a.w * b.x + a.y * b.z - a.z * b.y;\n  quat.y = a.y * b.w + a.w * b.y + a.z * b.x - a.x * b.z;\n  quat.z = a.z * b.w + a.w * b.z + a.x * b.y - a.y * b.x;\n  quat.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;\n  return quat;\n}\nvoid rotateVecFromQuat (inout vec3 v, vec4 q){\n  float ix = q.w * v.x + q.y * v.z - q.z * v.y;\n  float iy = q.w * v.y + q.z * v.x - q.x * v.z;\n  float iz = q.w * v.z + q.x * v.y - q.y * v.x;\n  float iw = -q.x * v.x - q.y * v.y - q.z * v.z;\n  v.x = ix * q.w + iw * -q.x + iy * -q.z - iz * -q.y;\n  v.y = iy * q.w + iw * -q.y + iz * -q.x - ix * -q.z;\n  v.z = iz * q.w + iw * -q.z + ix * -q.y - iy * -q.x;\n}\nvec3 rotateInLocalSpace (vec3 pos, vec3 xAxis, vec3 yAxis, vec3 zAxis, vec4 q){\n  vec4 viewQuat = quaternionFromAxis(xAxis, yAxis, zAxis);\n  vec4 rotQuat = quatMultiply(viewQuat, q);\n  rotateVecFromQuat(pos, rotQuat);\n  return pos;\n}\nvarying mediump vec2 uv;\nvarying mediump vec4 color;\nvoid computeVertPos (inout vec4 pos, vec2 vertOffset, vec4 q, vec3 s\n  , mat4 viewInv\n) {\n  vec3 viewSpaceVert = vec3(vertOffset.x * s.x, vertOffset.y * s.y, 0.);\n  vec3 camX = normalize(vec3(viewInv[0][0], viewInv[1][0], viewInv[2][0]));\n  vec3 camY = normalize(vec3(viewInv[0][1], viewInv[1][1], viewInv[2][1]));\n  vec3 camZ = normalize(vec3(viewInv[0][2], viewInv[1][2], viewInv[2][2]));\n  pos.xyz += rotateInLocalSpace(viewSpaceVert, camX, camY, camZ, q);\n}\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\n   uniform vec4 cc_size_rotation;\nvec4 vs_main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matWorld * pos;\n  vec2 vertOffset = a_texCoord.xy - 0.5;\n  computeVertPos(pos, vertOffset, quaternionFromEuler(vec3(0., 0., cc_size_rotation.z)), vec3(cc_size_rotation.xy, 0.), cc_matViewInv);\n  pos = cc_matViewProj * pos;\n  uv = a_texCoord.xy;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vs_main(); }", "frag": "\nprecision mediump float;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nvarying vec2 uv;\nvarying vec4 color;\nuniform sampler2D mainTexture;\nvec4 addSmooth () {\n  vec4 col = color * texture2D(mainTexture, uv);\n  col.rgb *= col.a;\n  return CCFragOutput(col);\n}\nvoid main() { gl_FragColor = addSmooth(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 61, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 42}}, "defines": [], "name": "particles/builtin-billboard|vert:vs_main|no-tint-fs:addSmooth"}, {"blocks": [{"name": "Constants", "members": [{"name": "mainTiling_Offset", "type": 16, "count": 1}, {"name": "frameTile_velLenScale", "type": 16, "count": 1}, {"name": "scale", "type": 16, "count": 1}, {"name": "nodeRotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "builtin", "members": [{"name": "cc_size_rotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 1}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "defines": [], "format": 32, "location": 0}, {"name": "a_texCoord", "defines": [], "format": 21, "location": 1}, {"name": "a_color", "defines": [], "format": 44, "location": 2}], "varyings": [{"name": "uv", "type": 14, "count": 1, "defines": [], "stageFlags": 17, "location": 0}, {"name": "color", "type": 16, "count": 1, "defines": [], "stageFlags": 17, "location": 1}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "defines": [], "stageFlags": 16, "location": 0}], "descriptors": [{"rate": 0, "blocks": [{"tags": {"builtin": "local"}, "name": "CCLocal", "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": [], "stageFlags": 1}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constants", "members": [{"name": "mainTiling_Offset", "type": 16, "count": 1}, {"name": "frameTile_velLenScale", "type": 16, "count": 1}, {"name": "scale", "type": 16, "count": 1}, {"name": "nodeRotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 0}, {"name": "builtin", "members": [{"name": "cc_size_rotation", "type": 16, "count": 1}], "defines": [], "stageFlags": 1, "binding": 1}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "defines": [], "stageFlags": 16, "sampleType": 0, "binding": 2}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"tags": {"builtin": "global"}, "name": "CCGlobal", "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}, {"tags": {"builtin": "global"}, "name": "CCCamera", "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": [], "stageFlags": 17}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "hash": 3619147658, "glsl4": {"vert": "\nprecision mediump float;\nlayout(set = 1, binding = 0) uniform Constants {\n  vec4 mainTiling_Offset;\n  vec4 frameTile_velLenScale;\n  vec4 scale;\n  vec4 nodeRotation;\n};\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(set = 2, binding = 0) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nvec4 quaternionFromAxis (vec3 xAxis,vec3 yAxis,vec3 zAxis){\n  mat3 m = mat3(xAxis,yAxis,zAxis);\n  float trace = m[0][0] + m[1][1] + m[2][2];\n  vec4 quat;\n  if (trace > 0.) {\n    float s = 0.5 / sqrt(trace + 1.0);\n    quat.w = 0.25 / s;\n    quat.x = (m[2][1] - m[1][2]) * s;\n    quat.y = (m[0][2] - m[2][0]) * s;\n    quat.z = (m[1][0] - m[0][1]) * s;\n  } else if ((m[0][0] > m[1][1]) && (m[0][0] > m[2][2])) {\n    float s = 2.0 * sqrt(1.0 + m[0][0] - m[1][1] - m[2][2]);\n    quat.w = (m[2][1] - m[1][2]) / s;\n    quat.x = 0.25 * s;\n    quat.y = (m[0][1] + m[1][0]) / s;\n    quat.z = (m[0][2] + m[2][0]) / s;\n  } else if (m[1][1] > m[2][2]) {\n    float s = 2.0 * sqrt(1.0 + m[1][1] - m[0][0] - m[2][2]);\n    quat.w = (m[0][2] - m[2][0]) / s;\n    quat.x = (m[0][1] + m[1][0]) / s;\n    quat.y = 0.25 * s;\n    quat.z = (m[1][2] + m[2][1]) / s;\n  } else {\n    float s = 2.0 * sqrt(1.0 + m[2][2] - m[0][0] - m[1][1]);\n    quat.w = (m[1][0] - m[0][1]) / s;\n    quat.x = (m[0][2] + m[2][0]) / s;\n    quat.y = (m[1][2] + m[2][1]) / s;\n    quat.z = 0.25 * s;\n  }\n  float len = quat.x * quat.x + quat.y * quat.y + quat.z * quat.z + quat.w * quat.w;\n  if (len > 0.) {\n    len = 1. / sqrt(len);\n    quat.x = quat.x * len;\n    quat.y = quat.y * len;\n    quat.z = quat.z * len;\n    quat.w = quat.w * len;\n  }\n  return quat;\n}\nvec4 quaternionFromEuler (vec3 angle){\n  float x = angle.x / 2.;\n  float y = angle.y / 2.;\n  float z = angle.z / 2.;\n  float sx = sin(x);\n  float cx = cos(x);\n  float sy = sin(y);\n  float cy = cos(y);\n  float sz = sin(z);\n  float cz = cos(z);\n  vec4 quat = vec4(0);\n  quat.x = sx * cy * cz + cx * sy * sz;\n  quat.y = cx * sy * cz + sx * cy * sz;\n  quat.z = cx * cy * sz - sx * sy * cz;\n  quat.w = cx * cy * cz - sx * sy * sz;\n  return quat;\n}\nvec4 quatMultiply (vec4 a, vec4 b){\n  vec4 quat;\n  quat.x = a.x * b.w + a.w * b.x + a.y * b.z - a.z * b.y;\n  quat.y = a.y * b.w + a.w * b.y + a.z * b.x - a.x * b.z;\n  quat.z = a.z * b.w + a.w * b.z + a.x * b.y - a.y * b.x;\n  quat.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;\n  return quat;\n}\nvoid rotateVecFromQuat (inout vec3 v, vec4 q){\n  float ix = q.w * v.x + q.y * v.z - q.z * v.y;\n  float iy = q.w * v.y + q.z * v.x - q.x * v.z;\n  float iz = q.w * v.z + q.x * v.y - q.y * v.x;\n  float iw = -q.x * v.x - q.y * v.y - q.z * v.z;\n  v.x = ix * q.w + iw * -q.x + iy * -q.z - iz * -q.y;\n  v.y = iy * q.w + iw * -q.y + iz * -q.x - ix * -q.z;\n  v.z = iz * q.w + iw * -q.z + ix * -q.y - iy * -q.x;\n}\nvec3 rotateInLocalSpace (vec3 pos, vec3 xAxis, vec3 yAxis, vec3 zAxis, vec4 q){\n  vec4 viewQuat = quaternionFromAxis(xAxis, yAxis, zAxis);\n  vec4 rotQuat = quatMultiply(viewQuat, q);\n  rotateVecFromQuat(pos, rotQuat);\n  return pos;\n}\nlayout(location = 0) out mediump vec2 uv;\nlayout(location = 1) out mediump vec4 color;\nvoid computeVertPos (inout vec4 pos, vec2 vertOffset, vec4 q, vec3 s\n  , mat4 viewInv\n) {\n  vec3 viewSpaceVert = vec3(vertOffset.x * s.x, vertOffset.y * s.y, 0.);\n  vec3 camX = normalize(vec3(viewInv[0][0], viewInv[1][0], viewInv[2][0]));\n  vec3 camY = normalize(vec3(viewInv[0][1], viewInv[1][1], viewInv[2][1]));\n  vec3 camZ = normalize(vec3(viewInv[0][2], viewInv[1][2], viewInv[2][2]));\n  pos.xyz += rotateInLocalSpace(viewSpaceVert, camX, camY, camZ, q);\n}\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec2 a_texCoord;\nlayout(location = 2) in vec4 a_color;\nlayout(set = 1, binding = 1) uniform builtin {\n  vec4 cc_size_rotation;\n};\nvec4 vs_main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matWorld * pos;\n  vec2 vertOffset = a_texCoord.xy - 0.5;\n  computeVertPos(pos, vertOffset, quaternionFromEuler(vec3(0., 0., cc_size_rotation.z)), vec3(cc_size_rotation.xy, 0.), cc_matViewInv);\n  pos = cc_matViewProj * pos;\n  uv = a_texCoord.xy;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vs_main(); }", "frag": "\nprecision mediump float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nlayout(location = 0) in vec2 uv;\nlayout(location = 1) in vec4 color;\nlayout(set = 1, binding = 2) uniform sampler2D mainTexture;\nvec4 premultiplied () {\n  vec4 col = color * texture(mainTexture, uv) * color.a;\n  return CCFragOutput(col);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = premultiplied(); }"}, "glsl3": {"vert": "\nprecision mediump float;\nlayout(std140) uniform Constants {\n  vec4 mainTiling_Offset;\n  vec4 frameTile_velLenScale;\n  vec4 scale;\n  vec4 nodeRotation;\n};\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(std140) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nvec4 quaternionFromAxis (vec3 xAxis,vec3 yAxis,vec3 zAxis){\n  mat3 m = mat3(xAxis,yAxis,zAxis);\n  float trace = m[0][0] + m[1][1] + m[2][2];\n  vec4 quat;\n  if (trace > 0.) {\n    float s = 0.5 / sqrt(trace + 1.0);\n    quat.w = 0.25 / s;\n    quat.x = (m[2][1] - m[1][2]) * s;\n    quat.y = (m[0][2] - m[2][0]) * s;\n    quat.z = (m[1][0] - m[0][1]) * s;\n  } else if ((m[0][0] > m[1][1]) && (m[0][0] > m[2][2])) {\n    float s = 2.0 * sqrt(1.0 + m[0][0] - m[1][1] - m[2][2]);\n    quat.w = (m[2][1] - m[1][2]) / s;\n    quat.x = 0.25 * s;\n    quat.y = (m[0][1] + m[1][0]) / s;\n    quat.z = (m[0][2] + m[2][0]) / s;\n  } else if (m[1][1] > m[2][2]) {\n    float s = 2.0 * sqrt(1.0 + m[1][1] - m[0][0] - m[2][2]);\n    quat.w = (m[0][2] - m[2][0]) / s;\n    quat.x = (m[0][1] + m[1][0]) / s;\n    quat.y = 0.25 * s;\n    quat.z = (m[1][2] + m[2][1]) / s;\n  } else {\n    float s = 2.0 * sqrt(1.0 + m[2][2] - m[0][0] - m[1][1]);\n    quat.w = (m[1][0] - m[0][1]) / s;\n    quat.x = (m[0][2] + m[2][0]) / s;\n    quat.y = (m[1][2] + m[2][1]) / s;\n    quat.z = 0.25 * s;\n  }\n  float len = quat.x * quat.x + quat.y * quat.y + quat.z * quat.z + quat.w * quat.w;\n  if (len > 0.) {\n    len = 1. / sqrt(len);\n    quat.x = quat.x * len;\n    quat.y = quat.y * len;\n    quat.z = quat.z * len;\n    quat.w = quat.w * len;\n  }\n  return quat;\n}\nvec4 quaternionFromEuler (vec3 angle){\n  float x = angle.x / 2.;\n  float y = angle.y / 2.;\n  float z = angle.z / 2.;\n  float sx = sin(x);\n  float cx = cos(x);\n  float sy = sin(y);\n  float cy = cos(y);\n  float sz = sin(z);\n  float cz = cos(z);\n  vec4 quat = vec4(0);\n  quat.x = sx * cy * cz + cx * sy * sz;\n  quat.y = cx * sy * cz + sx * cy * sz;\n  quat.z = cx * cy * sz - sx * sy * cz;\n  quat.w = cx * cy * cz - sx * sy * sz;\n  return quat;\n}\nvec4 quatMultiply (vec4 a, vec4 b){\n  vec4 quat;\n  quat.x = a.x * b.w + a.w * b.x + a.y * b.z - a.z * b.y;\n  quat.y = a.y * b.w + a.w * b.y + a.z * b.x - a.x * b.z;\n  quat.z = a.z * b.w + a.w * b.z + a.x * b.y - a.y * b.x;\n  quat.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;\n  return quat;\n}\nvoid rotateVecFromQuat (inout vec3 v, vec4 q){\n  float ix = q.w * v.x + q.y * v.z - q.z * v.y;\n  float iy = q.w * v.y + q.z * v.x - q.x * v.z;\n  float iz = q.w * v.z + q.x * v.y - q.y * v.x;\n  float iw = -q.x * v.x - q.y * v.y - q.z * v.z;\n  v.x = ix * q.w + iw * -q.x + iy * -q.z - iz * -q.y;\n  v.y = iy * q.w + iw * -q.y + iz * -q.x - ix * -q.z;\n  v.z = iz * q.w + iw * -q.z + ix * -q.y - iy * -q.x;\n}\nvec3 rotateInLocalSpace (vec3 pos, vec3 xAxis, vec3 yAxis, vec3 zAxis, vec4 q){\n  vec4 viewQuat = quaternionFromAxis(xAxis, yAxis, zAxis);\n  vec4 rotQuat = quatMultiply(viewQuat, q);\n  rotateVecFromQuat(pos, rotQuat);\n  return pos;\n}\nout mediump vec2 uv;\nout mediump vec4 color;\nvoid computeVertPos (inout vec4 pos, vec2 vertOffset, vec4 q, vec3 s\n  , mat4 viewInv\n) {\n  vec3 viewSpaceVert = vec3(vertOffset.x * s.x, vertOffset.y * s.y, 0.);\n  vec3 camX = normalize(vec3(viewInv[0][0], viewInv[1][0], viewInv[2][0]));\n  vec3 camY = normalize(vec3(viewInv[0][1], viewInv[1][1], viewInv[2][1]));\n  vec3 camZ = normalize(vec3(viewInv[0][2], viewInv[1][2], viewInv[2][2]));\n  pos.xyz += rotateInLocalSpace(viewSpaceVert, camX, camY, camZ, q);\n}\nin vec3 a_position;\nin vec2 a_texCoord;\nin vec4 a_color;\nlayout(std140) uniform builtin {\n  vec4 cc_size_rotation;\n};\nvec4 vs_main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matWorld * pos;\n  vec2 vertOffset = a_texCoord.xy - 0.5;\n  computeVertPos(pos, vertOffset, quaternionFromEuler(vec3(0., 0., cc_size_rotation.z)), vec3(cc_size_rotation.xy, 0.), cc_matViewInv);\n  pos = cc_matViewProj * pos;\n  uv = a_texCoord.xy;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vs_main(); }", "frag": "\nprecision mediump float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nin vec2 uv;\nin vec4 color;\nuniform sampler2D mainTexture;\nvec4 premultiplied () {\n  vec4 col = color * texture(mainTexture, uv) * color.a;\n  return CCFragOutput(col);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = premultiplied(); }"}, "glsl1": {"vert": "\nprecision mediump float;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matViewInv;\n  uniform highp mat4 cc_matViewProj;\nuniform highp mat4 cc_matWorld;\nvec4 quaternionFromAxis (vec3 xAxis,vec3 yAxis,vec3 zAxis){\n  mat3 m = mat3(xAxis,yAxis,zAxis);\n  float trace = m[0][0] + m[1][1] + m[2][2];\n  vec4 quat;\n  if (trace > 0.) {\n    float s = 0.5 / sqrt(trace + 1.0);\n    quat.w = 0.25 / s;\n    quat.x = (m[2][1] - m[1][2]) * s;\n    quat.y = (m[0][2] - m[2][0]) * s;\n    quat.z = (m[1][0] - m[0][1]) * s;\n  } else if ((m[0][0] > m[1][1]) && (m[0][0] > m[2][2])) {\n    float s = 2.0 * sqrt(1.0 + m[0][0] - m[1][1] - m[2][2]);\n    quat.w = (m[2][1] - m[1][2]) / s;\n    quat.x = 0.25 * s;\n    quat.y = (m[0][1] + m[1][0]) / s;\n    quat.z = (m[0][2] + m[2][0]) / s;\n  } else if (m[1][1] > m[2][2]) {\n    float s = 2.0 * sqrt(1.0 + m[1][1] - m[0][0] - m[2][2]);\n    quat.w = (m[0][2] - m[2][0]) / s;\n    quat.x = (m[0][1] + m[1][0]) / s;\n    quat.y = 0.25 * s;\n    quat.z = (m[1][2] + m[2][1]) / s;\n  } else {\n    float s = 2.0 * sqrt(1.0 + m[2][2] - m[0][0] - m[1][1]);\n    quat.w = (m[1][0] - m[0][1]) / s;\n    quat.x = (m[0][2] + m[2][0]) / s;\n    quat.y = (m[1][2] + m[2][1]) / s;\n    quat.z = 0.25 * s;\n  }\n  float len = quat.x * quat.x + quat.y * quat.y + quat.z * quat.z + quat.w * quat.w;\n  if (len > 0.) {\n    len = 1. / sqrt(len);\n    quat.x = quat.x * len;\n    quat.y = quat.y * len;\n    quat.z = quat.z * len;\n    quat.w = quat.w * len;\n  }\n  return quat;\n}\nvec4 quaternionFromEuler (vec3 angle){\n  float x = angle.x / 2.;\n  float y = angle.y / 2.;\n  float z = angle.z / 2.;\n  float sx = sin(x);\n  float cx = cos(x);\n  float sy = sin(y);\n  float cy = cos(y);\n  float sz = sin(z);\n  float cz = cos(z);\n  vec4 quat = vec4(0);\n  quat.x = sx * cy * cz + cx * sy * sz;\n  quat.y = cx * sy * cz + sx * cy * sz;\n  quat.z = cx * cy * sz - sx * sy * cz;\n  quat.w = cx * cy * cz - sx * sy * sz;\n  return quat;\n}\nvec4 quatMultiply (vec4 a, vec4 b){\n  vec4 quat;\n  quat.x = a.x * b.w + a.w * b.x + a.y * b.z - a.z * b.y;\n  quat.y = a.y * b.w + a.w * b.y + a.z * b.x - a.x * b.z;\n  quat.z = a.z * b.w + a.w * b.z + a.x * b.y - a.y * b.x;\n  quat.w = a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z;\n  return quat;\n}\nvoid rotateVecFromQuat (inout vec3 v, vec4 q){\n  float ix = q.w * v.x + q.y * v.z - q.z * v.y;\n  float iy = q.w * v.y + q.z * v.x - q.x * v.z;\n  float iz = q.w * v.z + q.x * v.y - q.y * v.x;\n  float iw = -q.x * v.x - q.y * v.y - q.z * v.z;\n  v.x = ix * q.w + iw * -q.x + iy * -q.z - iz * -q.y;\n  v.y = iy * q.w + iw * -q.y + iz * -q.x - ix * -q.z;\n  v.z = iz * q.w + iw * -q.z + ix * -q.y - iy * -q.x;\n}\nvec3 rotateInLocalSpace (vec3 pos, vec3 xAxis, vec3 yAxis, vec3 zAxis, vec4 q){\n  vec4 viewQuat = quaternionFromAxis(xAxis, yAxis, zAxis);\n  vec4 rotQuat = quatMultiply(viewQuat, q);\n  rotateVecFromQuat(pos, rotQuat);\n  return pos;\n}\nvarying mediump vec2 uv;\nvarying mediump vec4 color;\nvoid computeVertPos (inout vec4 pos, vec2 vertOffset, vec4 q, vec3 s\n  , mat4 viewInv\n) {\n  vec3 viewSpaceVert = vec3(vertOffset.x * s.x, vertOffset.y * s.y, 0.);\n  vec3 camX = normalize(vec3(viewInv[0][0], viewInv[1][0], viewInv[2][0]));\n  vec3 camY = normalize(vec3(viewInv[0][1], viewInv[1][1], viewInv[2][1]));\n  vec3 camZ = normalize(vec3(viewInv[0][2], viewInv[1][2], viewInv[2][2]));\n  pos.xyz += rotateInLocalSpace(viewSpaceVert, camX, camY, camZ, q);\n}\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\n   uniform vec4 cc_size_rotation;\nvec4 vs_main() {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matWorld * pos;\n  vec2 vertOffset = a_texCoord.xy - 0.5;\n  computeVertPos(pos, vertOffset, quaternionFromEuler(vec3(0., 0., cc_size_rotation.z)), vec3(cc_size_rotation.xy, 0.), cc_matViewInv);\n  pos = cc_matViewProj * pos;\n  uv = a_texCoord.xy;\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vs_main(); }", "frag": "\nprecision mediump float;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nvarying vec2 uv;\nvarying vec4 color;\nuniform sampler2D mainTexture;\nvec4 premultiplied () {\n  vec4 col = color * texture2D(mainTexture, uv) * color.a;\n  return CCFragOutput(col);\n}\nvoid main() { gl_FragColor = premultiplied(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 61, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 42}}, "defines": [], "name": "particles/builtin-billboard|vert:vs_main|no-tint-fs:premultiplied"}], "combinations": [], "hideInEditor": false}