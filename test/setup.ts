import * as fs from 'fs-extra';
import * as path from 'path';

// 测试环境设置
beforeAll(() => {
  // 设置测试环境变量
  process.env.NODE_ENV = 'test';
});

// 每个测试后清理
afterEach(async () => {
  // 清理测试文件
  const testDir = path.join(process.cwd(), 'test-temp');
  if (await fs.pathExists(testDir)) {
    await fs.remove(testDir);
  }
});

// 全局测试工具
global.createTestDir = async () => {
  const testDir = path.join(process.cwd(), 'test-temp');
  await fs.ensureDir(testDir);
  return testDir;
};

global.createTestFile = async (filePath: string, content: string) => {
  await fs.ensureDir(path.dirname(filePath));
  await fs.writeFile(filePath, content);
};