import * as fs from 'fs-extra';
import * as path from 'path';
import { FileUtils } from '../src/utils/FileUtils';

describe('FileUtils', () => {
  let testDir: string;

  beforeEach(async () => {
    testDir = await (global as any).createTestDir();
  });

  describe('copyDir', () => {
    it('should copy directory recursively', async () => {
      const srcDir = path.join(testDir, 'src');
      const destDir = path.join(testDir, 'dest');

      // 创建源目录结构
      await fs.ensureDir(path.join(srcDir, 'subdir'));
      await fs.writeFile(path.join(srcDir, 'file1.txt'), 'content1');
      await fs.writeFile(path.join(srcDir, 'subdir', 'file2.txt'), 'content2');

      await FileUtils.copyDir(srcDir, destDir);

      // 验证文件已复制
      expect(await fs.pathExists(path.join(destDir, 'file1.txt'))).toBe(true);
      expect(await fs.pathExists(path.join(destDir, 'subdir', 'file2.txt'))).toBe(true);

      const content1 = await fs.readFile(path.join(destDir, 'file1.txt'), 'utf-8');
      const content2 = await fs.readFile(path.join(destDir, 'subdir', 'file2.txt'), 'utf-8');

      expect(content1).toBe('content1');
      expect(content2).toBe('content2');
    });

    it('should exclude files matching patterns', async () => {
      const srcDir = path.join(testDir, 'src');
      const destDir = path.join(testDir, 'dest');

      // 创建测试文件
      await fs.writeFile(path.join(srcDir, 'keep.txt'), 'keep');
      await fs.writeFile(path.join(srcDir, 'exclude.log'), 'exclude');
      await fs.ensureDir(path.join(srcDir, 'node_modules'));
      await fs.writeFile(path.join(srcDir, 'node_modules', 'package.json'), '{}');

      await FileUtils.copyDir(srcDir, destDir, {
        excludePatterns: ['*.log', 'node_modules']
      });

      expect(await fs.pathExists(path.join(destDir, 'keep.txt'))).toBe(true);
      expect(await fs.pathExists(path.join(destDir, 'exclude.log'))).toBe(false);
      expect(await fs.pathExists(path.join(destDir, 'node_modules'))).toBe(false);
    });
  });

  describe('getDirectorySize', () => {
    it('should calculate directory size correctly', async () => {
      const dir = path.join(testDir, 'sizetest');
      await fs.ensureDir(dir);

      // 创建测试文件
      await fs.writeFile(path.join(dir, 'file1.txt'), 'a'.repeat(100));
      await fs.writeFile(path.join(dir, 'file2.txt'), 'b'.repeat(200));

      const size = await FileUtils.getDirectorySize(dir);
      expect(size).toBe(300);
    });

    it('should return 0 for non-existent directory', async () => {
      const size = await FileUtils.getDirectorySize(path.join(testDir, 'nonexistent'));
      expect(size).toBe(0);
    });
  });

  describe('formatFileSize', () => {
    it('should format bytes correctly', () => {
      expect(FileUtils.formatFileSize(0)).toBe('0 B');
      expect(FileUtils.formatFileSize(1024)).toBe('1.00 KB');
      expect(FileUtils.formatFileSize(1024 * 1024)).toBe('1.00 MB');
      expect(FileUtils.formatFileSize(1024 * 1024 * 1024)).toBe('1.00 GB');
    });
  });

  describe('findFiles', () => {
    it('should find files matching pattern', async () => {
      const dir = path.join(testDir, 'findtest');
      await fs.ensureDir(path.join(dir, 'subdir'));
      
      await fs.writeFile(path.join(dir, 'test1.js'), '');
      await fs.writeFile(path.join(dir, 'test2.ts'), '');
      await fs.writeFile(path.join(dir, 'subdir', 'test3.js'), '');

      const jsFiles = await FileUtils.findFiles(dir, '*.js');
      
      expect(jsFiles).toHaveLength(2);
      expect(jsFiles.some(f => f.endsWith('test1.js'))).toBe(true);
      expect(jsFiles.some(f => f.endsWith('test3.js'))).toBe(true);
    });

    it('should respect recursive option', async () => {
      const dir = path.join(testDir, 'findtest');
      await fs.ensureDir(path.join(dir, 'subdir'));
      
      await fs.writeFile(path.join(dir, 'test1.js'), '');
      await fs.writeFile(path.join(dir, 'subdir', 'test2.js'), '');

      const filesRecursive = await FileUtils.findFiles(dir, '*.js', { recursive: true });
      const filesNonRecursive = await FileUtils.findFiles(dir, '*.js', { recursive: false });

      expect(filesRecursive).toHaveLength(2);
      expect(filesNonRecursive).toHaveLength(1);
    });
  });

  describe('backup', () => {
    it('should create backup file', async () => {
      const originalFile = path.join(testDir, 'original.txt');
      await fs.writeFile(originalFile, 'original content');

      const backupPath = await FileUtils.backup(originalFile);

      expect(await fs.pathExists(backupPath)).toBe(true);
      expect(backupPath).toContain('original.txt.backup.');

      const backupContent = await fs.readFile(backupPath, 'utf-8');
      expect(backupContent).toBe('original content');
    });

    it('should create backup in specified directory', async () => {
      const originalFile = path.join(testDir, 'original.txt');
      const backupDir = path.join(testDir, 'backups');
      
      await fs.writeFile(originalFile, 'content');

      const backupPath = await FileUtils.backup(originalFile, backupDir);

      expect(backupPath.startsWith(backupDir)).toBe(true);
      expect(await fs.pathExists(backupPath)).toBe(true);
    });
  });

  describe('safeDelete', () => {
    it('should move file to trash directory', async () => {
      const file = path.join(testDir, 'todelete.txt');
      await fs.writeFile(file, 'delete me');

      await FileUtils.safeDelete(file);

      expect(await fs.pathExists(file)).toBe(false);

      const trashDir = path.join(process.cwd(), '.trash');
      const trashFiles = await fs.readdir(trashDir);
      expect(trashFiles.some(f => f.startsWith('todelete.txt.'))).toBe(true);
    });
  });

  describe('sanitizePath', () => {
    it('should allow safe paths', () => {
      const basePath = '/base/path';
      const result = FileUtils.sanitizePath('subdir/file.txt', basePath);
      expect(result).toBe(path.resolve(basePath, 'subdir/file.txt'));
    });

    it('should reject dangerous paths', () => {
      const basePath = '/base/path';
      
      expect(() => FileUtils.sanitizePath('../../../etc/passwd', basePath))
        .toThrow('不安全的文件路径');
        
      expect(() => FileUtils.sanitizePath('/absolute/path', basePath))
        .toThrow('不安全的文件路径');
    });
  });

  describe('isSubPath', () => {
    it('should correctly identify sub paths', () => {
      expect(FileUtils.isSubPath('/parent/child', '/parent')).toBe(true);
      expect(FileUtils.isSubPath('/parent/child/grandchild', '/parent')).toBe(true);
      expect(FileUtils.isSubPath('/parent', '/parent')).toBe(true);
      expect(FileUtils.isSubPath('/other', '/parent')).toBe(false);
      expect(FileUtils.isSubPath('/parent/../other', '/parent')).toBe(false);
    });
  });
});