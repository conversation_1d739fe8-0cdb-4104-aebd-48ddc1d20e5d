"use strict";
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];
    result["default"] = mod;
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
// 测试环境设置
beforeAll(() => {
    // 设置测试环境变量
    process.env.NODE_ENV = 'test';
});
// 每个测试后清理
afterEach(async () => {
    // 清理测试文件
    const testDir = path.join(process.cwd(), 'test-temp');
    if (await fs.pathExists(testDir)) {
        await fs.remove(testDir);
    }
});
// 全局测试工具
global.createTestDir = async () => {
    const testDir = path.join(process.cwd(), 'test-temp');
    await fs.ensureDir(testDir);
    return testDir;
};
global.createTestFile = async (filePath, content) => {
    await fs.ensureDir(path.dirname(filePath));
    await fs.writeFile(filePath, content);
};
