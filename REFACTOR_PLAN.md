# 🔧 version-craft 独立化改造计划

## 📋 **当前状况分析**

### **严重问题发现**
在测试回滚功能时发现了一个**致命的设计缺陷**：

```bash
# 问题场景
当前状态: LuckyCoin项目包含version-craft工具代码
执行命令: version-craft rollback-to 0.1.4
Git操作: await this.git.checkout([tagName])
结果: 整个项目代码回滚到0.1.4，包括version-craft本身！
后果: 工具代码丢失，编译失败，工具"自毁"
```

### **根本原因**
- ❌ **错误架构**: 工具代码位于被管理的项目内部
- ❌ **Git操作混乱**: 工具操作的Git仓库包含了工具本身
- ❌ **工作目录错误**: `process.cwd()` 指向工具目录而非目标项目

## 🎯 **改造目标**

### **正确的架构设计**
```
# 改造前 (错误)
LuckyCoin/
├── version-craft/          # ❌ 工具在项目内
│   ├── src/
│   └── package.json
├── web/                        # 项目代码
├── assets/                     # 项目代码
└── version-craft.config.json    # 项目配置

# 改造后 (正确)
version-craft/              # ✅ 独立的全局工具
├── src/
├── package.json
├── bin/version-craft
└── REFACTOR_PLAN.md           # 本文档

LuckyCoin/                      # ✅ 纯项目代码
├── web/
├── assets/
├── package.json
└── version-craft.config.json
```

## 🔄 **详细改造步骤**

### **第一阶段: 工具独立化**

#### **1.1 创建独立工具目录**
```bash
# 在合适的位置创建独立目录
mkdir D:/Desktop/version-craft
cp -r D:/Desktop/LuckyCoin/version-craft/* D:/Desktop/version-craft/
cd D:/Desktop/version-craft
```

#### **1.2 修改package.json配置**
```json
{
  "name": "version-craft",
  "version": "1.0.0",
  "description": "游戏版本管理工具",
  "bin": {
    "version-craft": "./dist/cli.js"
  },
  "preferGlobal": true,
  "files": [
    "dist/**/*",
    "bin/**/*"
  ]
}
```

### **第二阶段: 核心逻辑改造**

#### **2.1 项目根目录查找机制**
```typescript
// src/utils/ProjectDetector.ts (新文件)
export class ProjectDetector {
  /**
   * 查找目标项目根目录
   * 从当前目录向上查找包含 version-craft.config.json 的目录
   */
  static findProjectRoot(startDir?: string): string {
    let currentDir = startDir || process.cwd();
    
    while (currentDir !== path.parse(currentDir).root) {
      const configPath = path.join(currentDir, 'version-craft.config.json');
      if (fs.existsSync(configPath)) {
        return currentDir;
      }
      currentDir = path.dirname(currentDir);
    }
    
    throw new Error('未找到 version-craft.config.json，请在项目根目录或子目录执行命令');
  }

  /**
   * 验证项目目录有效性
   */
  static validateProjectRoot(projectRoot: string): boolean {
    const configPath = path.join(projectRoot, 'version-craft.config.json');
    return fs.existsSync(configPath);
  }
}
```

#### **2.2 所有Manager类改造**
```typescript
// 改造模式 (适用于所有Manager类)
export class VersionManager {
  private projectRoot: string;  // ✅ 目标项目根目录
  private git: SimpleGit;       // ✅ 操作目标项目的Git

  constructor(projectRoot?: string) {
    // ✅ 工具运行在独立目录，但操作目标项目
    this.projectRoot = projectRoot || ProjectDetector.findProjectRoot();
    this.git = simpleGit(this.projectRoot);
    this.configManager = new ConfigManager(this.projectRoot);
  }
}

// 需要改造的类列表:
// - VersionManager
// - ConfigManager  
// - BuildManager
// - DeployManager
// - HotUpdateManager
```

#### **2.3 命令类改造**
```typescript
// 改造模式 (适用于所有Command类)
export class RollbackCommand {
  private projectRoot: string;
  private git: SimpleGit;

  constructor(projectRoot?: string) {
    this.projectRoot = projectRoot || ProjectDetector.findProjectRoot();
    this.git = simpleGit(this.projectRoot);  // ✅ Git操作指向目标项目
    
    // 初始化所有Manager时传入projectRoot
    this.versionManager = new VersionManager(this.projectRoot);
    this.buildManager = new BuildManager(this.projectRoot);
    this.deployManager = new DeployManager(this.projectRoot);
    this.configManager = new ConfigManager(this.projectRoot);
  }
}

// 需要改造的类列表:
// - VersionCommand
// - BuildCommand  
// - DeployCommand
// - ConfigCommand
// - RollbackCommand
// - HotUpdateCommand
```

### **第三阶段: CLI入口改造**

#### **3.1 添加项目路径参数**
```typescript
// src/cli.ts
program
  .version(packageJson.version)
  .option('-p, --project <path>', '指定项目路径 (默认: 当前目录)')
  .option('-v, --verbose', '显示详细日志')
  .hook('preAction', (thisCommand) => {
    // 设置全局项目路径
    const projectPath = thisCommand.opts().project;
    if (projectPath) {
      process.env.GAME_VERSION_PROJECT_ROOT = path.resolve(projectPath);
    }
  });
```

#### **3.2 命令初始化改造**
```typescript
// 所有命令都需要传入项目路径
const projectRoot = process.env.GAME_VERSION_PROJECT_ROOT;

program
  .command('bump [type]')
  .action(async (type, options) => {
    const versionCommand = new VersionCommand(projectRoot);
    await versionCommand.bumpVersion(type, options);
  });
```

## 🔧 **关键文件改造清单**

### **核心工具类 (必须改造)**
- [ ] `src/utils/ProjectDetector.ts` - **新建**，项目检测工具
- [ ] `src/modules/config/ConfigManager.ts` - 添加projectRoot参数
- [ ] `src/modules/version/VersionManager.ts` - 添加projectRoot参数
- [ ] `src/modules/build/BuildManager.ts` - 添加projectRoot参数
- [ ] `src/modules/deploy/DeployManager.ts` - 添加projectRoot参数
- [ ] `src/modules/hotupdate/HotUpdateManager.ts` - 添加projectRoot参数

### **命令类 (必须改造)**
- [ ] `src/commands/VersionCommand.ts` - 添加projectRoot参数
- [ ] `src/commands/BuildCommand.ts` - 添加projectRoot参数
- [ ] `src/commands/DeployCommand.ts` - 添加projectRoot参数
- [ ] `src/commands/ConfigCommand.ts` - 添加projectRoot参数
- [ ] `src/commands/RollbackCommand.ts` - **重点改造**，修复Git操作
- [ ] `src/commands/HotUpdateCommand.ts` - 添加projectRoot参数

### **入口文件 (必须改造)**
- [ ] `src/cli.ts` - 添加项目路径参数支持
- [ ] `package.json` - 配置全局安装

## ⚠️ **改造注意事项**

### **关键原则**
1. **工具代码与项目代码完全分离**
2. **所有Git操作必须指向目标项目**
3. **所有文件操作必须基于目标项目根目录**
4. **保持向后兼容性**

### **测试验证**
改造完成后必须验证：
- [ ] 工具可以全局安装: `npm install -g .`
- [ ] 可以在任意项目中使用: `cd /any/project && version-craft current`
- [ ] 回滚操作不影响工具本身: `version-craft rollback-to x.x.x`
- [ ] 所有版本操作正常: `version-craft bump patch`

## 📝 **当前会话状态记录**

### **已完成的优化**
- ✅ 版本号维护规则完全实现 (VERSION_MAINTENANCE_RULES.md)
- ✅ 智能回滚策略 (版本号递增+回滚标记)
- ✅ 预发布版本完整生命周期
- ✅ 多文件版本同步机制
- ✅ 版本冲突检测和验证
- ✅ 所有核心功能测试通过

### **发现的致命问题**
- ❌ 工具架构设计错误 (工具在项目内部)
- ❌ Git操作会回滚工具本身代码
- ❌ 导致工具"自毁"和编译失败

### **下一步行动**
1. **立即执行独立化改造** (按本文档步骤)
2. **重新测试所有功能** (特别是回滚功能)
3. **更新文档** (反映新的架构)

## 💻 **具体代码改造示例**

### **ProjectDetector.ts 完整实现**
```typescript
import * as fs from 'fs-extra';
import * as path from 'path';

export class ProjectDetector {
  static findProjectRoot(startDir?: string): string {
    let currentDir = startDir || process.cwd();

    while (currentDir !== path.parse(currentDir).root) {
      const configPath = path.join(currentDir, 'version-craft.config.json');
      if (fs.existsSync(configPath)) {
        console.log(`找到项目根目录: ${currentDir}`);
        return currentDir;
      }
      currentDir = path.dirname(currentDir);
    }

    throw new Error(`未找到 version-craft.config.json\n请在包含配置文件的项目目录中执行命令`);
  }

  static validateProjectRoot(projectRoot: string): boolean {
    const configPath = path.join(projectRoot, 'version-craft.config.json');
    const packagePath = path.join(projectRoot, 'package.json');

    return fs.existsSync(configPath) && fs.existsSync(packagePath);
  }

  static getProjectInfo(projectRoot: string): { name: string; version: string } {
    const configPath = path.join(projectRoot, 'version-craft.config.json');
    const config = fs.readJSONSync(configPath);

    return {
      name: config.project.name,
      version: config.project.version
    };
  }
}
```

### **ConfigManager 改造示例**
```typescript
// 改造前
export class ConfigManager {
  private configPath: string;

  constructor() {
    this.configPath = path.join(process.cwd(), 'version-craft.config.json'); // ❌ 错误
  }
}

// 改造后
export class ConfigManager {
  private configPath: string;
  private projectRoot: string;

  constructor(projectRoot?: string) {
    this.projectRoot = projectRoot || ProjectDetector.findProjectRoot(); // ✅ 正确
    this.configPath = path.join(this.projectRoot, 'version-craft.config.json');
  }
}
```

### **RollbackCommand 关键改造**
```typescript
// 改造前 (致命错误)
export class RollbackCommand {
  constructor() {
    this.git = simpleGit(); // ❌ 操作当前目录 (工具目录)
  }

  private async updateVersionTo(version: string): Promise<void> {
    await this.git.checkout([tagName]); // ❌ 回滚工具代码！
  }
}

// 改造后 (正确)
export class RollbackCommand {
  private projectRoot: string;

  constructor(projectRoot?: string) {
    this.projectRoot = projectRoot || ProjectDetector.findProjectRoot();
    this.git = simpleGit(this.projectRoot); // ✅ 操作目标项目
  }

  private async updateVersionTo(version: string): Promise<void> {
    // ✅ 现在Git操作只影响目标项目，不会影响工具本身
    await this.git.checkout([tagName]);
  }
}
```

## 🚀 **改造后的使用方式**

### **全局安装**
```bash
# 在独立的工具目录中
cd /path/to/version-craft
npm install -g .

# 验证安装
version-craft --version
```

### **在任意项目中使用**
```bash
# 方式1: 在项目根目录执行
cd /path/to/LuckyCoin
version-craft current
version-craft bump patch
version-craft rollback-to 1.0.0  # ✅ 安全，不会影响工具

# 方式2: 指定项目路径
version-craft -p /path/to/LuckyCoin current
version-craft -p /path/to/LuckyCoin bump patch

# 方式3: 在项目子目录执行 (自动向上查找)
cd /path/to/LuckyCoin/web
version-craft current  # 自动找到 /path/to/LuckyCoin
```

## 📊 **改造前后对比**

| 方面 | 改造前 (错误) | 改造后 (正确) |
|------|---------------|---------------|
| **工具位置** | 项目内部 | 独立目录 |
| **Git操作** | 操作工具+项目 | 只操作目标项目 |
| **回滚安全** | 工具会自毁 | 工具完全安全 |
| **使用方式** | 只能在特定项目 | 可用于任意项目 |
| **维护性** | 工具与项目耦合 | 完全解耦 |

## ⏰ **改造时间估算**

- **第一阶段** (工具独立化): 30分钟
- **第二阶段** (核心逻辑改造): 2小时
- **第三阶段** (CLI改造): 1小时
- **测试验证**: 1小时
- **总计**: 约4.5小时

---

**重要提醒**: 这个改造是解决当前致命问题的唯一正确方案，必须优先执行！

**下次会话开始时，请先阅读此文档了解完整情况。**
