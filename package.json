{"name": "version-craft", "version": "1.0.0", "description": "游戏版本管理工具 - Monorepo", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "npm run build --workspaces", "build:core": "npm run build --workspace=packages/core", "build:cli": "npm run build --workspace=packages/cli", "build:gui": "npm run build --workspace=packages/gui", "clean": "npm run clean --workspaces", "dev:cli": "npm run dev --workspace=packages/cli", "dev:gui": "npm run dev --workspace=packages/gui", "start:cli": "npm run start --workspace=packages/cli", "start:gui": "npm run start --workspace=packages/gui"}, "devDependencies": {"typescript": "^5.2.2", "@types/node": "^20.8.0", "rimraf": "^5.0.5", "electron": "^25.9.0", "electron-builder": "^24.13.3"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}