{"name": "version-craft", "version": "1.0.0", "description": "游戏版本管理工具 - Monorepo", "private": true, "workspaces": ["packages/*", "cli", "gui"], "scripts": {"build": "npm run build --workspaces", "build:core": "npm run build --workspace=packages/core", "build:cli": "npm run build --workspace=cli", "build:gui": "npm run build --workspace=gui", "clean": "npm run clean --workspaces", "dev:cli": "npm run dev --workspace=cli", "dev:gui": "npm run dev --workspace=gui", "start:cli": "npm run start --workspace=cli", "start:gui": "npm run start --workspace=gui"}, "devDependencies": {"@types/node": "^20.8.0", "electron": "^25.9.0", "electron-builder": "^24.13.3", "rimraf": "^5.0.5", "typescript": "^5.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}, "dependencies": {"axios": "^1.11.0", "inquirer": "^8.2.6"}}