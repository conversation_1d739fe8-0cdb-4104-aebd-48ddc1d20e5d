<template>
  <div class="hot-update-container">
    <div class="header">
      <h2>🔥 热更新管理</h2>
      <p class="subtitle">管理游戏资源的热更新，支持增量更新和版本回滚</p>
    </div>

    <!-- 操作面板 -->
    <div class="action-panel">
      <div class="action-group">
        <h3>📦 资源清单</h3>
        <div class="form-group">
          <label>目标版本:</label>
          <input v-model="manifestOptions.version" placeholder="留空使用当前版本" />
        </div>
        <div class="form-group">
          <label>平台:</label>
          <select v-model="manifestOptions.platform">
            <option value="">所有平台</option>
            <option value="web-mobile">Web Mobile</option>
            <option value="android">Android</option>
            <option value="ios">iOS</option>
          </select>
        </div>
        <button @click="generateManifest" :disabled="isGeneratingManifest" class="btn-primary">
          {{ isGeneratingManifest ? '生成中...' : '生成资源清单' }}
        </button>
      </div>

      <div class="action-group">
        <h3>🔄 增量更新</h3>
        <div class="form-group">
          <label>源版本:</label>
          <input v-model="patchOptions.fromVersion" placeholder="例如: 1.0.0" required />
        </div>
        <div class="form-group">
          <label>目标版本:</label>
          <input v-model="patchOptions.toVersion" placeholder="例如: 1.0.1" required />
        </div>
        <div class="form-group">
          <label>平台:</label>
          <select v-model="patchOptions.platform">
            <option value="">所有平台</option>
            <option value="web-mobile">Web Mobile</option>
            <option value="android">Android</option>
            <option value="ios">iOS</option>
          </select>
        </div>
        <button @click="generatePatch" :disabled="isGeneratingPatch || !canGeneratePatch" class="btn-primary">
          {{ isGeneratingPatch ? '生成中...' : '生成增量更新包' }}
        </button>
      </div>

      <div class="action-group">
        <h3>🔍 工具</h3>
        <div class="tool-buttons">
          <button @click="checkChanges" :disabled="!canCheckChanges" class="btn-secondary">
            检查资源变更
          </button>
          <button @click="cleanCache" :disabled="isCleaningCache" class="btn-secondary">
            {{ isCleaningCache ? '清理中...' : '清理缓存' }}
          </button>
          <button @click="refreshHistory" class="btn-secondary">
            刷新历史
          </button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-panel" v-if="stats">
      <h3>📊 统计信息</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ stats.total }}</div>
          <div class="stat-label">总更新次数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ stats.successful }}</div>
          <div class="stat-label">成功次数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ formatSize(stats.totalSize) }}</div>
          <div class="stat-label">总更新大小</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ Object.keys(stats.byPlatform || {}).length }}</div>
          <div class="stat-label">支持平台</div>
        </div>
      </div>
    </div>

    <!-- 更新历史 -->
    <div class="history-panel">
      <h3>📋 更新历史</h3>
      <div class="history-filters">
        <select v-model="historyFilter" @change="refreshHistory">
          <option value="">所有平台</option>
          <option value="web-mobile">Web Mobile</option>
          <option value="android">Android</option>
          <option value="ios">iOS</option>
        </select>
      </div>
      
      <div class="history-list" v-if="history.length > 0">
        <div v-for="item in history" :key="item.id" class="history-item">
          <div class="history-info">
            <div class="history-version">
              {{ item.fromVersion }} → {{ item.toVersion }}
            </div>
            <div class="history-platform">{{ item.platform }}</div>
            <div class="history-date">{{ formatDate(item.timestamp) }}</div>
          </div>
          <div class="history-status">
            <span :class="['status-badge', item.success ? 'success' : 'failed']">
              {{ item.success ? '成功' : '失败' }}
            </span>
          </div>
          <div class="history-size" v-if="item.size">
            {{ formatSize(item.size) }}
          </div>
        </div>
      </div>
      
      <div v-else class="empty-state">
        <p>暂无热更新历史记录</p>
      </div>
    </div>

    <!-- 结果显示 -->
    <div v-if="lastResult" class="result-panel">
      <h3>📄 操作结果</h3>
      <div class="result-content">
        <pre>{{ JSON.stringify(lastResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HotUpdate',
  data() {
    return {
      // 生成清单选项
      manifestOptions: {
        version: '',
        platform: ''
      },
      
      // 生成补丁选项
      patchOptions: {
        fromVersion: '',
        toVersion: '',
        platform: ''
      },
      
      // 状态
      isGeneratingManifest: false,
      isGeneratingPatch: false,
      isCleaningCache: false,
      
      // 数据
      history: [],
      stats: null,
      lastResult: null,
      historyFilter: ''
    };
  },
  
  computed: {
    canGeneratePatch() {
      return this.patchOptions.fromVersion && this.patchOptions.toVersion;
    },
    
    canCheckChanges() {
      return this.patchOptions.fromVersion && this.patchOptions.toVersion;
    }
  },
  
  async mounted() {
    await this.loadData();
  },
  
  methods: {
    async loadData() {
      await Promise.all([
        this.refreshHistory(),
        this.loadStats()
      ]);
    },
    
    async generateManifest() {
      this.isGeneratingManifest = true;
      try {
        const result = await window.electronAPI.invoke('hotupdate-generate-manifest', this.manifestOptions);
        if (result.success) {
          this.lastResult = result.data;
          this.$emit('show-message', { type: 'success', message: result.data.message });
        } else {
          this.$emit('show-message', { type: 'error', message: result.error });
        }
      } catch (error) {
        this.$emit('show-message', { type: 'error', message: '生成资源清单失败: ' + error.message });
      } finally {
        this.isGeneratingManifest = false;
      }
    },
    
    async generatePatch() {
      this.isGeneratingPatch = true;
      try {
        const result = await window.electronAPI.invoke('hotupdate-generate-patch', this.patchOptions);
        if (result.success) {
          this.lastResult = result.data;
          this.$emit('show-message', { type: 'success', message: result.data.message });
          await this.refreshHistory();
        } else {
          this.$emit('show-message', { type: 'error', message: result.error });
        }
      } catch (error) {
        this.$emit('show-message', { type: 'error', message: '生成增量更新包失败: ' + error.message });
      } finally {
        this.isGeneratingPatch = false;
      }
    },
    
    async checkChanges() {
      try {
        const result = await window.electronAPI.invoke('hotupdate-check-changes', 
          this.patchOptions.fromVersion, this.patchOptions.toVersion);
        if (result.success) {
          this.lastResult = result.data;
          this.$emit('show-message', { type: 'success', message: result.data.message });
        } else {
          this.$emit('show-message', { type: 'error', message: result.error });
        }
      } catch (error) {
        this.$emit('show-message', { type: 'error', message: '检查资源变更失败: ' + error.message });
      }
    },
    
    async cleanCache() {
      this.isCleaningCache = true;
      try {
        const result = await window.electronAPI.invoke('hotupdate-clean-cache');
        if (result.success) {
          this.$emit('show-message', { type: 'success', message: result.data.message });
        } else {
          this.$emit('show-message', { type: 'error', message: result.error });
        }
      } catch (error) {
        this.$emit('show-message', { type: 'error', message: '清理缓存失败: ' + error.message });
      } finally {
        this.isCleaningCache = false;
      }
    },
    
    async refreshHistory() {
      try {
        const result = await window.electronAPI.invoke('hotupdate-get-history', this.historyFilter);
        if (result.success) {
          this.history = result.data.history || [];
        }
      } catch (error) {
        console.error('获取热更新历史失败:', error);
      }
    },
    
    async loadStats() {
      try {
        const result = await window.electronAPI.invoke('hotupdate-get-stats');
        if (result.success) {
          this.stats = result.data;
        }
      } catch (error) {
        console.error('获取热更新统计失败:', error);
      }
    },
    
    formatDate(timestamp) {
      return new Date(timestamp).toLocaleString();
    },
    
    formatSize(bytes) {
      if (!bytes) return '0 B';
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
  }
};
</script>

<style scoped>
.hot-update-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: 30px;
  text-align: center;
}

.header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.subtitle {
  color: #7f8c8d;
  font-size: 14px;
}

.action-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.action-group {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.action-group h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #495057;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.tool-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.btn-primary,
.btn-secondary {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.btn-primary:disabled,
.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.stats-panel {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.stat-item {
  text-align: center;
  background: white;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
}

.history-panel {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.history-filters {
  margin-bottom: 15px;
}

.history-filters select {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.history-list {
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 8px;
}

.history-info {
  flex: 1;
}

.history-version {
  font-weight: 500;
  color: #495057;
}

.history-platform {
  font-size: 12px;
  color: #6c757d;
  margin-top: 2px;
}

.history-date {
  font-size: 11px;
  color: #adb5bd;
  margin-top: 2px;
}

.history-status {
  margin-right: 15px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.status-badge.success {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.history-size {
  font-size: 12px;
  color: #6c757d;
  min-width: 60px;
  text-align: right;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.result-panel {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.result-content {
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  max-height: 300px;
  overflow-y: auto;
}

.result-content pre {
  margin: 0;
  font-size: 12px;
  color: #495057;
}
</style>
