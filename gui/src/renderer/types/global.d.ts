// 全局类型声明

// Electron API 接口
export interface ElectronAPI {
  // 项目管理
  selectProjectDirectory: () => Promise<{ success: boolean; path?: string; error?: string }>;
  getCurrentProject: () => Promise<string | null>;
  getProjectInfo: () => Promise<{ success: boolean; data?: any; error?: string }>;

  // 版本管理
  getCurrentVersion: () => Promise<{ success: boolean; data?: any; error?: string }>;
  getVersionHistory: () => Promise<{ success: boolean; data?: any; error?: string }>;
  bumpVersion: (options: any) => Promise<{ success: boolean; data?: any; error?: string }>;
  rollbackVersion: (targetVersion: string, force?: boolean) => Promise<{ success: boolean; data?: any; error?: string }>;

  // 构建管理
  startBuild: (platform: string, options?: any) => Promise<{ success: boolean; data?: any; error?: string }>;
  cancelBuild: (buildId: string) => Promise<{ success: boolean; data?: any; error?: string }>;

  // 系统操作
  openExternal: (url: string) => Promise<void>;
  showItemInFolder: (path: string) => Promise<void>;
  quitApp: () => Promise<void>;

  // IPC 通信
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  on: (channel: string, callback: (data: any) => void) => void;
  off: (channel: string, callback?: (data: any) => void) => void;

  // 事件监听（兼容性保留）
  onBuildProgress: (callback: (data: any) => void) => void;
  onVersionChanged: (callback: (data: any) => void) => void;
  offBuildProgress: () => void;
  offVersionChanged: () => void;
}

// 扩展 Window 接口
declare global {
  interface Window {
    electronAPI?: ElectronAPI;
  }
}

// Vite 环境变量
declare const __APP_VERSION__: string;
declare const __BUILD_TIME__: string;

export {};
