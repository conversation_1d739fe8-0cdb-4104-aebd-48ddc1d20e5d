<template>
  <div class="hot-update-management">
    <HotUpdate @show-message="showMessage" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import HotUpdate from '../components/HotUpdate.vue';

// 消息通知
const showMessage = (messageData: { type: string; message: string }) => {
  // 这里可以集成到全局的消息通知系统
  console.log(`[${messageData.type.toUpperCase()}] ${messageData.message}`);
  
  // 如果有全局的通知系统，可以这样调用：
  // const appStore = useAppStore();
  // appStore.showNotification(messageData.type, messageData.message);
};
</script>

<style scoped>
.hot-update-management {
  height: 100%;
  overflow: auto;
}
</style>
