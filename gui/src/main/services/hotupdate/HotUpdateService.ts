import { HotUpdateManager } from '@version-craft/core';

// 定义本地类型接口
interface ResourceManifest {
  version: string;
  buildNumber: number;
  releaseDate: string;
  resources: ResourceInfo[];
  totalSize: number;
}

interface ResourceInfo {
  path: string;
  size: number;
  checksum: string;
  version: string;
  priority: number;
  type: 'script' | 'config' | 'texture' | 'audio' | 'asset' | 'other';
  compressed: boolean;
  url?: string;
}

interface IncrementalUpdate {
  fromVersion: string;
  toVersion: string;
  addedFiles: ResourceInfo[];
  modifiedFiles: ResourceInfo[];
  deletedFiles: string[];
  totalSize: number;
  updateType: 'patch' | 'full';
}

/**
 * 热更新管理服务
 * 基于 HotUpdateManager 的完整功能实现
 * 
 * 核心功能：
 * 1. generateResourceManifest - 生成资源清单
 * 2. generateIncrementalUpdate - 生成增量更新包
 * 3. addVersionTags - 为资源文件添加版本标记
 */
export class HotUpdateService {
  private hotUpdateManager: HotUpdateManager;
  private projectPath: string;

  constructor(projectPath: string) {
    this.projectPath = projectPath;
    // 设置工作目录并初始化热更新管理器
    process.chdir(projectPath);
    this.hotUpdateManager = new HotUpdateManager();
  }

  /**
   * 生成热更新资源清单 - 对应 CLI 的 hotupdate-manifest
   */
  async generateManifest(options: {
    version?: string;
    outputPath?: string;
    includePatterns?: string[];
    excludePatterns?: string[];
    baseUrl?: string;
    mandatory?: boolean;
    description?: string;
  } = {}): Promise<any> {
    console.log('✅ [HotUpdateService] Generating resource manifest using core package:', options);

    try {
      const manifest = await this.hotUpdateManager.generateResourceManifest(
        options.version || '1.0.0',
        {
          outputPath: options.outputPath,
          includePatterns: options.includePatterns,
          excludePatterns: options.excludePatterns,
          baseUrl: options.baseUrl,
          mandatory: options.mandatory,
          description: options.description
        }
      );
      
      console.log('✅ [HotUpdateService] Resource manifest generated successfully:', manifest);
      
      return {
        message: '热更新资源清单生成成功',
        manifest,
        resourceCount: manifest.resources.length,
        totalSize: manifest.totalSize
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error generating manifest:', error);
      throw new Error(`生成资源清单失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 生成增量更新包 - 对应 CLI 的 hotupdate-patch
   */
  async generatePatch(options: {
    fromVersion: string;
    toVersion: string;
    outputDir?: string;
    baseUrl?: string;
  }): Promise<any> {
    console.log('✅ [HotUpdateService] Generating incremental update using core package:', options);

    try {
      const incrementalUpdate = await this.hotUpdateManager.generateIncrementalUpdate(
        options.fromVersion,
        options.toVersion,
        {
          outputDir: options.outputDir,
          baseUrl: options.baseUrl
        }
      );
      
      console.log('✅ [HotUpdateService] Incremental update generated successfully:', incrementalUpdate);
      
      return {
        message: `增量更新包生成成功: ${options.fromVersion} → ${options.toVersion}`,
        incrementalUpdate,
        addedCount: incrementalUpdate.addedFiles.length,
        modifiedCount: incrementalUpdate.modifiedFiles.length,
        deletedCount: incrementalUpdate.deletedFiles.length,
        totalSize: incrementalUpdate.totalSize,
        updateType: incrementalUpdate.updateType
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error generating patch:', error);
      throw new Error(`生成增量更新包失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 为资源文件添加版本标记 - 对应 CLI 的 hotupdate-tag
   */
  async tagResources(options: {
    version: string;
    resourcePaths?: string[];
    outputDir?: string;
    tagFormat?: 'suffix' | 'query' | 'header';
  }): Promise<any> {
    console.log('✅ [HotUpdateService] Adding version tags using core package:', options);

    try {
      const versionedFiles = await this.hotUpdateManager.addVersionTags(
        options.version,
        {
          resourcePaths: options.resourcePaths,
          outputDir: options.outputDir,
          tagFormat: options.tagFormat
        }
      );
      
      console.log('✅ [HotUpdateService] Version tags added successfully:', versionedFiles.size, 'files');
      
      return {
        message: `资源版本标记成功: ${versionedFiles.size} 个文件`,
        versionedFiles: Array.from(versionedFiles.entries()).map(([original, versioned]) => ({
          original,
          versioned
        })),
        processedCount: versionedFiles.size,
        version: options.version,
        tagFormat: options.tagFormat
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error adding version tags:', error);
      throw new Error(`资源版本标记失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 验证资源清单完整性 - 基于核心包功能实现
   */
  async verifyManifest(manifestPath: string): Promise<any> {
    console.log('✅ [HotUpdateService] Verifying manifest:', manifestPath);

    try {
      // 读取清单文件
      const fs = require('fs-extra');
      const path = require('path');
      
      if (!await fs.pathExists(manifestPath)) {
        throw new Error(`清单文件不存在: ${manifestPath}`);
      }

      const manifest: ResourceManifest = await fs.readJSON(manifestPath);
      
      // 验证清单结构
      const requiredFields = ['version', 'buildNumber', 'releaseDate', 'resources'];
      const missingFields = requiredFields.filter(field => !(manifest as any)[field]);
      
      if (missingFields.length > 0) {
        throw new Error(`清单文件缺少必要字段: ${missingFields.join(', ')}`);
      }

      // 验证资源文件
      let validFiles = 0;
      let invalidFiles = 0;
      const invalidFilesList: string[] = [];

      for (const resource of manifest.resources) {
        const resourcePath = path.resolve(path.dirname(manifestPath), '..', resource.path);
        if (await fs.pathExists(resourcePath)) {
          validFiles++;
        } else {
          invalidFiles++;
          invalidFilesList.push(resource.path);
        }
      }

      const verification = {
        valid: invalidFiles === 0,
        manifestPath,
        version: manifest.version,
        totalResources: manifest.resources.length,
        validFiles,
        invalidFiles,
        invalidFilesList: invalidFilesList.slice(0, 10), // 只显示前10个
        totalSize: manifest.totalSize
      };

      console.log('✅ [HotUpdateService] Manifest verification completed:', verification);
      
      return {
        message: verification.valid ? '资源清单验证通过' : `资源清单验证失败: ${invalidFiles} 个文件缺失`,
        verification
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error verifying manifest:', error);
      throw new Error(`验证资源清单失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取热更新历史记录 - 模拟实现
   */
  async getUpdateHistory(platform?: string): Promise<any> {
    console.log('✅ [HotUpdateService] Getting update history...');

    try {
      // 模拟历史记录，实际项目中可以从数据库或文件系统读取
      const history = [
        {
          id: '1',
          fromVersion: '1.0.0',
          toVersion: '1.0.1',
          platform: platform || 'web-mobile',
          success: true,
          timestamp: new Date(Date.now() - 86400000).toISOString(), // 1天前
          size: 1024 * 1024, // 1MB
          updateType: 'patch'
        },
        {
          id: '2',
          fromVersion: '1.0.1',
          toVersion: '1.0.2',
          platform: platform || 'web-mobile',
          success: true,
          timestamp: new Date().toISOString(),
          size: 2048 * 1024, // 2MB
          updateType: 'patch'
        }
      ];
      
      // 根据平台过滤
      let filteredHistory = history;
      if (platform) {
        filteredHistory = history.filter((record: any) => record.platform === platform);
      }
      
      return {
        history: filteredHistory,
        total: history.length,
        filtered: filteredHistory.length,
        platform
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error getting update history:', error);
      throw new Error(`获取热更新历史失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 检查资源变更 - 基于核心包功能实现
   */
  async checkResourceChanges(fromVersion: string, toVersion: string): Promise<any> {
    console.log('✅ [HotUpdateService] Checking resource changes:', fromVersion, '→', toVersion);

    try {
      // 尝试生成增量更新来获取变更信息
      const incrementalUpdate = await this.hotUpdateManager.generateIncrementalUpdate(
        fromVersion,
        toVersion
      );
      
      const changes = {
        added: incrementalUpdate.addedFiles.map(f => f.path),
        modified: incrementalUpdate.modifiedFiles.map(f => f.path),
        deleted: incrementalUpdate.deletedFiles
      };
      
      console.log('✅ [HotUpdateService] Resource changes checked:', changes);
      
      return {
        message: `资源变更检查完成: ${changes.added.length} 新增, ${changes.modified.length} 修改, ${changes.deleted.length} 删除`,
        changes,
        fromVersion,
        toVersion,
        summary: {
          addedCount: changes.added.length,
          modifiedCount: changes.modified.length,
          deletedCount: changes.deleted.length,
          totalChanges: changes.added.length + changes.modified.length + changes.deleted.length
        }
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error checking resource changes:', error);
      throw new Error(`检查资源变更失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 清理热更新缓存 - 模拟实现
   */
  async cleanCache(): Promise<any> {
    console.log('✅ [HotUpdateService] Cleaning hot update cache...');

    try {
      const fs = require('fs-extra');
      const path = require('path');
      
      // 清理临时文件和缓存
      const cacheDir = path.join(this.projectPath, 'dist', 'hotupdate-cache');
      const tempDir = path.join(this.projectPath, 'temp', 'hotupdate');
      
      let cleanedSize = 0;
      let cleanedFiles = 0;

      // 清理缓存目录
      if (await fs.pathExists(cacheDir)) {
        const stats = await this.getDirectoryStats(cacheDir);
        cleanedSize += stats.size;
        cleanedFiles += stats.files;
        await fs.remove(cacheDir);
      }

      // 清理临时目录
      if (await fs.pathExists(tempDir)) {
        const stats = await this.getDirectoryStats(tempDir);
        cleanedSize += stats.size;
        cleanedFiles += stats.files;
        await fs.remove(tempDir);
      }

      const cleanResult = {
        cleanedFiles,
        cleanedSize,
        cleanedSizeFormatted: this.formatSize(cleanedSize)
      };
      
      console.log('✅ [HotUpdateService] Cache cleaned successfully:', cleanResult);
      
      return {
        message: `热更新缓存清理成功: 清理了 ${cleanedFiles} 个文件，释放 ${cleanResult.cleanedSizeFormatted} 空间`,
        result: cleanResult
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error cleaning cache:', error);
      throw new Error(`清理缓存失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // 辅助方法
  private async getDirectoryStats(dirPath: string): Promise<{ files: number; size: number }> {
    const fs = require('fs-extra');
    const path = require('path');
    
    let files = 0;
    let size = 0;
    
    try {
      const items = await fs.readdir(dirPath);
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stats = await fs.stat(itemPath);
        
        if (stats.isDirectory()) {
          const subStats = await this.getDirectoryStats(itemPath);
          files += subStats.files;
          size += subStats.size;
        } else {
          files++;
          size += stats.size;
        }
      }
    } catch {
      // 忽略错误
    }
    
    return { files, size };
  }

  private formatSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取热更新配置 - 模拟实现
   */
  async getHotUpdateConfig(): Promise<any> {
    console.log('✅ [HotUpdateService] Getting hot update config...');

    try {
      // 模拟配置，实际项目中从配置文件读取
      const config = {
        enabled: true,
        baseUrl: 'https://cdn.example.com/hotupdate',
        checkInterval: 300000, // 5分钟
        retryCount: 3,
        timeout: 30000, // 30秒
        platforms: ['web-mobile', 'android', 'ios'],
        compression: true,
        encryption: false
      };

      console.log('✅ [HotUpdateService] Got hot update config:', config);

      return config;

    } catch (error) {
      console.error('❌ [HotUpdateService] Error getting hot update config:', error);
      throw new Error(`获取热更新配置失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 设置热更新配置 - 模拟实现
   */
  async setHotUpdateConfig(config: any): Promise<any> {
    console.log('✅ [HotUpdateService] Setting hot update config:', config);

    try {
      // 模拟保存配置，实际项目中写入配置文件
      console.log('✅ [HotUpdateService] Hot update config set successfully');

      return {
        message: '热更新配置设置成功',
        config
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error setting hot update config:', error);
      throw new Error(`设置热更新配置失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取支持的平台列表
   */
  getSupportedPlatforms(): string[] {
    return ['web-mobile', 'android', 'ios'];
  }

  /**
   * 检查平台是否支持热更新
   */
  isPlatformSupported(platform: string): boolean {
    return this.getSupportedPlatforms().includes(platform);
  }

  /**
   * 获取热更新统计信息
   */
  async getHotUpdateStats(): Promise<any> {
    console.log('✅ [HotUpdateService] Getting hot update stats...');

    try {
      const history = await this.getUpdateHistory();

      // 统计分析
      const stats = {
        total: history.history.length,
        successful: history.history.filter((record: any) => record.success).length,
        failed: history.history.filter((record: any) => !record.success).length,
        byPlatform: {} as any,
        recent: history.history.slice(-10), // 最近10次更新
        totalSize: history.history.reduce((sum: number, record: any) => sum + (record.size || 0), 0)
      };

      // 按平台统计
      history.history.forEach((record: any) => {
        const platform = record.platform;
        if (!stats.byPlatform[platform]) {
          stats.byPlatform[platform] = { total: 0, successful: 0, failed: 0, totalSize: 0 };
        }
        stats.byPlatform[platform].total++;
        stats.byPlatform[platform].totalSize += record.size || 0;
        if (record.success) {
          stats.byPlatform[platform].successful++;
        } else {
          stats.byPlatform[platform].failed++;
        }
      });

      return stats;

    } catch (error) {
      console.error('❌ [HotUpdateService] Error getting hot update stats:', error);
      throw new Error(`获取热更新统计失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 预览更新包内容
   */
  async previewUpdate(updatePath: string): Promise<any> {
    console.log('✅ [HotUpdateService] Previewing update package:', updatePath);

    try {
      const fs = require('fs-extra');
      const path = require('path');

      if (!await fs.pathExists(updatePath)) {
        throw new Error(`更新包不存在: ${updatePath}`);
      }

      // 读取更新包信息（假设是JSON格式）
      let preview: any = {
        path: updatePath,
        files: [],
        size: 0,
        version: '',
        platform: '',
        timestamp: new Date().toISOString()
      };

      try {
        const stats = await fs.stat(updatePath);
        preview.size = stats.size;

        // 如果是JSON文件，尝试读取内容
        if (path.extname(updatePath) === '.json') {
          const content = await fs.readJSON(updatePath);
          preview = { ...preview, ...content };
        }
      } catch {
        // 忽略读取错误
      }

      return {
        message: '更新包预览成功',
        preview
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error previewing update:', error);
      throw new Error(`预览更新包失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
