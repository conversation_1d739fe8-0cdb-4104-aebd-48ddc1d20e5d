import { HotUpdateManager } from '@version-craft/core';

/**
 * 热更新管理服务
 * 负责所有热更新相关的操作
 */
export class HotUpdateService {
  private hotUpdateManager: HotUpdateManager;
  private projectPath: string;

  constructor(projectPath: string) {
    this.projectPath = projectPath;
    // 设置工作目录并初始化热更新管理器
    process.chdir(projectPath);
    this.hotUpdateManager = new HotUpdateManager();
  }

  /**
   * 生成热更新资源清单 - 使用核心包
   */
  async generateManifest(options: {
    version?: string;
    platform?: string;
    outputPath?: string;
  } = {}): Promise<any> {
    console.log('✅ [HotUpdateService] Generating manifest using core package:', options);

    try {
      const manifest = await this.hotUpdateManager.generateManifest(
        options.version,
        options.platform,
        options.outputPath
      );
      
      console.log('✅ [HotUpdateService] Manifest generated successfully:', manifest);
      
      return {
        message: '热更新资源清单生成成功',
        manifest,
        version: options.version,
        platform: options.platform
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error generating manifest:', error);
      throw new Error(`生成资源清单失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 生成增量更新包 - 使用核心包
   */
  async generatePatch(options: {
    fromVersion: string;
    toVersion: string;
    platform?: string;
    outputPath?: string;
  }): Promise<any> {
    console.log('✅ [HotUpdateService] Generating patch using core package:', options);

    try {
      const patch = await this.hotUpdateManager.generateIncrementalUpdate(
        options.fromVersion,
        options.toVersion,
        options.platform,
        options.outputPath
      );
      
      console.log('✅ [HotUpdateService] Patch generated successfully:', patch);
      
      return {
        message: `增量更新包生成成功: ${options.fromVersion} → ${options.toVersion}`,
        patch,
        fromVersion: options.fromVersion,
        toVersion: options.toVersion,
        platform: options.platform
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error generating patch:', error);
      throw new Error(`生成增量更新包失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 验证资源清单完整性 - 使用核心包
   */
  async verifyManifest(manifestPath: string): Promise<any> {
    console.log('✅ [HotUpdateService] Verifying manifest using core package:', manifestPath);

    try {
      const verification = await this.hotUpdateManager.verifyManifest(manifestPath);
      
      console.log('✅ [HotUpdateService] Manifest verification completed:', verification);
      
      return {
        message: verification.valid ? '资源清单验证通过' : '资源清单验证失败',
        verification,
        manifestPath
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error verifying manifest:', error);
      throw new Error(`验证资源清单失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取热更新历史记录 - 使用核心包
   */
  async getUpdateHistory(platform?: string): Promise<any> {
    console.log('✅ [HotUpdateService] Getting update history using core package...');

    try {
      const history = await this.hotUpdateManager.getUpdateHistory();
      
      console.log('✅ [HotUpdateService] Got update history:', history.length, 'records');
      
      // 根据平台过滤
      let filteredHistory = history;
      if (platform) {
        filteredHistory = history.filter(record => record.platform === platform);
      }
      
      return {
        history: filteredHistory,
        total: history.length,
        filtered: filteredHistory.length,
        platform
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error getting update history:', error);
      throw new Error(`获取热更新历史失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 检查资源变更 - 使用核心包
   */
  async checkResourceChanges(fromVersion: string, toVersion: string): Promise<any> {
    console.log('✅ [HotUpdateService] Checking resource changes:', fromVersion, '→', toVersion);

    try {
      const changes = await this.hotUpdateManager.checkResourceChanges(fromVersion, toVersion);
      
      console.log('✅ [HotUpdateService] Resource changes checked:', changes);
      
      return {
        message: `资源变更检查完成: ${changes.added.length} 新增, ${changes.modified.length} 修改, ${changes.deleted.length} 删除`,
        changes,
        fromVersion,
        toVersion
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error checking resource changes:', error);
      throw new Error(`检查资源变更失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 清理热更新缓存 - 使用核心包
   */
  async cleanCache(): Promise<any> {
    console.log('✅ [HotUpdateService] Cleaning cache using core package...');

    try {
      const cleanResult = await this.hotUpdateManager.cleanCache();
      
      console.log('✅ [HotUpdateService] Cache cleaned successfully:', cleanResult);
      
      return {
        message: '热更新缓存清理成功',
        result: cleanResult
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error cleaning cache:', error);
      throw new Error(`清理缓存失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取热更新配置 - 使用核心包
   */
  async getHotUpdateConfig(): Promise<any> {
    console.log('✅ [HotUpdateService] Getting hot update config...');

    try {
      const config = await this.hotUpdateManager.getConfig();
      
      console.log('✅ [HotUpdateService] Got hot update config:', config);
      
      return config;

    } catch (error) {
      console.error('❌ [HotUpdateService] Error getting hot update config:', error);
      throw new Error(`获取热更新配置失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 设置热更新配置 - 使用核心包
   */
  async setHotUpdateConfig(config: any): Promise<any> {
    console.log('✅ [HotUpdateService] Setting hot update config:', config);

    try {
      await this.hotUpdateManager.setConfig(config);
      
      console.log('✅ [HotUpdateService] Hot update config set successfully');
      
      return {
        message: '热更新配置设置成功',
        config
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error setting hot update config:', error);
      throw new Error(`设置热更新配置失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取支持的平台列表
   */
  getSupportedPlatforms(): string[] {
    return ['web-mobile', 'android', 'ios'];
  }

  /**
   * 检查平台是否支持热更新
   */
  isPlatformSupported(platform: string): boolean {
    return this.getSupportedPlatforms().includes(platform);
  }

  /**
   * 获取热更新统计信息
   */
  async getHotUpdateStats(): Promise<any> {
    console.log('✅ [HotUpdateService] Getting hot update stats...');

    try {
      const history = await this.hotUpdateManager.getUpdateHistory();
      
      // 统计分析
      const stats = {
        total: history.length,
        successful: history.filter(record => record.success).length,
        failed: history.filter(record => !record.success).length,
        byPlatform: {},
        recent: history.slice(-10), // 最近10次更新
        totalSize: history.reduce((sum, record) => sum + (record.size || 0), 0)
      };

      // 按平台统计
      history.forEach(record => {
        const platform = record.platform;
        if (!stats.byPlatform[platform]) {
          stats.byPlatform[platform] = { total: 0, successful: 0, failed: 0, totalSize: 0 };
        }
        stats.byPlatform[platform].total++;
        stats.byPlatform[platform].totalSize += record.size || 0;
        if (record.success) {
          stats.byPlatform[platform].successful++;
        } else {
          stats.byPlatform[platform].failed++;
        }
      });

      return stats;

    } catch (error) {
      console.error('❌ [HotUpdateService] Error getting hot update stats:', error);
      throw new Error(`获取热更新统计失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 预览更新包内容
   */
  async previewUpdate(updatePath: string): Promise<any> {
    console.log('✅ [HotUpdateService] Previewing update package:', updatePath);

    try {
      // 这里可以扩展为实际的更新包预览功能
      const preview = {
        path: updatePath,
        files: [], // 文件列表
        size: 0,   // 包大小
        version: '', // 版本信息
        platform: '', // 平台信息
        timestamp: new Date().toISOString()
      };

      return {
        message: '更新包预览成功',
        preview
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error previewing update:', error);
      throw new Error(`预览更新包失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 为资源文件添加版本标记 - 对应 CLI 的 hotupdate-tag
   */
  async tagResources(options: {
    format?: 'suffix' | 'query' | 'header';
    version?: string;
  } = {}): Promise<any> {
    console.log('✅ [HotUpdateService] Tagging resources using core package:', options);

    try {
      const tagResult = await this.hotUpdateManager.tagResources(options.format, options.version);

      console.log('✅ [HotUpdateService] Resources tagged successfully:', tagResult);

      return {
        message: `资源版本标记成功: ${options.format || 'suffix'} 格式`,
        tagResult,
        format: options.format,
        version: options.version
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error tagging resources:', error);
      throw new Error(`资源版本标记失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 完整的热更新发布流程 - 对应 CLI 的 hotupdate-release
   */
  async releaseHotUpdate(options: {
    version?: string;
    platform?: string;
    mandatory?: boolean;
    baseUrl?: string;
  } = {}): Promise<any> {
    console.log('✅ [HotUpdateService] Starting hot update release process:', options);

    try {
      const releaseResult = await this.hotUpdateManager.releaseHotUpdate(options);

      console.log('✅ [HotUpdateService] Hot update release completed:', releaseResult);

      return {
        message: '热更新发布流程完成',
        releaseResult,
        version: options.version,
        platform: options.platform,
        mandatory: options.mandatory
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error in hot update release:', error);
      throw new Error(`热更新发布失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 清理旧版本资源 - 对应 CLI 的 hotupdate-clean
   */
  async cleanOldVersions(options: {
    keepVersions?: number;
    platform?: string;
  } = {}): Promise<any> {
    console.log('✅ [HotUpdateService] Cleaning old versions:', options);

    try {
      const cleanResult = await this.hotUpdateManager.cleanOldVersions(
        options.keepVersions || 5,
        options.platform
      );

      console.log('✅ [HotUpdateService] Old versions cleaned:', cleanResult);

      return {
        message: `旧版本清理完成: 保留 ${options.keepVersions || 5} 个版本`,
        cleanResult,
        keepVersions: options.keepVersions,
        platform: options.platform
      };

    } catch (error) {
      console.error('❌ [HotUpdateService] Error cleaning old versions:', error);
      throw new Error(`清理旧版本失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
