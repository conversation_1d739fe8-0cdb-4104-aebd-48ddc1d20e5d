import { BuildManager } from '@version-craft/core';
import { ChildProcess } from 'child_process';

/**
 * 构建管理服务
 * 负责所有构建相关的操作
 */
export class BuildService {
  private buildManager: BuildManager;
  private projectPath: string;
  private runningProcesses: Map<string, ChildProcess> = new Map();

  constructor(projectPath: string) {
    this.projectPath = projectPath;
    // 设置工作目录并初始化构建管理器
    process.chdir(projectPath);
    this.buildManager = new BuildManager();
  }

  /**
   * 开始构建 - 使用核心包
   */
  async startBuild(platform: string, options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Starting build using core package for platform:', platform);

    const buildId = `build_${Date.now()}`;

    try {
      // 使用核心包进行构建
      const buildResult = await this.buildManager.buildPlatform(platform);
      
      console.log('✅ [BuildService] Build completed:', buildResult);
      
      return {
        buildId,
        message: buildResult.success ? 'Build completed successfully' : 'Build failed',
        platform,
        startTime: new Date().toISOString(),
        result: buildResult
      };

    } catch (error) {
      console.error('❌ [BuildService] Build error:', error);
      
      return {
        buildId,
        message: 'Build failed',
        platform,
        startTime: new Date().toISOString(),
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 构建所有平台 - 使用核心包
   */
  async buildAllPlatforms(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building all platforms using core package...');

    try {
      const buildResults = await this.buildManager.buildAllPlatforms();
      console.log('✅ [BuildService] All platforms build completed:', buildResults);
      
      const successfulBuilds = buildResults.filter(result => result.success);
      
      return {
        message: `构建完成: ${successfulBuilds.length}/${buildResults.length} 成功`,
        results: buildResults,
        summary: {
          total: buildResults.length,
          successful: successfulBuilds.length,
          failed: buildResults.length - successfulBuilds.length
        }
      };

    } catch (error) {
      console.error('❌ [BuildService] Error building all platforms:', error);
      throw new Error(`构建所有平台失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取构建统计信息 - 使用核心包
   */
  async getBuildStats(): Promise<any> {
    console.log('✅ [BuildService] Getting build stats using core package...');

    try {
      const buildStats = await this.buildManager.getBuildStats();
      console.log('✅ [BuildService] Got build stats:', buildStats);
      return buildStats;
    } catch (error) {
      console.error('❌ [BuildService] Error getting build stats:', error);
      throw new Error(`获取构建统计失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 清理构建输出 - 使用核心包
   */
  async cleanBuild(): Promise<any> {
    console.log('✅ [BuildService] Cleaning build using core package...');

    try {
      const cleanResult = await this.buildManager.cleanBuild();
      console.log('✅ [BuildService] Build cleaned:', cleanResult);
      return { 
        message: '构建输出清理成功', 
        result: cleanResult 
      };
    } catch (error) {
      console.error('❌ [BuildService] Error cleaning build:', error);
      throw new Error(`清理构建失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 构建特定平台（Web Mobile）- 使用核心包
   */
  async buildWeb(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building web platform using core package...');
    return this.startBuild('web-mobile', options);
  }

  /**
   * 构建特定平台（Android）- 使用核心包
   */
  async buildAndroid(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building android platform using core package...');
    return this.startBuild('android', options);
  }

  /**
   * 构建特定平台（iOS）- 使用核心包
   */
  async buildIOS(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building ios platform using core package...');
    return this.startBuild('ios', options);
  }

  /**
   * 构建特定平台（Windows）- 使用核心包
   */
  async buildWindows(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building windows platform using core package...');
    return this.startBuild('windows', options);
  }

  /**
   * 构建特定平台（Mac）- 使用核心包
   */
  async buildMac(options: any = {}): Promise<any> {
    console.log('✅ [BuildService] Building mac platform using core package...');
    return this.startBuild('mac', options);
  }

  /**
   * 取消构建
   */
  cancelBuild(buildId: string): boolean {
    const process = this.runningProcesses.get(buildId);
    if (process) {
      process.kill('SIGTERM');
      this.runningProcesses.delete(buildId);
      return true;
    }
    return false;
  }

  /**
   * 获取支持的平台列表
   */
  getSupportedPlatforms(): string[] {
    return ['web-mobile', 'android', 'ios', 'windows', 'mac'];
  }

  /**
   * 检查平台是否支持
   */
  isPlatformSupported(platform: string): boolean {
    return this.getSupportedPlatforms().includes(platform);
  }
}
