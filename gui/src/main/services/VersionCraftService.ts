import { BrowserWindow } from 'electron';
// 导入各个功能服务
import { VersionService } from './version/VersionService';
import { BuildService } from './build/BuildService';
import { ConfigService } from './config/ConfigService';
import { DeployService } from './deploy/DeployService';
import { HotUpdateService } from './hotupdate/HotUpdateService';
import { RollbackService } from './rollback/RollbackService';

/**
 * Version-Craft 主服务协调器
 * 基于 @version-craft/core 核心包，提供完整的功能服务
 * 
 * 功能对应 CLI 命令分组：
 * - version: 版本管理 (current, bump, list, tag, changelog)
 * - build: 构建管理 (build-web, build-android, build-ios, build-win, build-mac)
 * - config: 配置管理 (init, show, set, validate)
 * - deploy: 部署管理 (deploy-staging, deploy-production, history, status)
 * - hotupdate: 热更新管理 (manifest, patch, verify)
 */
export class VersionCraftService {
  private projectPath: string;
  
  // 功能服务实例
  public readonly version: VersionService;
  public readonly build: BuildService;
  public readonly config: ConfigService;
  public readonly deploy: DeployService;
  public readonly hotupdate: HotUpdateService;
  public readonly rollback: RollbackService;

  constructor(projectPath: string) {
    this.projectPath = projectPath;
    
    // 初始化各个功能服务
    this.version = new VersionService(projectPath);
    this.build = new BuildService(projectPath);
    this.config = new ConfigService(projectPath);
    this.deploy = new DeployService(projectPath);
    this.hotupdate = new HotUpdateService(projectPath);
    this.rollback = new RollbackService(projectPath);
    
    console.log('🔧 [VersionCraftService] 服务协调器初始化完成');
    console.log('📁 [VersionCraftService] 项目路径:', this.projectPath);
    console.log('✅ [VersionCraftService] 所有功能服务已初始化');
  }

  // ==================== 通知方法 ====================

  /**
   * 通知版本变更
   */
  notifyVersionChanged(): void {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      window.webContents.send('version-changed');
    });
  }

  /**
   * 通知构建进度
   */
  notifyBuildProgress(buildId: string, data: any): void {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      window.webContents.send('build-progress', { buildId, data });
    });
  }

  /**
   * 通知构建完成
   */
  notifyBuildComplete(buildId: string, success: boolean, error?: string): void {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      window.webContents.send('build-complete', { buildId, success, error });
    });
  }

  /**
   * 通知部署状态变更
   */
  notifyDeploymentStatusChanged(environment: string, status: string): void {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      window.webContents.send('deployment-status-changed', { environment, status });
    });
  }

  /**
   * 通知热更新完成
   */
  notifyHotUpdateComplete(type: string, result: any): void {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      window.webContents.send('hotupdate-complete', { type, result });
    });
  }

  // ==================== 兼容性方法 ====================
  // 为了保持与现有 GUI 代码的兼容性，提供一些直接方法

  /**
   * 获取当前版本 - 兼容性方法
   */
  async getCurrentVersion(): Promise<any> {
    return this.version.getCurrentVersion();
  }

  /**
   * 获取版本历史 - 兼容性方法
   */
  async getVersionHistory(): Promise<any[]> {
    return this.version.getVersionHistory();
  }

  /**
   * 版本升级 - 兼容性方法
   */
  async bumpVersion(options: {
    type: 'major' | 'minor' | 'patch';
    prerelease?: string;
    message?: string;
  }): Promise<any> {
    const result = await this.version.bumpVersion(options);
    // 通知渲染进程版本已更改
    this.notifyVersionChanged();
    return result;
  }

  /**
   * 版本回滚 - 兼容性方法
   */
  async rollbackVersion(targetVersion: string, force: boolean = false): Promise<any> {
    const result = await this.version.rollbackVersion(targetVersion, force);
    // 通知渲染进程版本已更改
    this.notifyVersionChanged();
    return result;
  }

  /**
   * 开始构建 - 兼容性方法
   */
  async startBuild(platform: string, options: any = {}): Promise<any> {
    const result = await this.build.startBuild(platform, options);
    // 通知构建完成
    this.notifyBuildComplete(result.buildId, result.result?.success || false, result.error);
    return result;
  }

  // ==================== 兼容性方法 ====================
  // 为了保持与现有 GUI 代码的兼容性，提供一些直接方法

  /**
   * 获取当前版本 - 兼容性方法
   */
  async getCurrentVersion(): Promise<any> {
    return this.version.getCurrentVersion();
  }

  /**
   * 获取版本历史 - 兼容性方法
   */
  async getVersionHistory(): Promise<any[]> {
    return this.version.getVersionHistory();
  }

  /**
   * 版本升级 - 兼容性方法
   */
  async bumpVersion(options: {
    type: 'major' | 'minor' | 'patch';
    prerelease?: string;
    message?: string;
  }): Promise<any> {
    const result = await this.version.bumpVersion(options);
    // 通知渲染进程版本已更改
    this.notifyVersionChanged();
    return result;
  }

  /**
   * 版本回滚 - 兼容性方法
   */
  async rollbackVersion(targetVersion: string, force: boolean = false): Promise<any> {
    const result = await this.version.rollbackVersion(targetVersion, force);
    // 通知渲染进程版本已更改
    this.notifyVersionChanged();
    return result;
  }

  /**
   * 开始构建 - 兼容性方法
   */
  async startBuild(platform: string, options: any = {}): Promise<any> {
    const result = await this.build.startBuild(platform, options);
    // 通知构建完成
    this.notifyBuildComplete(result.buildId, result.result?.success || false, result.error);
    return result;
  }

  // ==================== 工具方法 ====================

  /**
   * 获取项目路径
   */
  getProjectPath(): string {
    return this.projectPath;
  }

  /**
   * 健康检查 - 检查所有服务状态
   */
  async healthCheck(): Promise<{
    version: boolean;
    build: boolean;
    config: boolean;
    deploy: boolean;
    hotupdate: boolean;
    rollback: boolean;
    overall: boolean;
  }> {
    const health = {
      version: false,
      build: false,
      config: false,
      deploy: false,
      hotupdate: false,
      rollback: false,
      overall: false
    };

    try {
      await this.version.getCurrentVersion();
      health.version = true;
    } catch {}

    try {
      await this.build.getBuildStats();
      health.build = true;
    } catch {}

    try {
      await this.config.getConfig();
      health.config = true;
    } catch {}

    try {
      await this.deploy.getDeploymentStatus();
      health.deploy = true;
    } catch {}

    try {
      await this.hotupdate.getHotUpdateConfig();
      health.hotupdate = true;
    } catch {}

    try {
      await this.rollback.getRollbackStatus();
      health.rollback = true;
    } catch {}

    // 整体健康状态：至少版本和配置服务正常
    health.overall = health.version && health.config;

    return health;
  }

  /**
   * 获取服务信息
   */
  getServiceInfo(): any {
    return {
      projectPath: this.projectPath,
      services: {
        version: 'VersionService - 版本管理 (current, bump, list, tag, changelog)',
        build: 'BuildService - 构建管理 (web, android, ios, windows, mac)',
        config: 'ConfigService - 配置管理 (init, show, set, validate)',
        deploy: 'DeployService - 部署管理 (staging, production, history, status)',
        hotupdate: 'HotUpdateService - 热更新管理 (manifest, patch, verify)',
        rollback: 'RollbackService - 回滚管理 (list, rollback-to, rollback-last, status)'
      },
      initialized: new Date().toISOString(),
      corePackage: '@version-craft/core'
    };
  }

  /**
   * 获取所有服务的统计信息
   */
  async getAllStats(): Promise<any> {
    const stats = {
      version: null,
      build: null,
      deploy: null,
      hotupdate: null,
      timestamp: new Date().toISOString()
    };

    try {
      stats.version = await this.version.getVersionHistory();
    } catch {}

    try {
      stats.build = await this.build.getBuildStats();
    } catch {}

    try {
      stats.deploy = await this.deploy.getDeploymentStats();
    } catch {}

    try {
      stats.hotupdate = await this.hotupdate.getHotUpdateStats();
    } catch {}

    return stats;
  }

  /**
   * 初始化项目 - 对应 CLI 的 init 命令
   */
  async initProject(): Promise<any> {
    console.log('🚀 [VersionCraftService] Initializing project...');

    try {
      // 初始化配置
      const configResult = await this.config.initConfig();
      
      // 通知初始化完成
      const windows = BrowserWindow.getAllWindows();
      windows.forEach(window => {
        window.webContents.send('project-initialized');
      });

      return {
        message: '项目初始化成功',
        config: configResult
      };

    } catch (error) {
      throw new Error(`项目初始化失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取项目状态概览
   */
  async getProjectOverview(): Promise<any> {
    try {
      const [currentVersion, buildStats, deployStatus, health] = await Promise.all([
        this.version.getCurrentVersion().catch(() => null),
        this.build.getBuildStats().catch(() => null),
        this.deploy.getDeploymentStatus().catch(() => null),
        this.healthCheck()
      ]);

      return {
        version: currentVersion,
        build: buildStats,
        deployment: deployStatus,
        health,
        projectPath: this.projectPath,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      throw new Error(`获取项目概览失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
