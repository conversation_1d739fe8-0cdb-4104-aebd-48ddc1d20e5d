import { BrowserWindow } from 'electron';
// 导入各个功能服务
import { VersionService } from './version/VersionService';
import { BuildService } from './build/BuildService';
import { ConfigService } from './config/ConfigService';
import { DeployService } from './deploy/DeployService';
import { HotUpdateService } from './hotupdate/HotUpdateService';

/**
 * Version-Craft 主服务协调器
 * 负责协调各个功能服务，提供统一的接口
 * 
 * 重构说明：
 * - 原来的单一服务类过于复杂，现在拆分为多个功能服务
 * - 每个服务专注于自己的领域：版本、构建、配置、部署
 * - 主服务作为协调器，提供统一的访问入口
 */
export class VersionCraftService {
  private projectPath: string;
  
  // 功能服务实例 - 公开访问，便于直接调用
  public readonly version: VersionService;
  public readonly build: BuildService;
  public readonly config: ConfigService;
  public readonly deploy: DeployService;

  constructor(projectPath: string) {
    this.projectPath = projectPath;
    
    // 初始化各个功能服务
    this.version = new VersionService(projectPath);
    this.build = new BuildService(projectPath);
    this.config = new ConfigService(projectPath);
    this.deploy = new DeployService(projectPath);
    
    console.log('🔧 [VersionCraftService] 服务协调器初始化完成');
    console.log('📁 [VersionCraftService] 项目路径:', this.projectPath);
    console.log('✅ [VersionCraftService] 所有功能服务已初始化');
  }

  // ==================== 兼容性方法 ====================
  // 为了保持与现有 GUI 代码的兼容性，保留一些原有的方法名
  // 这些方法直接委托给对应的功能服务

  /**
   * 获取当前版本 - 兼容性方法
   */
  async getCurrentVersion(): Promise<any> {
    return this.version.getCurrentVersion();
  }

  /**
   * 获取版本历史 - 兼容性方法
   */
  async getVersionHistory(): Promise<any[]> {
    return this.version.getVersionHistory();
  }

  /**
   * 版本升级 - 兼容性方法
   */
  async bumpVersion(options: {
    type: 'major' | 'minor' | 'patch';
    prerelease?: string;
    message?: string;
  }): Promise<any> {
    const result = await this.version.bumpVersion(options);
    // 通知渲染进程版本已更改
    this.notifyVersionChanged();
    return result;
  }

  /**
   * 版本回滚 - 兼容性方法
   */
  async rollbackVersion(targetVersion: string, force: boolean = false): Promise<any> {
    const result = await this.version.rollbackVersion(targetVersion, force);
    // 通知渲染进程版本已更改
    this.notifyVersionChanged();
    return result;
  }

  /**
   * 开始构建 - 兼容性方法
   */
  async startBuild(platform: string, options: any = {}): Promise<any> {
    const result = await this.build.startBuild(platform, options);
    // 通知构建完成
    this.notifyBuildComplete(result.buildId, result.result?.success || false, result.error);
    return result;
  }

  // ==================== 通知方法 ====================
  // 这些方法用于向渲染进程发送通知

  /**
   * 通知版本变更
   */
  private notifyVersionChanged(): void {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      window.webContents.send('version-changed');
    });
  }

  /**
   * 通知构建进度
   */
  private notifyBuildProgress(buildId: string, data: any): void {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      window.webContents.send('build-progress', { buildId, data });
    });
  }

  /**
   * 通知构建完成
   */
  private notifyBuildComplete(buildId: string, success: boolean, error?: string): void {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      window.webContents.send('build-complete', { buildId, success, error });
    });
  }

  // ==================== 工具方法 ====================

  /**
   * 获取项目路径
   */
  getProjectPath(): string {
    return this.projectPath;
  }

  /**
   * 健康检查 - 检查所有服务状态
   */
  async healthCheck(): Promise<{
    version: boolean;
    build: boolean;
    config: boolean;
    deploy: boolean;
  }> {
    const health = {
      version: false,
      build: false,
      config: false,
      deploy: false
    };

    try {
      await this.version.getCurrentVersion();
      health.version = true;
    } catch {}

    try {
      await this.build.getBuildStats();
      health.build = true;
    } catch {}

    try {
      await this.config.getConfig();
      health.config = true;
    } catch {}

    try {
      await this.deploy.getDeploymentStatus();
      health.deploy = true;
    } catch {}

    return health;
  }

  /**
   * 获取服务信息
   */
  getServiceInfo(): any {
    return {
      projectPath: this.projectPath,
      services: {
        version: 'VersionService - 版本管理',
        build: 'BuildService - 构建管理',
        config: 'ConfigService - 配置管理',
        deploy: 'DeployService - 部署管理'
      },
      initialized: new Date().toISOString()
    };
  }
}
