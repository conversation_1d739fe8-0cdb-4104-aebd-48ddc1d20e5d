import { ConfigManager } from '@version-craft/core';

/**
 * 配置管理服务
 * 负责所有配置相关的操作
 */
export class ConfigService {
  private configManager: ConfigManager;
  private projectPath: string;

  constructor(projectPath: string) {
    this.projectPath = projectPath;
    // 设置工作目录并初始化配置管理器
    process.chdir(projectPath);
    this.configManager = new ConfigManager();
  }

  /**
   * 获取当前配置 - 使用核心包
   */
  async getConfig(environment?: string): Promise<any> {
    console.log('✅ [ConfigService] Getting config using core package...');

    try {
      const config = await this.configManager.loadConfig();
      console.log('✅ [ConfigService] Got config:', config);
      return config;
    } catch (error) {
      console.error('❌ [ConfigService] Error getting config:', error);
      throw new Error(`获取配置失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 设置配置项 - 使用核心包
   */
  async setConfig(key: string, value: any): Promise<any> {
    console.log('✅ [ConfigService] Setting config using core package:', key, value);

    try {
      await this.configManager.setConfig(key, value);
      console.log('✅ [ConfigService] Config set successfully');
      return { 
        message: `配置项 ${key} 设置成功`,
        key,
        value
      };
    } catch (error) {
      console.error('❌ [ConfigService] Error setting config:', error);
      throw new Error(`设置配置失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 验证配置文件 - 使用核心包
   */
  async validateConfig(): Promise<any> {
    console.log('✅ [ConfigService] Validating config using core package...');

    try {
      const validation = await this.configManager.validateConfig();
      console.log('✅ [ConfigService] Config validation result:', validation);
      return validation;
    } catch (error) {
      console.error('❌ [ConfigService] Error validating config:', error);
      throw new Error(`验证配置失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 初始化配置文件 - 使用核心包
   */
  async initConfig(): Promise<any> {
    console.log('✅ [ConfigService] Initializing config using core package...');

    try {
      await this.configManager.initConfig();
      console.log('✅ [ConfigService] Config initialized successfully');
      return { 
        message: '配置文件初始化成功' 
      };
    } catch (error) {
      console.error('❌ [ConfigService] Error initializing config:', error);
      throw new Error(`初始化配置失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 检查配置文件是否存在 - 使用核心包
   */
  async configExists(): Promise<boolean> {
    console.log('✅ [ConfigService] Checking if config exists using core package...');

    try {
      const exists = await this.configManager.configExists();
      console.log('✅ [ConfigService] Config exists:', exists);
      return exists;
    } catch (error) {
      console.error('❌ [ConfigService] Error checking config existence:', error);
      return false;
    }
  }

  /**
   * 切换环境配置
   */
  async switchEnvironment(environment: string): Promise<any> {
    console.log('✅ [ConfigService] Switching environment:', environment);

    try {
      // 这里可以扩展为从核心包获取环境切换功能
      await this.setConfig('currentEnvironment', environment);
      
      return { 
        message: `已切换到 ${environment} 环境`,
        environment
      };
    } catch (error) {
      console.error('❌ [ConfigService] Error switching environment:', error);
      throw new Error(`切换环境失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 导出配置文件
   */
  async exportConfig(filePath?: string): Promise<any> {
    console.log('✅ [ConfigService] Exporting config...');

    try {
      const config = await this.configManager.loadConfig();
      
      // 如果没有指定文件路径，返回配置内容
      if (!filePath) {
        return {
          message: '配置导出成功',
          config
        };
      }

      // 这里可以扩展为实际的文件导出功能
      return {
        message: `配置已导出到 ${filePath}`,
        filePath,
        config
      };
    } catch (error) {
      console.error('❌ [ConfigService] Error exporting config:', error);
      throw new Error(`导出配置失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 导入配置文件
   */
  async importConfig(filePath: string): Promise<any> {
    console.log('✅ [ConfigService] Importing config from:', filePath);

    try {
      // 这里可以扩展为实际的文件导入功能
      // 目前返回模拟结果
      return {
        message: `配置已从 ${filePath} 导入成功`,
        filePath
      };
    } catch (error) {
      console.error('❌ [ConfigService] Error importing config:', error);
      throw new Error(`导入配置失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取支持的环境列表
   */
  getSupportedEnvironments(): string[] {
    return ['development', 'staging', 'production'];
  }

  /**
   * 获取当前环境
   */
  async getCurrentEnvironment(): Promise<string> {
    try {
      const config = await this.configManager.loadConfig();
      return config.currentEnvironment || 'development';
    } catch {
      return 'development';
    }
  }
}
