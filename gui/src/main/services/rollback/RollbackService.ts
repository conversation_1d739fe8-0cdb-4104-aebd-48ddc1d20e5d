import { VersionManager, DeployManager } from '@version-craft/core';

/**
 * 回滚管理服务
 * 负责所有回滚相关的操作，对应 CLI 的回滚管理功能
 */
export class RollbackService {
  private versionManager: VersionManager;
  private deployManager: DeployManager;
  private projectPath: string;

  constructor(projectPath: string) {
    this.projectPath = projectPath;
    // 设置工作目录并初始化管理器
    process.chdir(projectPath);
    this.versionManager = new VersionManager();
    this.deployManager = new DeployManager();
  }

  /**
   * 列出可回滚的版本 - 对应 CLI 的 rollback-list
   */
  async listRollbackVersions(options: {
    limit?: number;
    platform?: string;
    environment?: string;
  } = {}): Promise<any> {
    console.log('✅ [RollbackService] Listing rollback versions using core package:', options);

    try {
      const versions = await this.versionManager.getRollbackVersions(
        options.limit || 10,
        options.platform,
        options.environment
      );
      
      console.log('✅ [RollbackService] Got rollback versions:', versions.length);
      
      return {
        message: `找到 ${versions.length} 个可回滚版本`,
        versions,
        limit: options.limit || 10,
        platform: options.platform,
        environment: options.environment
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error listing rollback versions:', error);
      throw new Error(`获取可回滚版本失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 回滚到指定版本 - 对应 CLI 的 rollback-to
   */
  async rollbackToVersion(options: {
    version: string;
    platform?: string;
    environment?: string;
    force?: boolean;
  }): Promise<any> {
    console.log('✅ [RollbackService] Rolling back to version using core package:', options);

    try {
      const rollbackResult = await this.versionManager.rollbackToVersion(
        options.version,
        options.force || false,
        options.platform,
        options.environment || 'staging'
      );
      
      console.log('✅ [RollbackService] Rollback completed:', rollbackResult);
      
      return {
        message: `回滚到版本 ${options.version} 成功`,
        rollbackResult,
        version: options.version,
        platform: options.platform,
        environment: options.environment || 'staging'
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error rolling back to version:', error);
      throw new Error(`回滚到版本失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 回滚到上一个版本 - 对应 CLI 的 rollback-last
   */
  async rollbackToLast(options: {
    platform?: string;
    environment?: string;
  } = {}): Promise<any> {
    console.log('✅ [RollbackService] Rolling back to last version using core package:', options);

    try {
      const rollbackResult = await this.versionManager.rollbackToLastVersion(
        options.platform,
        options.environment || 'staging'
      );
      
      console.log('✅ [RollbackService] Rollback to last version completed:', rollbackResult);
      
      return {
        message: `回滚到上一版本成功: ${rollbackResult.previousVersion}`,
        rollbackResult,
        platform: options.platform,
        environment: options.environment || 'staging'
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error rolling back to last version:', error);
      throw new Error(`回滚到上一版本失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 检查回滚状态 - 对应 CLI 的 rollback-status
   */
  async getRollbackStatus(): Promise<any> {
    console.log('✅ [RollbackService] Getting rollback status using core package...');

    try {
      const status = await this.versionManager.getRollbackStatus();
      
      console.log('✅ [RollbackService] Got rollback status:', status);
      
      return {
        message: '回滚状态获取成功',
        status
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error getting rollback status:', error);
      throw new Error(`获取回滚状态失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 创建回滚点 - 对应 CLI 的 rollback-checkpoint
   */
  async createCheckpoint(name?: string): Promise<any> {
    console.log('✅ [RollbackService] Creating rollback checkpoint using core package:', name);

    try {
      const checkpoint = await this.versionManager.createRollbackCheckpoint(name);
      
      console.log('✅ [RollbackService] Checkpoint created:', checkpoint);
      
      return {
        message: `回滚点创建成功: ${checkpoint.name}`,
        checkpoint
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error creating checkpoint:', error);
      throw new Error(`创建回滚点失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取回滚历史记录
   */
  async getRollbackHistory(options: {
    limit?: number;
    platform?: string;
    environment?: string;
  } = {}): Promise<any> {
    console.log('✅ [RollbackService] Getting rollback history using core package:', options);

    try {
      const history = await this.versionManager.getRollbackHistory(
        options.limit || 20,
        options.platform,
        options.environment
      );
      
      console.log('✅ [RollbackService] Got rollback history:', history.length, 'records');
      
      return {
        message: `获取到 ${history.length} 条回滚历史记录`,
        history,
        limit: options.limit || 20,
        platform: options.platform,
        environment: options.environment
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error getting rollback history:', error);
      throw new Error(`获取回滚历史失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 验证回滚可行性
   */
  async validateRollback(options: {
    version: string;
    platform?: string;
    environment?: string;
  }): Promise<any> {
    console.log('✅ [RollbackService] Validating rollback feasibility:', options);

    try {
      const validation = await this.versionManager.validateRollback(
        options.version,
        options.platform,
        options.environment
      );
      
      console.log('✅ [RollbackService] Rollback validation result:', validation);
      
      return {
        message: validation.valid ? '回滚验证通过' : '回滚验证失败',
        validation,
        version: options.version,
        platform: options.platform,
        environment: options.environment
      };

    } catch (error) {
      console.error('❌ [RollbackService] Error validating rollback:', error);
      throw new Error(`回滚验证失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取支持的环境列表
   */
  getSupportedEnvironments(): string[] {
    return ['staging', 'production'];
  }

  /**
   * 获取支持的平台列表
   */
  getSupportedPlatforms(): string[] {
    return ['web-mobile', 'android', 'ios', 'windows', 'mac'];
  }

  /**
   * 检查环境是否支持
   */
  isEnvironmentSupported(environment: string): boolean {
    return this.getSupportedEnvironments().includes(environment);
  }

  /**
   * 检查平台是否支持
   */
  isPlatformSupported(platform: string): boolean {
    return this.getSupportedPlatforms().includes(platform);
  }
}
