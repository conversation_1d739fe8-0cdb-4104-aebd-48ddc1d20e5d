# ⚡ 快速开始改造指南

## 🎯 **立即执行步骤**

### **步骤1: 创建独立工具目录**
```bash
# 1. 在合适位置创建独立目录
mkdir D:/Desktop/version-craft-standalone

# 2. 复制所有工具文件
cp -r D:/Desktop/LuckyCoin/version-craft/* D:/Desktop/version-craft-standalone/

# 3. 进入独立目录
cd D:/Desktop/version-craft-standalone
```

### **步骤2: 创建ProjectDetector工具类**
创建文件 `src/utils/ProjectDetector.ts`:
```typescript
import * as fs from 'fs-extra';
import * as path from 'path';

export class ProjectDetector {
  static findProjectRoot(startDir?: string): string {
    let currentDir = startDir || process.cwd();
    
    while (currentDir !== path.parse(currentDir).root) {
      const configPath = path.join(currentDir, 'version-craft.config.json');
      if (fs.existsSync(configPath)) {
        console.log(`找到项目根目录: ${currentDir}`);
        return currentDir;
      }
      currentDir = path.dirname(currentDir);
    }
    
    throw new Error(`未找到 version-craft.config.json\n请在包含配置文件的项目目录中执行命令`);
  }

  static validateProjectRoot(projectRoot: string): boolean {
    const configPath = path.join(projectRoot, 'version-craft.config.json');
    return fs.existsSync(configPath);
  }
}
```

### **步骤3: 修改所有Manager类构造函数**
在以下文件中添加 `projectRoot` 参数：

**ConfigManager.ts**:
```typescript
// 在构造函数中添加
constructor(projectRoot?: string) {
  this.projectRoot = projectRoot || ProjectDetector.findProjectRoot();
  this.configPath = path.join(this.projectRoot, 'version-craft.config.json');
  // ... 其他初始化代码
}
```

**VersionManager.ts**:
```typescript
// 在构造函数中添加
constructor(projectRoot?: string) {
  this.projectRoot = projectRoot || ProjectDetector.findProjectRoot();
  this.git = simpleGit(this.projectRoot);
  this.configManager = new ConfigManager(this.projectRoot);
  // ... 其他初始化代码
}
```

**类似地修改**:
- BuildManager.ts
- DeployManager.ts  
- HotUpdateManager.ts

### **步骤4: 修改所有Command类构造函数**
在以下文件中添加 `projectRoot` 参数：

**RollbackCommand.ts** (最重要):
```typescript
constructor(projectRoot?: string) {
  this.projectRoot = projectRoot || ProjectDetector.findProjectRoot();
  this.git = simpleGit(this.projectRoot); // ✅ 关键修复
  
  // 传递projectRoot给所有Manager
  this.versionManager = new VersionManager(this.projectRoot);
  this.buildManager = new BuildManager(this.projectRoot);
  this.deployManager = new DeployManager(this.projectRoot);
  this.configManager = new ConfigManager(this.projectRoot);
}
```

**类似地修改**:
- VersionCommand.ts
- BuildCommand.ts
- DeployCommand.ts
- ConfigCommand.ts
- HotUpdateCommand.ts

### **步骤5: 修改CLI入口**
在 `src/cli.ts` 中添加项目路径支持：
```typescript
program
  .version(packageJson.version)
  .option('-p, --project <path>', '指定项目路径')
  .hook('preAction', (thisCommand) => {
    const projectPath = thisCommand.opts().project;
    if (projectPath) {
      process.env.GAME_VERSION_PROJECT_ROOT = path.resolve(projectPath);
    }
  });

// 在所有命令中传递projectRoot
const projectRoot = process.env.GAME_VERSION_PROJECT_ROOT;

program
  .command('bump [type]')
  .action(async (type, options) => {
    const versionCommand = new VersionCommand(projectRoot);
    await versionCommand.bumpVersion(type, options);
  });

// 类似地修改所有其他命令...
```

### **步骤6: 修改package.json**
```json
{
  "name": "version-craft",
  "version": "1.0.0",
  "bin": {
    "version-craft": "./dist/cli.js"
  },
  "preferGlobal": true,
  "files": [
    "dist/**/*"
  ]
}
```

### **步骤7: 构建和测试**
```bash
# 构建
npm run build

# 全局安装
npm install -g .

# 测试 (在LuckyCoin项目中)
cd D:/Desktop/LuckyCoin
version-craft current

# 测试回滚 (现在应该安全了)
version-craft rollback-to 0.1.4
```

## ⚠️ **关键检查点**

改造完成后，必须验证：
- [ ] 工具可以全局安装
- [ ] 在LuckyCoin项目中可以正常使用
- [ ] 回滚操作不会影响工具代码
- [ ] 所有Git操作指向目标项目

## 🆘 **如果遇到问题**

1. **找不到配置文件**: 确保在包含 `version-craft.config.json` 的目录中执行
2. **Git操作失败**: 检查 `simpleGit(this.projectRoot)` 是否正确设置
3. **编译错误**: 确保所有导入的 `ProjectDetector` 路径正确

---

**这个改造解决了工具"自毁"的致命问题，是当前最高优先级的任务！**
