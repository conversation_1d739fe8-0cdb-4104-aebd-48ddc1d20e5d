# 🔥 热更新功能使用指南

## 📋 功能概述

`version-craft` 现已支持完整的热更新功能，包括：

- ✅ **资源清单生成** - 为热更新生成详细的资源清单
- ✅ **资源版本标记** - 为资源文件添加版本标记和校验和
- ✅ **增量更新包** - 生成增量更新包，减少下载量
- ✅ **完整工作流** - 一键式热更新发布流程

## 🚀 快速开始

### 1. 生成资源清单

```bash
# 为当前版本生成资源清单
version-craft hotupdate-manifest

# 指定版本和输出路径
version-craft hotupdate-manifest -v 1.2.0 -o dist/manifest_1.2.0.json

# 设置资源基础URL和强制更新
version-craft hotupdate-manifest --base-url https://cdn.luckycoin.com --mandatory
```

**生成的清单文件包含：**
- 所有资源文件的路径、大小、校验和
- 资源优先级和类型分类
- 版本信息和更新描述
- 下载URL和包信息

### 2. 生成增量更新包

```bash
# 生成从1.1.0到1.2.0的增量更新包
version-craft hotupdate-patch 1.1.0 1.2.0

# 指定输出目录和基础URL
version-craft hotupdate-patch 1.1.0 1.2.0 -o dist/patches --base-url https://cdn.luckycoin.com
```

**增量更新包包含：**
- 新增、修改、删除的文件列表
- 文件校验和和大小信息
- 更新类型（patch/full）判断
- 压缩的更新包文件

### 3. 添加版本标记

```bash
# 为资源文件添加版本后缀
version-craft hotupdate-tag -v 1.2.0 --format suffix

# 使用查询参数格式
version-craft hotupdate-tag --format query -o dist/versioned

# 指定特定文件
version-craft hotupdate-tag --files "assets/**/*.png" "assets/**/*.json"
```

**支持的标记格式：**
- `suffix`: `button.png` → `button.1.2.0.png`
- `query`: `button.png` → `button.png?v=1.2.0`
- `header`: 在HTTP头中添加版本信息

### 4. 完整发布流程

```bash
# 一键式热更新发布
version-craft hotupdate-release

# 指定起始版本和基础URL
version-craft hotupdate-release --from-version 1.1.0 --base-url https://cdn.luckycoin.com

# 跳过构建步骤
version-craft hotupdate-release --skip-build
```

**发布流程包括：**
1. 版本号升级
2. 资源清单生成
3. 增量更新包创建
4. 版本标记添加
5. 部署准备

## 📊 资源清单格式

### 清单文件结构

```json
{
  "version": "1.2.0",
  "buildNumber": 1640995200000,
  "releaseDate": "2024-01-01T00:00:00.000Z",
  "description": "新增金币收集功能",
  "mandatory": false,
  "totalSize": 10485760,
  "resources": [
    {
      "path": "assets/textures/coin.png",
      "size": 2048,
      "checksum": "abc123def456",
      "version": "1.2.0",
      "priority": 3,
      "type": "texture",
      "compressed": false,
      "url": "https://cdn.luckycoin.com/assets/textures/coin.png?v=1.2.0"
    }
  ],
  "packageUrl": "https://cdn.luckycoin.com/packages/1.2.0.zip",
  "remoteManifestUrl": "https://cdn.luckycoin.com/manifest.json",
  "remoteVersionUrl": "https://cdn.luckycoin.com/version.json"
}
```

### 资源优先级

| 优先级 | 类型 | 说明 |
|--------|------|------|
| 1 | 配置文件 | game_config.json 等 |
| 2 | 脚本文件 | .js, .ts 文件 |
| 3 | UI资源 | UI相关的图片资源 |
| 5 | 其他资源 | 一般游戏资源 |
| 8 | 音频文件 | .mp3, .wav 等 |

## 🔧 高级用法

### 自定义资源扫描

```bash
# 只扫描特定目录
version-craft hotupdate-manifest --include "assets/ui/**/*" --include "src/configs/**/*"

# 排除特定文件
version-craft hotupdate-manifest --exclude "**/*.meta" --exclude "**/temp/**/*"
```

### 验证资源清单

```bash
# 验证清单文件的完整性
version-craft hotupdate-verify dist/manifest.json

# 比较两个版本的差异
version-craft hotupdate-diff 1.1.0 1.2.0 --detailed
```

### 清理旧版本

```bash
# 清理旧版本资源，保留最新5个版本
version-craft hotupdate-clean --keep 5

# 预览模式，不实际删除
version-craft hotupdate-clean --dry-run
```

## 🎯 与客户端集成

### 1. 客户端使用清单

客户端的 `VersionManager` 可以直接使用生成的清单：

```typescript
// 客户端代码示例
const manifest = await this.loadRemoteManifest();
if (manifest.version !== this.currentVersion) {
    await this.performHotUpdate(manifest);
}
```

### 2. 增量更新流程

```typescript
// 检查增量更新
const patch = await this.checkIncrementalUpdate(currentVersion, latestVersion);
if (patch && patch.updateType === 'patch') {
    // 下载增量包
    await this.downloadPatch(patch);
} else {
    // 下载完整包
    await this.downloadFullUpdate(manifest);
}
```

### 3. 资源加载

```typescript
// 使用版本化资源URL
const resourceUrl = `${baseUrl}/${resourcePath}?v=${version}&checksum=${checksum}`;
const resource = await this.loadResource(resourceUrl);
```

## 📈 最佳实践

### 1. 版本管理策略

- **补丁版本** - 用于bug修复和小功能更新
- **次版本** - 用于新功能添加
- **主版本** - 用于重大架构变更

### 2. 资源优化

- **压缩资源** - 对文本文件启用压缩
- **优先级设置** - 关键资源优先下载
- **增量更新** - 减少用户下载量

### 3. 部署策略

- **CDN部署** - 使用CDN加速资源下载
- **多环境** - 测试环境验证后再发布生产
- **回滚准备** - 保留旧版本以便快速回滚

## 🔍 故障排除

### 常见问题

**Q: 生成清单时提示文件不存在**
```bash
# 检查项目结构和文件路径
version-craft config-show
```

**Q: 增量更新包过大**
```bash
# 检查资源变更情况
version-craft hotupdate-diff 1.1.0 1.2.0 --detailed
```

**Q: 客户端校验失败**
```bash
# 验证清单完整性
version-craft hotupdate-verify dist/manifest.json
```

### 调试模式

```bash
# 启用详细日志
DEBUG=version-craft:* version-craft hotupdate-manifest

# 查看配置信息
version-craft config-show --env production
```

## 🚀 工作流示例

### 日常热更新发布

```bash
# 1. 开发完成后，升级版本并生成热更新
version-craft hotupdate-release

# 2. 部署到测试环境
version-craft deploy-staging

# 3. 测试通过后，部署到生产环境
version-craft deploy-production
```

### 紧急修复发布

```bash
# 1. 快速修复并生成补丁
version-craft bump patch
version-craft hotupdate-manifest --mandatory --description "紧急修复"

# 2. 生成增量包
version-craft hotupdate-patch 1.1.9 1.1.10

# 3. 立即部署
version-craft deploy-production --platform web-mobile
```

现在您的 `version-craft` 已经具备了完整的热更新功能，可以完美支持客户端的版本管理需求！🎉
