version-craft/
├── src/
│   ├── commands/          # CLI 命令实现
│   ├── modules/           # 核心功能模块
│   │   ├── version/       # 版本管理模块
│   │   ├── build/         # 构建管理模块
│   │   ├── deploy/        # 发布管理模块
│   │   └── config/        # 配置管理模块
│   ├── utils/             # 工具函数
│   ├── types/             # TypeScript 类型定义
│   ├── schemas/           # JSON Schema 定义
│   ├── cli.ts             # CLI 入口文件
│   └── index.ts           # API 入口文件
├── config/                # 默认配置文件
├── templates/             # 模板文件
├── test/                  # 测试文件
├── docs/                  # 文档
├── package.json
├── tsconfig.json
├── jest.config.js
├── .eslintrc.js
└── README.md