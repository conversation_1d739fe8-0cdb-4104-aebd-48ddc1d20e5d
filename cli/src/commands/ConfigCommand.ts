import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import ora from 'ora';
import * as fs from 'fs-extra';
import { ConfigManager } from '../modules/config/ConfigManager';

export class ConfigCommand {
  private configManager: ConfigManager;

  constructor() {
    this.configManager = new ConfigManager();
  }

  /**
   * 注册所有配置相关命令到主程序
   */
  registerCommands(program: Command): void {
    // 初始化配置 - 提升到顶级
    program
      .command('init')
      .description('初始化项目配置文件')
      .action(async () => {
        await this.initConfig();
      });

    // 显示配置
    program
      .command('config-show')
      .description('显示当前配置')
      .option('-e, --env <environment>', '显示指定环境配置')
      .action(async (options) => {
        await this.showConfig(options);
      });

    // 设置配置
    program
      .command('config-set <key> <value>')
      .description('设置配置项')
      .action(async (key: string, value: string) => {
        await this.setConfig(key, value);
      });

    // 环境配置管理
    program
      .command('config-env <environment>')
      .description('切换环境配置')
      .action(async (environment: string) => {
        await this.switchEnvironment(environment);
      });

    // 验证配置
    program
      .command('config-validate')
      .description('验证配置文件')
      .option('-e, --env <environment>', '验证指定环境配置')
      .action(async (options) => {
        await this.validateConfig(options);
      });

    // 导出配置
    program
      .command('config-export [file]')
      .description('导出配置文件')
      .action(async (file?: string) => {
        await this.exportConfig(file);
      });

    // 导入配置
    program
      .command('config-import <file>')
      .description('导入配置文件')
      .action(async (file: string) => {
        await this.importConfig(file);
      });
  }

  private async initConfig(): Promise<void> {
    try {
      console.log(chalk.blue('🔧 初始化配置文件...'));

      // 检查是否已存在配置文件
      if (await this.configManager.configExists()) {
        const { overwrite } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'overwrite',
            message: '配置文件已存在，是否覆盖？',
            default: false
          }
        ]);

        if (!overwrite) {
          console.log(chalk.yellow('初始化已取消'));
          return;
        }
      }

      const spinner = ora('创建默认配置...').start();
      await this.configManager.initConfig();
      spinner.succeed(chalk.green('配置文件初始化成功！'));

      console.log(chalk.gray('配置文件位置: version-craft.config.json'));
      console.log(chalk.gray('使用 "version-craft config-show" 查看配置'));

    } catch (error) {
      console.error(chalk.red('配置初始化失败:'), error);
    }
  }

  private async showConfig(options?: any): Promise<void> {
    try {
      const spinner = ora('读取配置...').start();
      const config = await this.configManager.loadConfig();
      spinner.stop();

      if (options?.env) {
        // 显示环境配置
        console.log(chalk.blue(`📋 环境配置 (${options.env}):`));
        
        if (config.environments[options.env]) {
          const envPath = config.environments[options.env];
          console.log(chalk.gray(`配置路径: ${envPath}`));
          
          const isValid = await this.configManager.validateEnvironmentConfig(options.env);
          console.log(chalk.gray(`配置状态: ${isValid ? '✓ 有效' : '✗ 无效'}`));
        } else {
          console.log(chalk.red(`环境 ${options.env} 不存在`));
        }
      } else {
        // 显示完整配置
        console.log(chalk.blue('📋 当前配置:'));
        console.log('');
        
        // 项目配置
        console.log(chalk.yellow('项目信息:'));
        console.log(chalk.gray(`  名称: ${config.project.name}`));
        console.log(chalk.gray(`  类型: ${config.project.type}`));
        console.log(chalk.gray(`  版本: ${config.project.version}`));
        console.log(chalk.gray(`  描述: ${config.project.description || '无'}`));
        console.log('');

        // 构建配置
        console.log(chalk.yellow('构建配置:'));
        console.log(chalk.gray(`  平台: ${config.build.platforms.join(', ')}`));
        console.log(chalk.gray(`  输出目录: ${config.build.outputDir}`));
        console.log(chalk.gray(`  压缩: ${config.build.optimization.compress ? '启用' : '禁用'}`));
        console.log(chalk.gray(`  混淆: ${config.build.optimization.minify ? '启用' : '禁用'}`));
        console.log('');

        // 部署配置
        console.log(chalk.yellow('部署配置:'));
        if (config.deploy.web) {
          console.log(chalk.gray(`  Web 测试: ${config.deploy.web.staging || '未配置'}`));
          console.log(chalk.gray(`  Web 生产: ${config.deploy.web.production || '未配置'}`));
        }
        console.log('');

        // 环境配置
        console.log(chalk.yellow('环境配置:'));
        Object.keys(config.environments).forEach(env => {
          console.log(chalk.gray(`  ${env}: ${config.environments[env]}`));
        });
        console.log('');

        // Git 配置
        console.log(chalk.yellow('Git 配置:'));
        console.log(chalk.gray(`  自动标签: ${config.git.autoTag ? '启用' : '禁用'}`));
        console.log(chalk.gray(`  标签前缀: ${config.git.tagPrefix}`));
        console.log(chalk.gray(`  变更日志: ${config.git.generateChangelog ? '启用' : '禁用'}`));
      }

    } catch (error) {
      console.error(chalk.red('读取配置失败:'), error);
    }
  }

  private async setConfig(key: string, value: string): Promise<void> {
    try {
      console.log(chalk.blue(`🔧 设置配置项: ${key} = ${value}`));

      const config = await this.configManager.loadConfig();
      
      // 解析嵌套键名 (如 project.name)
      const keys = key.split('.');
      let current: any = config;
      
      for (let i = 0; i < keys.length - 1; i++) {
        if (!(keys[i] in current)) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }

      // 转换值类型
      const finalKey = keys[keys.length - 1];
      let parsedValue: any = value;
      
      if (value === 'true') parsedValue = true;
      else if (value === 'false') parsedValue = false;
      else if (!isNaN(Number(value))) parsedValue = Number(value);
      else if (value.startsWith('[') && value.endsWith(']')) {
        try {
          parsedValue = JSON.parse(value);
        } catch {
          // 保持字符串值
        }
      }

      current[finalKey] = parsedValue;

      const spinner = ora('更新配置...').start();
      await this.configManager.updateConfig(config);
      spinner.succeed(chalk.green('配置更新成功'));

    } catch (error) {
      console.error(chalk.red('配置更新失败:'), error);
    }
  }

  private async switchEnvironment(environment: string): Promise<void> {
    try {
      const config = await this.configManager.loadConfig();
      
      if (!config.environments[environment]) {
        console.log(chalk.red(`环境 ${environment} 不存在`));
        
        const { create } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'create',
            message: `是否创建新环境 ${environment}？`,
            default: true
          }
        ]);

        if (create) {
          const { configPath } = await inquirer.prompt([
            {
              type: 'input',
              name: 'configPath',
              message: '环境配置路径:',
              default: `./config/${environment}`
            }
          ]);

          config.environments[environment] = configPath;
          
          const spinner = ora('创建环境配置...').start();
          await this.configManager.updateConfig(config);
          spinner.succeed(chalk.green(`环境 ${environment} 创建成功`));
        }
        return;
      }

      console.log(chalk.blue(`🔄 切换到环境: ${environment}`));
      
      const envPath = config.environments[environment];
      const isValid = await this.configManager.validateEnvironmentConfig(environment);
      
      console.log(chalk.gray(`配置路径: ${envPath}`));
      console.log(chalk.gray(`配置状态: ${isValid ? '✓ 有效' : '✗ 无效'}`));

      if (!isValid) {
        console.log(chalk.yellow('环境配置文件不存在或无效'));
      }

    } catch (error) {
      console.error(chalk.red('切换环境失败:'), error);
    }
  }

  private async validateConfig(options?: any): Promise<void> {
    try {
      const spinner = ora('验证配置...').start();

      if (options?.env) {
        // 验证指定环境配置
        const isValid = await this.configManager.validateEnvironmentConfig(options.env);
        spinner.stop();
        
        if (isValid) {
          console.log(chalk.green(`✓ 环境 ${options.env} 配置有效`));
        } else {
          console.log(chalk.red(`✗ 环境 ${options.env} 配置无效`));
        }
      } else {
        // 验证主配置文件
        try {
          await this.configManager.loadConfig();
          spinner.succeed(chalk.green('✓ 配置文件验证通过'));
        } catch (error) {
          spinner.fail(chalk.red('✗ 配置文件验证失败'));
          console.error(chalk.red('错误详情:'), error);
        }
      }

    } catch (error) {
      console.error(chalk.red('配置验证失败:'), error);
    }
  }

  private async exportConfig(file?: string): Promise<void> {
    try {
      const config = await this.configManager.loadConfig();
      const exportFile = file || `version-craft-config-${Date.now()}.json`;

      const spinner = ora(`导出配置到 ${exportFile}...`).start();
      await fs.writeJSON(exportFile, config, { spaces: 2 });
      spinner.succeed(chalk.green(`配置已导出到: ${exportFile}`));

    } catch (error) {
      console.error(chalk.red('导出配置失败:'), error);
    }
  }

  private async importConfig(file: string): Promise<void> {
    try {
      if (!await fs.pathExists(file)) {
        console.log(chalk.red(`文件不存在: ${file}`));
        return;
      }

      const { confirm } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirm',
          message: '导入配置将覆盖当前配置，确定继续？',
          default: false
        }
      ]);

      if (!confirm) {
        console.log(chalk.gray('导入已取消'));
        return;
      }

      const spinner = ora(`从 ${file} 导入配置...`).start();
      const importedConfig = await fs.readJSON(file);
      
      await this.configManager.updateConfig(importedConfig);
      spinner.succeed(chalk.green('配置导入成功'));

    } catch (error) {
      console.error(chalk.red('导入配置失败:'), error);
    }
  }
}