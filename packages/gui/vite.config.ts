import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { join } from 'path';

export default defineConfig({
  plugins: [vue()],

  // 开发服务器配置
  server: {
    port: 5173,
    host: true
  },

  // CSS 配置
  css: {
    postcss: './postcss.config.js'
  },

  // 构建配置
  build: {
    outDir: 'dist/renderer',
    assetsDir: 'assets',
    sourcemap: process.env.NODE_ENV === 'development',
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          icons: ['@heroicons/vue'],
          utils: ['date-fns']
        }
      }
    }
  },

  // 路径别名
  resolve: {
    alias: {
      '@': join(__dirname, 'src/renderer'),
      '@main': join(__dirname, 'src/main'),
      '@shared': join(__dirname, 'src/shared')
    }
  },

  // 环境变量
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0')
  },

  // 基础路径 (Electron 使用 file:// 协议)
  base: './'
});