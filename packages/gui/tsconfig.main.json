{
  "compilerOptions": {
    "target": "ES2020",
    "module": "CommonJS",
    "lib": ["ES2020"],
    "outDir": "./dist/main",
    "rootDir": "./src/main",
    "noEmit": false,
    "strict": true,
    "skipLibCheck": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,

    /* Node.js specific */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,

    /* Type definitions */
    "types": ["node", "electron"]
  },
  "include": [
    "src/main/**/*.ts",
    "src/shared/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "release",
    "src/renderer"
  ]
}
