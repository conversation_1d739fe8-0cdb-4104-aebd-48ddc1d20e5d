<template>
  <div class="p-6 space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900">项目配置</h1>
      <div class="flex space-x-3">
        <button
          @click="resetConfig"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          重置配置
        </button>
        <button
          @click="saveConfig"
          :disabled="!hasChanges"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
        >
          保存配置
        </button>
      </div>
    </div>

    <!-- 基本配置 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">基本配置</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">项目名称</label>
          <input
            v-model="config.name"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="请输入项目名称"
          >
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">当前版本</label>
          <input
            v-model="config.version"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="1.0.0"
          >
        </div>
        
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-2">项目描述</label>
          <textarea
            v-model="config.description"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="请输入项目描述"
          ></textarea>
        </div>
      </div>
    </div>

    <!-- 构建配置 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">构建配置</h2>
      
      <div class="space-y-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">输出目录</label>
          <input
            v-model="config.build.outputDir"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="dist"
          >
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">支持平台</label>
          <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
            <label
              v-for="platform in availablePlatforms"
              :key="platform.id"
              class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
            >
              <input
                v-model="config.build.platforms"
                type="checkbox"
                :value="platform.id"
                class="mr-3"
              >
              <div>
                <div class="font-medium text-sm">{{ platform.name }}</div>
                <div class="text-xs text-gray-500">{{ platform.description }}</div>
              </div>
            </label>
          </div>
        </div>
        
        <div>
          <h3 class="text-base font-medium text-gray-900 mb-3">优化选项</h3>
          <div class="space-y-3">
            <label class="flex items-center">
              <input
                v-model="config.build.optimization.minify"
                type="checkbox"
                class="mr-3"
              >
              <div>
                <div class="font-medium text-sm">代码压缩</div>
                <div class="text-xs text-gray-500">压缩 JavaScript 和 CSS 代码</div>
              </div>
            </label>
            
            <label class="flex items-center">
              <input
                v-model="config.build.optimization.compress"
                type="checkbox"
                class="mr-3"
              >
              <div>
                <div class="font-medium text-sm">资源压缩</div>
                <div class="text-xs text-gray-500">压缩图片和其他资源文件</div>
              </div>
            </label>
            
            <label class="flex items-center">
              <input
                v-model="config.build.optimization.sourcemap"
                type="checkbox"
                class="mr-3"
              >
              <div>
                <div class="font-medium text-sm">生成 Source Map</div>
                <div class="text-xs text-gray-500">用于调试的源码映射文件</div>
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- 部署配置 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">部署配置</h2>
      
      <div class="space-y-6">
        <div
          v-for="(env, envName) in config.deploy.environments"
          :key="envName"
          class="border border-gray-200 rounded-lg p-4"
        >
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-base font-medium text-gray-900 capitalize">{{ envName }} 环境</h3>
            <label class="flex items-center">
              <input
                v-model="env.enabled"
                type="checkbox"
                class="mr-2"
              >
              <span class="text-sm text-gray-600">启用</span>
            </label>
          </div>
          
          <div v-if="env.enabled" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">服务器地址</label>
              <input
                v-model="env.server"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                placeholder="https://example.com"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">部署路径</label>
              <input
                v-model="env.path"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                placeholder="/var/www/html"
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 高级配置 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">高级配置</h2>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">自定义脚本</label>
          <div class="space-y-2">
            <div class="flex items-center space-x-3">
              <span class="text-sm text-gray-600 w-20">构建前:</span>
              <input
                v-model="config.scripts.prebuild"
                type="text"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                placeholder="npm run lint"
              >
            </div>
            
            <div class="flex items-center space-x-3">
              <span class="text-sm text-gray-600 w-20">构建后:</span>
              <input
                v-model="config.scripts.postbuild"
                type="text"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                placeholder="npm run test"
              >
            </div>
          </div>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">环境变量</label>
          <div class="space-y-2">
            <div
              v-for="(value, key, index) in config.env"
              :key="index"
              class="flex items-center space-x-3"
            >
              <input
                v-model="config.env[key]"
                type="text"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                :placeholder="`${key}=${value}`"
                readonly
              >
              <button
                @click="removeEnvVar(key)"
                class="text-red-600 hover:text-red-800"
              >
                删除
              </button>
            </div>
            
            <div class="flex items-center space-x-3">
              <input
                v-model="newEnvKey"
                type="text"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                placeholder="变量名"
              >
              <input
                v-model="newEnvValue"
                type="text"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                placeholder="变量值"
              >
              <button
                @click="addEnvVar"
                :disabled="!newEnvKey || !newEnvValue"
                class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 text-sm"
              >
                添加
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useAppStore } from '../stores/app';

// Store
const appStore = useAppStore();

// 响应式数据
const originalConfig = ref<any>({});
const config = ref<any>({
  name: '',
  version: '1.0.0',
  description: '',
  build: {
    outputDir: 'dist',
    platforms: ['web-mobile'],
    optimization: {
      minify: true,
      compress: true,
      sourcemap: false
    }
  },
  deploy: {
    environments: {
      development: {
        enabled: false,
        server: '',
        path: ''
      },
      staging: {
        enabled: false,
        server: '',
        path: ''
      },
      production: {
        enabled: false,
        server: '',
        path: ''
      }
    }
  },
  scripts: {
    prebuild: '',
    postbuild: ''
  },
  env: {
    NODE_ENV: 'production'
  }
});

const newEnvKey = ref('');
const newEnvValue = ref('');

// 可用平台
const availablePlatforms = [
  { id: 'web-mobile', name: 'Web Mobile', description: 'H5 移动端' },
  { id: 'android', name: 'Android', description: 'Android APK' },
  { id: 'ios', name: 'iOS', description: 'iOS IPA' },
  { id: 'windows', name: 'Windows', description: 'Windows EXE' },
  { id: 'mac', name: 'macOS', description: 'macOS APP' }
];

// 计算属性
const hasChanges = computed(() => {
  return JSON.stringify(config.value) !== JSON.stringify(originalConfig.value);
});

// 方法
const loadConfig = async () => {
  try {
    // 从项目信息中加载配置
    const projectInfo = appStore.projectInfo;
    if (projectInfo?.config) {
      config.value = { ...config.value, ...projectInfo.config };
    }
    
    // 从 package.json 中加载基本信息
    if (projectInfo?.package) {
      config.value.name = projectInfo.package.name || '';
      config.value.version = projectInfo.package.version || '1.0.0';
      config.value.description = projectInfo.package.description || '';
    }
    
    // 保存原始配置用于比较
    originalConfig.value = JSON.parse(JSON.stringify(config.value));
  } catch (error) {
    console.error('Load config error:', error);
  }
};

const saveConfig = async () => {
  try {
    // 这里应该调用 API 保存配置
    console.log('Saving config:', config.value);
    
    // 更新原始配置
    originalConfig.value = JSON.parse(JSON.stringify(config.value));
    
    // 显示成功消息
    alert('配置保存成功！');
  } catch (error) {
    console.error('Save config error:', error);
    alert('配置保存失败！');
  }
};

const resetConfig = () => {
  if (confirm('确定要重置配置吗？这将丢失所有未保存的更改。')) {
    config.value = JSON.parse(JSON.stringify(originalConfig.value));
  }
};

const addEnvVar = () => {
  if (newEnvKey.value && newEnvValue.value) {
    config.value.env[newEnvKey.value] = newEnvValue.value;
    newEnvKey.value = '';
    newEnvValue.value = '';
  }
};

const removeEnvVar = (key: string) => {
  delete config.value.env[key];
};

// 生命周期
onMounted(() => {
  if (appStore.hasProject) {
    loadConfig();
  }
});

// 监听项目变化
watch(() => appStore.projectInfo, () => {
  if (appStore.hasProject) {
    loadConfig();
  }
});
</script>
