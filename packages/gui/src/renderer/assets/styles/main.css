@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 自定义字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* 基础样式 */
@layer base {
  * {
    @apply border-gray-200;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Firefox 滚动条 */
  * {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
  }
}

/* 组件样式 */
@layer components {
  /* 按钮组件 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }

  .btn-success {
    @apply btn bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  /* 卡片组件 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  /* 表单组件 */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm;
  }

  .form-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  /* 导航项 */
  .nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150;
  }

  .nav-item-active {
    @apply bg-blue-50 text-blue-700;
  }

  /* 加载动画 */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本截断 */
  .text-truncate {
    @apply truncate;
  }

  /* 拖拽区域 */
  .drag-region {
    -webkit-app-region: drag;
  }

  .no-drag {
    -webkit-app-region: no-drag;
  }
}

/* Electron 特定样式 */
.electron-app {
  /* 禁用文本选择 */
  -webkit-user-select: none;
  user-select: none;
}

.electron-app input,
.electron-app textarea {
  /* 允许输入框文本选择 */
  -webkit-user-select: text;
  user-select: text;
}
