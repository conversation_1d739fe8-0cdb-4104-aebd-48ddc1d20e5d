import { spawn, ChildProcess } from 'child_process';
import { join } from 'path';
import fs from 'fs-extra';
import { BrowserWindow } from 'electron';

export class VersionCraftService {
  private projectPath: string;
  private cliPath: string;
  private runningProcesses: Map<string, ChildProcess> = new Map();

  constructor(projectPath: string) {
    this.projectPath = projectPath;
    // CLI 路径 - 从 GUI 的 dist/main 目录向上找到项目根目录的 dist/cli.js
    this.cliPath = join(__dirname, '../../../../dist/cli.js');
    console.log('🔧 [VersionCraftService] 初始化服务');
    console.log('📁 [VersionCraftService] 项目路径:', this.projectPath);
    console.log('⚙️ [VersionCraftService] CLI 路径:', this.cliPath);
  }

  /**
   * 执行 CLI 命令
   */
  private async executeCommand(args: string[], options: {
    onProgress?: (data: string) => void;
    processId?: string;
  } = {}): Promise<{ success: boolean; output?: string; error?: string }> {
    console.log('🔧 [VersionCraftService] 执行命令:', ['node', this.cliPath, ...args].join(' '));
    console.log('📁 [VersionCraftService] 工作目录:', this.projectPath);

    return new Promise((resolve) => {
      const child = spawn('node', [this.cliPath, ...args], {
        cwd: this.projectPath,
        stdio: 'pipe',
        shell: true
      });

      // 如果提供了进程ID，保存进程引用
      if (options.processId) {
        this.runningProcesses.set(options.processId, child);
      }

      let output = '';
      let error = '';

      child.stdout?.on('data', (data) => {
        // 正确处理编码并清理 ANSI 颜色代码
        let text = data.toString('utf8');
        // 移除 ANSI 颜色代码
        text = text.replace(/\x1b\[[0-9;]*m/g, '');
        output += text;
        console.log('📤 [CLI-STDOUT]:', text.trim());

        // 实时进度回调
        if (options.onProgress) {
          options.onProgress(text);
        }
      });

      child.stderr?.on('data', (data) => {
        // 正确处理编码并清理 ANSI 颜色代码
        let text = data.toString('utf8');
        // 移除 ANSI 颜色代码
        text = text.replace(/\x1b\[[0-9;]*m/g, '');

        // 区分真正的错误和进度信息
        const isProgressInfo = text.includes('获取') || text.includes('加载') || text.includes('-');

        if (isProgressInfo) {
          // 进度信息不算作错误
          console.log('📋 [CLI-INFO]:', text.trim());
        } else {
          // 真正的错误信息
          error += text;
          console.error('📤 [CLI-STDERR]:', text.trim());
        }

        // 所有信息都通过进度回调传递
        if (options.onProgress) {
          options.onProgress(text);
        }
      });

      child.on('close', (code) => {
        console.log('🏁 [CLI] 命令执行完成，退出码:', code);

        // 只显示关键输出，过滤重复的配置信息
        const filteredOutput = output
          .split('\n')
          .filter(line =>
            !line.includes('配置文件权限') &&
            !line.includes('configPath') &&
            !line.includes('currentMode') &&
            line.trim() !== ''
          )
          .join('\n');

        if (filteredOutput.trim()) {
          console.log('📤 [CLI] 关键输出:', filteredOutput.trim());
        }

        if (error && error.trim()) {
          console.error('❌ [CLI] 错误:', error.trim());
        }

        // 清理进程引用
        if (options.processId) {
          this.runningProcesses.delete(options.processId);
        }

        if (code === 0) {
          resolve({ success: true, output });
        } else {
          resolve({ success: false, error: error || '命令执行失败' });
        }
      });

      child.on('error', (err) => {
        console.error('💥 [CLI] 进程错误:', err.message);

        // 清理进程引用
        if (options.processId) {
          this.runningProcesses.delete(options.processId);
        }

        resolve({ success: false, error: err.message });
      });
    });
  }

  /**
   * 获取当前版本
   */
  async getCurrentVersion(): Promise<any> {
    console.log('[VersionCraftService] Getting current version...');

    try {
      // 首先尝试使用 CLI 命令获取版本
      const result = await this.executeCommand(['current']);

      if (result.success && result.output) {
        // 解析 CLI 输出，清理 ANSI 颜色代码
        let cleanOutput = result.output.replace(/\x1b\[[0-9;]*m/g, '');
        const versionMatch = cleanOutput.match(/当前版本:\s*(.+)/);
        if (versionMatch) {
          const version = versionMatch[1].trim();
          console.log('[VersionCraftService] Got version from CLI:', version);

          // 获取额外信息
          const gitInfo = await this.getGitInfo();
          const packagePath = join(this.projectPath, 'package.json');

          let packageInfo = {};
          if (await this.fileExists(packagePath)) {
            const fs = require('fs');
            const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf-8'));
            packageInfo = {
              name: packageJson.name,
              description: packageJson.description
            };
          }

          return {
            version,
            ...packageInfo,
            ...gitInfo,
            lastModified: new Date().toISOString()
          };
        }
      }

      console.log('[VersionCraftService] CLI failed, reading package.json directly');
      // 如果 CLI 失败，直接读取 package.json
      return this.getCurrentVersionFromPackage();

    } catch (error) {
      console.error('[VersionCraftService] Error getting current version:', error);
      // 降级到直接读取 package.json
      return this.getCurrentVersionFromPackage();
    }
  }

  /**
   * 从 package.json 直接获取版本信息
   */
  private async getCurrentVersionFromPackage(): Promise<any> {
    try {
      const packagePath = join(this.projectPath, 'package.json');
      if (await this.fileExists(packagePath)) {
        const fs = require('fs');
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf-8'));

        // 获取额外的版本信息
        const gitInfo = await this.getGitInfo();

        return {
          version: packageJson.version,
          name: packageJson.name,
          description: packageJson.description,
          ...gitInfo,
          lastModified: new Date().toISOString()
        };
      }

      throw new Error('package.json file not found');
    } catch (error) {
      throw new Error(`Failed to get current version: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * 检查文件是否存在
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      const fs = require('fs');
      return fs.existsSync(filePath);
    } catch {
      return false;
    }
  }

  /**
   * 获取版本历史
   */
  async getVersionHistory(): Promise<any[]> {
    console.log('[VersionCraftService] Getting version history...');

    // 使用正确的 CLI 命令：list
    const result = await this.executeCommand(['list']);

    if (result.success && result.output) {
      try {
        // 尝试解析 JSON 输出
        return JSON.parse(result.output);
      } catch {
        // 如果 JSON 解析失败，解析文本输出
        return this.parseVersionListOutput(result.output);
      }
    }

    console.log('[VersionCraftService] CLI command failed, using Git history');
    // 如果命令失败，尝试从 Git 获取历史
    return this.getGitVersionHistory();
  }

  /**
   * 版本升级
   */
  async bumpVersion(options: {
    type: 'major' | 'minor' | 'patch';
    prerelease?: string;
    message?: string;
  }): Promise<any> {
    const args = ['bump', options.type];

    if (options.prerelease) {
      args.push('--prerelease', options.prerelease);
    }

    // CLI 不支持 --message 参数，移除这个选项
    // if (options.message) {
    //   args.push('--message', options.message);
    // }

    // 添加 --tag 选项来创建 Git 标签
    args.push('--tag');

    const result = await this.executeCommand(args);

    if (result.success) {
      // 通知渲染进程版本已更改
      this.notifyVersionChanged();
      return { message: '版本升级成功', output: result.output };
    } else {
      throw new Error(result.error || '版本升级失败');
    }
  }

  /**
   * 版本回滚
   */
  async rollbackVersion(targetVersion: string, force: boolean = false): Promise<any> {
    console.log('[VersionCraftService] Rolling back to version:', targetVersion);

    // 使用正确的 CLI 命令：rollback
    const args = ['rollback', targetVersion];

    if (force) {
      args.push('--force');
    }

    console.log('[VersionCraftService] Rollback command:', args.join(' '));

    const result = await this.executeCommand(args);

    if (result.success) {
      console.log('[VersionCraftService] Rollback successful');
      // 通知渲染进程版本已更改
      this.notifyVersionChanged();
      return { message: 'Version rollback successful', output: result.output };
    } else {
      console.error('[VersionCraftService] Rollback failed:', result.error);
      throw new Error(result.error || 'Version rollback failed');
    }
  }

  /**
   * 开始构建
   */
  async startBuild(platform: string, options: any = {}): Promise<any> {
    console.log('[VersionCraftService] Starting build for platform:', platform);

    const buildId = `build_${Date.now()}`;
    let args: string[] = [];

    // 根据平台使用正确的 CLI 命令
    switch (platform) {
      case 'web-mobile':
        args = ['build-web'];
        break;
      case 'android':
        args = ['build-android'];
        break;
      case 'ios':
        args = ['build-ios'];
        break;
      case 'windows':
        args = ['build-win'];
        break;
      case 'mac':
        args = ['build-mac'];
        break;
      default:
        args = ['build-web']; // 默认构建 web
    }

    // 添加构建选项
    if (options.clean) args.push('--clean');
    if (options.sign && platform === 'android') args.push('--sign');

    console.log('[VersionCraftService] Build command:', args.join(' '));

    // 异步执行构建，通过进度回调实时更新
    this.executeCommand(args, {
      processId: buildId,
      onProgress: (data) => {
        this.notifyBuildProgress(buildId, data);
      }
    }).then((result) => {
      // 构建完成通知
      this.notifyBuildComplete(buildId, result.success, result.error);
    });

    return {
      buildId,
      message: 'Build started',
      platform,
      startTime: new Date().toISOString()
    };
  }

  /**
   * 取消构建
   */
  cancelBuild(buildId: string): boolean {
    const process = this.runningProcesses.get(buildId);
    if (process) {
      process.kill('SIGTERM');
      this.runningProcesses.delete(buildId);
      return true;
    }
    return false;
  }

  /**
   * 获取 Git 信息
   */
  private async getGitInfo(): Promise<any> {
    try {
      const gitDir = join(this.projectPath, '.git');
      if (await fs.pathExists(gitDir)) {
        // 这里可以使用 simple-git 获取更详细的 Git 信息
        return {
          hasGit: true,
          branch: 'main', // 简化处理
          lastCommit: new Date().toISOString()
        };
      }
      return { hasGit: false };
    } catch {
      return { hasGit: false };
    }
  }

  /**
   * 从 Git 获取版本历史
   */
  private async getGitVersionHistory(): Promise<any[]> {
    // 简化实现，返回模拟数据
    return [
      {
        version: '1.0.0',
        date: new Date().toISOString(),
        message: '初始版本',
        author: 'Developer'
      }
    ];
  }

  /**
   * 解析版本列表输出
   */
  private parseVersionListOutput(output: string): any[] {
    console.log('[VersionCraftService] Parsing version list output:', output);

    // 清理 ANSI 颜色代码
    const cleanOutput = output.replace(/\x1b\[[0-9;]*m/g, '');
    const lines = cleanOutput.split('\n').filter(line => line.trim());
    const versions: any[] = [];

    for (const line of lines) {
      // 尝试匹配版本号格式，支持更复杂的版本号
      const versionMatch = line.match(/(\d+\.\d+\.\d+(?:[-\w\.]*)?)/);
      if (versionMatch) {
        const version = versionMatch[1];
        // 检查是否是当前版本
        const isCurrent = line.includes('当前版本') || line.includes('✓');

        versions.push({
          version,
          message: line.replace(version, '').replace(/[✓\s\(\)当前版本]/g, '').trim() || 'No description',
          date: new Date().toISOString(),
          author: 'Unknown',
          isCurrent
        });
      }
    }

    return versions.length > 0 ? versions : this.getDefaultVersionHistory();
  }

  /**
   * 获取默认版本历史
   */
  private getDefaultVersionHistory(): any[] {
    return [
      {
        version: '1.0.0',
        message: 'Initial version',
        date: new Date().toISOString(),
        author: 'Developer'
      }
    ];
  }

  /**
   * 通知版本变更
   */
  private notifyVersionChanged(): void {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send('version-changed', {
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 通知构建进度
   */
  private notifyBuildProgress(buildId: string, data: string): void {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send('build-progress', {
        buildId,
        data,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 通知构建完成
   */
  private notifyBuildComplete(buildId: string, success: boolean, error?: string): void {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send('build-progress', {
        buildId,
        completed: true,
        success,
        error,
        timestamp: new Date().toISOString()
      });
    }
  }
}
