# Version-Craft GUI

Version-Craft 的桌面 GUI 应用，为 Version-Craft CLI 工具提供直观的可视化界面。

## 🎯 设计理念

Version-Craft GUI 是作为 CLI 工具的**可视化补充**而设计的，旨在：

- 🎨 **降低使用门槛** - 为不熟悉命令行的用户提供图形界面
- ⚡ **提高操作效率** - 通过可视化界面快速完成常用操作
- 📊 **增强信息展示** - 直观显示项目状态、版本历史等信息
- 🔄 **保持轻量级** - 不改变 Version-Craft 的核心定位

## ✨ 核心功能

### 📁 项目管理
- **项目选择** - 快速选择和切换 Version-Craft 项目
- **项目概览** - 显示项目基本信息和统计数据
- **配置管理** - 可视化编辑项目配置文件

### 🏷️ 版本管理
- **当前版本显示** - 清晰展示当前项目版本信息
- **版本升级向导** - 分步引导完成版本升级操作
- **版本历史浏览** - 可视化展示版本变更历史
- **版本回滚操作** - 安全的版本回滚功能

### 🔨 构建管理
- **多平台构建** - 支持 Web、Android、iOS、Windows、Mac 平台
- **实时进度监控** - 显示构建进度和日志信息
- **构建结果管理** - 查看构建结果和产物

### ⚙️ 配置管理
- **可视化配置** - 图形界面编辑 version-craft.config.json
- **配置验证** - 实时验证配置文件的正确性
- **默认配置生成** - 为新项目生成标准配置

## 🏗️ 技术架构

### 技术栈
- **Electron** - 跨平台桌面应用框架
- **Vue 3** - 现代化前端框架
- **TypeScript** - 类型安全的开发体验
- **Tailwind CSS** - 实用优先的样式框架
- **Pinia** - 轻量级状态管理

### 架构设计
```
┌─────────────────────────────────────┐
│           Electron 主进程            │
│  ┌─────────────────────────────────┐ │
│  │      Version-Craft 服务层        │ │
│  │  ┌─────────────┬─────────────┐   │ │
│  │  │ CLI 调用服务 │ 项目管理服务 │   │ │
│  │  └─────────────┴─────────────┘   │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
                    │
                   IPC
                    │
┌─────────────────────────────────────┐
│          Vue 3 渲染进程              │
│  ┌─────────────────────────────────┐ │
│  │         用户界面层               │ │
│  │  ┌─────────┬─────────┬───────┐   │ │
│  │  │ 项目概览 │ 版本管理 │ 构建  │   │ │
│  │  └─────────┴─────────┴───────┘   │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
cd version-craft-gui
npm install
```

### 开发环境
```bash
# 启动开发环境
npm run dev

# 或使用自定义脚本
node scripts/dev.js
```

### 构建应用
```bash
# 构建渲染进程
npm run build:renderer

# 构建主进程
npm run build:main

# 完整构建
npm run build
```

### 打包分发
```bash
# 打包所有平台
npm run dist

# 打包 Windows
npm run dist:win

# 打包 macOS
npm run dist:mac

# 打包 Linux
npm run dist:linux
```

## 📁 项目结构

```
version-craft-gui/
├── src/
│   ├── main/                   # Electron 主进程
│   │   ├── index.ts           # 主进程入口
│   │   ├── preload.ts         # 预加载脚本
│   │   └── services/          # 服务层
│   │       ├── VersionCraftService.ts
│   │       └── ProjectService.ts
│   └── renderer/              # Vue 渲染进程
│       ├── main.ts           # 前端入口
│       ├── App.vue           # 主应用组件
│       ├── components/       # 通用组件
│       ├── views/           # 页面组件
│       ├── stores/          # 状态管理
│       └── assets/          # 静态资源
├── dist/                     # 构建输出
├── release/                  # 打包输出
├── scripts/                  # 构建脚本
└── package.json
```

## 🎨 界面预览

### 主界面
```
┌─────────────────────────────────────────┐
│ Version-Craft GUI                    [_][□][×] │
├─────────────────────────────────────────┤
│ 📁 项目: my-cocos-project            🔄 刷新 │
├─────────────────────────────────────────┤
│ 📊 当前版本: 1.2.3                        │
│ 📅 创建时间: 2024-01-15 10:30:00          │
├─────────────────────────────────────────┤
│ [🚀 版本升级] [🔄 版本回滚] [🔨 构建项目]    │
└─────────────────────────────────────────┘
```

### 版本升级向导
```
┌─────────────────────────────────────────┐
│ 版本升级向导                    [1/3]     │
├─────────────────────────────────────────┤
│ 当前版本: 1.2.3                         │
│                                         │
│ 选择升级类型:                            │
│ ○ Major (2.0.0) - 重大版本更新           │
│ ● Minor (1.3.0) - 功能更新              │
│ ○ Patch (1.2.4) - 修复更新              │
│                                         │
│ [上一步] [下一步] [取消]                  │
└─────────────────────────────────────────┘
```

## 🔧 开发指南

### 添加新功能
1. 在主进程中添加 IPC 处理器
2. 在预加载脚本中暴露 API
3. 在渲染进程中调用 API
4. 更新 TypeScript 类型定义

### 调试技巧
- 主进程调试：使用 `console.log` 或 VS Code 调试器
- 渲染进程调试：打开开发者工具 (Ctrl+Shift+I)
- IPC 通信调试：在控制台查看 IPC 消息

### 性能优化
- 使用 `webSecurity: false` 仅在开发环境
- 合理使用 `nodeIntegration` 和 `contextIsolation`
- 避免在渲染进程中执行重计算任务

## 📦 打包配置

应用使用 `electron-builder` 进行打包，支持：

- **Windows**: NSIS 安装包 (.exe)
- **macOS**: DMG 磁盘镜像 (.dmg)
- **Linux**: AppImage 便携应用 (.AppImage)

### 自定义打包
编辑 `package.json` 中的 `build` 配置：

```json
{
  "build": {
    "appId": "com.versioncraft.gui",
    "productName": "Version-Craft GUI",
    "directories": {
      "output": "release"
    }
  }
}
```

## 🤝 与 CLI 集成

GUI 应用通过以下方式与 CLI 集成：

1. **直接调用**: 使用 `child_process` 执行 CLI 命令
2. **配置共享**: 读写相同的配置文件
3. **项目兼容**: 支持所有 CLI 支持的项目结构

### CLI 命令映射
- `version-craft bump` → GUI 版本升级向导
- `version-craft build` → GUI 构建管理
- `version-craft rollback-to` → GUI 版本回滚

## 🐛 故障排除

### 常见问题

#### 1. 应用无法启动
```bash
# 检查 Node.js 版本
node --version

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

#### 2. 构建失败
```bash
# 清理构建缓存
npm run clean

# 重新构建
npm run build
```

#### 3. 打包失败
```bash
# 检查 electron-builder 配置
npx electron-builder --help

# 清理并重新打包
rm -rf dist release
npm run dist
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题或有建议：

1. 查看 [常见问题](#故障排除)
2. 搜索现有 Issues
3. 创建新的 Issue
4. 联系开发团队

---

**Version-Craft GUI - 让版本管理更简单！** 🎉
