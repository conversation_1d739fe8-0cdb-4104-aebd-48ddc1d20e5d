<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Version-Craft GUI</title>
    <style>
      /* 防止页面闪烁 */
      body {
        margin: 0;
        padding: 0;
        background-color: #f9fafb;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      }
      
      /* 加载动画 */
      .loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #f9fafb;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        margin-top: 16px;
        color: #6b7280;
        font-size: 14px;
      }
    </style>
  </head>
  <body class="electron-app">
    <!-- 加载动画 -->
    <div id="loading" class="loading">
      <div class="text-center">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载 Version-Craft GUI...</div>
      </div>
    </div>
    
    <!-- Vue 应用挂载点 -->
    <div id="app"></div>
    
    <script type="module" src="/src/renderer/main.ts"></script>
    
    <script>
      // 应用加载完成后隐藏加载动画
      window.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            loading.style.transition = 'opacity 0.3s ease-out';
            setTimeout(() => {
              loading.style.display = 'none';
            }, 300);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
