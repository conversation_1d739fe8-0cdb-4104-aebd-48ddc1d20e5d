#!/usr/bin/env node

const { spawn } = require('child_process');
const { createServer } = require('vite');
const electron = require('electron');
const path = require('path');
const fs = require('fs');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(color, prefix, message) {
  console.log(`${colors[color]}[${prefix}]${colors.reset} ${message}`);
}

async function startDev() {
  log('blue', 'DEV', '启动 Version-Craft GUI 开发环境...');

  // 1. 启动 Vite 开发服务器
  log('cyan', 'VITE', '启动 Vite 开发服务器...');
  
  const server = await createServer({
    configFile: path.resolve(__dirname, '../vite.config.ts'),
    mode: 'development'
  });
  
  await server.listen(5173);
  log('green', 'VITE', 'Vite 开发服务器已启动: http://localhost:5173');

  // 2. 编译主进程代码
  log('cyan', 'TSC', '编译主进程 TypeScript 代码...');
  
  const tscProcess = spawn('npx', ['tsc', '-p', 'tsconfig.main.json', '--watch'], {
    stdio: 'pipe',
    shell: true
  });

  tscProcess.stdout.on('data', (data) => {
    const output = data.toString().trim();
    if (output) {
      log('yellow', 'TSC', output);
    }
  });

  tscProcess.stderr.on('data', (data) => {
    const output = data.toString().trim();
    if (output && !output.includes('Starting compilation')) {
      log('red', 'TSC', output);
    }
  });

  // 等待初始编译完成
  await new Promise((resolve) => {
    const checkCompiled = () => {
      const mainFile = path.resolve(__dirname, '../dist/main/index.js');
      if (fs.existsSync(mainFile)) {
        log('green', 'TSC', '主进程代码编译完成');
        resolve();
      } else {
        setTimeout(checkCompiled, 100);
      }
    };
    checkCompiled();
  });

  // 3. 启动 Electron
  log('cyan', 'ELECTRON', '启动 Electron 应用...');
  
  let electronProcess = null;
  
  const startElectron = () => {
    if (electronProcess) {
      electronProcess.kill();
    }
    
    electronProcess = spawn(electron, ['.'], {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'development'
      }
    });

    electronProcess.on('close', (code) => {
      if (code !== null) {
        log('yellow', 'ELECTRON', `Electron 进程退出，退出码: ${code}`);
        process.exit(code);
      }
    });
  };

  startElectron();

  // 监听主进程文件变化，自动重启 Electron
  const chokidar = require('chokidar');
  const watcher = chokidar.watch(path.resolve(__dirname, '../dist/main'), {
    ignored: /node_modules/,
    persistent: true
  });

  watcher.on('change', (filePath) => {
    log('yellow', 'RELOAD', `检测到主进程文件变化: ${path.basename(filePath)}`);
    log('cyan', 'ELECTRON', '重启 Electron 应用...');
    startElectron();
  });

  // 优雅关闭
  process.on('SIGINT', () => {
    log('yellow', 'DEV', '正在关闭开发环境...');
    
    if (electronProcess) {
      electronProcess.kill();
    }
    
    if (tscProcess) {
      tscProcess.kill();
    }
    
    server.close();
    watcher.close();
    
    log('green', 'DEV', '开发环境已关闭');
    process.exit(0);
  });

  log('green', 'DEV', '开发环境启动完成!');
  log('blue', 'INFO', '按 Ctrl+C 停止开发服务器');
}

// 错误处理
process.on('uncaughtException', (error) => {
  log('red', 'ERROR', `未捕获的异常: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log('red', 'ERROR', `未处理的 Promise 拒绝: ${reason}`);
  process.exit(1);
});

// 启动开发环境
startDev().catch((error) => {
  log('red', 'ERROR', `启动失败: ${error.message}`);
  process.exit(1);
});
