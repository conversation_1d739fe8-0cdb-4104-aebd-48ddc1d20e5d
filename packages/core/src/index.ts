// 核心管理器
export { VersionManager } from './modules/version/VersionManager';
export { BuildManager } from './modules/build/BuildManager';
export { ConfigManager } from './modules/config/ConfigManager';
export { DeployManager } from './modules/deploy/DeployManager';
export { HotUpdateManager, ResourceInfo, ResourceManifest, IncrementalUpdate } from './modules/hotupdate/HotUpdateManager';
export { RollbackManager, RollbackInfo, RollbackOptions, RollbackRecord } from './modules/rollback/RollbackManager';

// 工具类
export { Logger, LogLevel } from './utils/Logger';
export { SecurityUtils } from './utils/SecurityUtils';
export { FileUtils } from './utils/FileUtils';
export { ProjectDetector } from './utils/ProjectDetector';
export { VersionValidator } from './utils/VersionValidator';
export { VersionHistory } from './utils/VersionHistory';
export { CocosCreatorDetector } from './utils/CocosCreatorDetector';
export { InitializationHelper } from './utils/InitializationHelper';


// 类型定义
export * from './types/version';
export * from './types/config';

// 创建单例实例的工厂函数
export function createVersionCraftCore(projectPath?: string) {
  // 设置工作目录
  if (projectPath) {
    process.chdir(projectPath);
  }

  // 动态导入避免循环依赖
  const { VersionManager } = require('./modules/version/VersionManager');
  const { BuildManager } = require('./modules/build/BuildManager');
  const { ConfigManager } = require('./modules/config/ConfigManager');
  const { DeployManager } = require('./modules/deploy/DeployManager');
  const { HotUpdateManager } = require('./modules/hotupdate/HotUpdateManager');
  const { RollbackManager } = require('./modules/rollback/RollbackManager');

  return {
    versionManager: new VersionManager(),
    buildManager: new BuildManager(),
    configManager: new ConfigManager(),
    deployManager: new DeployManager(),
    hotUpdateManager: new HotUpdateManager(),
    rollbackManager: new RollbackManager()
  };
}
