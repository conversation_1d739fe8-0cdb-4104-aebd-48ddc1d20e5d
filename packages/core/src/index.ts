// 核心管理器
export { VersionManager } from './modules/version/VersionManager';
export { BuildManager } from './modules/build/BuildManager';
export { ConfigManager } from './modules/config/ConfigManager';
export { DeployManager } from './modules/deploy/DeployManager';

// 工具类
export { Logger, LogLevel } from './utils/Logger';
export { SecurityUtils } from './utils/SecurityUtils';
export { FileUtils } from './utils/FileUtils';
export { ProjectDetector } from './utils/ProjectDetector';
export { VersionValidator } from './utils/VersionValidator';

// 类型定义
export * from './types/version';
export * from './types/config';

// 创建单例实例的工厂函数
export function createVersionCraftCore(projectPath?: string) {
  // 设置工作目录
  if (projectPath) {
    process.chdir(projectPath);
  }

  return {
    versionManager: new VersionManager(),
    buildManager: new BuildManager(),
    configManager: new ConfigManager(),
    deployManager: new DeployManager()
  };
}
