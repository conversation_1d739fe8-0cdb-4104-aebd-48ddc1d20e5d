export type VersionType = 'major' | 'minor' | 'patch' | 'prerelease';

export type PrereleaseType = 'alpha' | 'beta' | 'rc';

export interface VersionInfo {
  current: string;
  next: string;
  type: VersionType;
  prerelease?: PrereleaseType;
}

export interface BuildResult {
  platform: string;
  success: boolean;
  outputPath?: string;
  buildTime: number;
  fileSize?: number;
  error?: string;
}

export interface DeployResult {
  platform: string;
  environment: string;
  success: boolean;
  url?: string;
  deployTime: number;
  error?: string;
}

export interface ChangelogEntry {
  version: string;
  date: string;
  changes: {
    added: string[];
    changed: string[];
    deprecated: string[];
    removed: string[];
    fixed: string[];
    security: string[];
  };
}

export interface RollbackInfo {
  version: string;
  date: string;
  commitHash: string;
  buildExists: boolean;
}

// 新增：GUI 需要的类型定义
export interface VersionListItem {
  version: string;
  isCurrent: boolean;
  marker: string;
  date?: string;
  author?: string;
  message?: string;
}

export interface BuildStats {
  totalBuilds: number;
  successfulBuilds: number;
  failedBuilds: number;
  averageBuildTime: number;
  lastBuildTime?: string;
  platformStats: {
    [platform: string]: {
      builds: number;
      successes: number;
      failures: number;
      averageTime: number;
    };
  };
}

export interface ProjectInfo {
  name: string;
  version: string;
  description?: string;
  path: string;
  type?: string;
  lastModified?: string;
}
