import * as semver from 'semver';
import * as fs from 'fs-extra';
import * as path from 'path';
import { simpleGit, SimpleGit } from 'simple-git';
import { Logger } from './Logger';

/**
 * 版本历史管理器
 * 严格按照VERSION_MAINTENANCE_RULES.md规范管理版本历史
 */
export class VersionHistory {
  private static logger = Logger.getInstance();
  private static git: SimpleGit;
  private static historyFile: string;

  /**
   * 初始化版本历史管理器
   */
  static initialize(projectRoot: string = process.cwd()) {
    this.git = simpleGit(projectRoot);
    this.historyFile = path.join(projectRoot, '.version-history.json');
  }

  /**
   * 记录版本变更
   */
  static async recordVersionChange(from: string, to: string, operation: 'bump' | 'rollback', metadata?: any): Promise<void> {
    try {
      const history = await this.loadHistory();
      
      const record = {
        timestamp: new Date().toISOString(),
        from,
        to,
        operation,
        metadata: metadata || {}
      };

      history.changes.push(record);
      
      // 更新最高版本
      if (operation === 'bump' && semver.gt(to, history.highestVersion)) {
        history.highestVersion = to;
      }

      await this.saveHistory(history);
      this.logger.info('版本变更已记录', record);

    } catch (error) {
      this.logger.error('记录版本变更失败', error);
    }
  }

  /**
   * 获取历史最高版本
   * 用于回滚后的版本升级基准
   */
  static async getHighestVersion(): Promise<string> {
    try {
      const history = await this.loadHistory();
      
      // 从Git标签获取最高版本作为备选
      const gitHighest = await this.getHighestGitVersion();
      
      // 返回两者中较高的版本
      if (semver.gt(gitHighest, history.highestVersion)) {
        history.highestVersion = gitHighest;
        await this.saveHistory(history);
        return gitHighest;
      }

      return history.highestVersion;

    } catch (error) {
      this.logger.error('获取最高版本失败', error);
      return '0.0.0';
    }
  }

  /**
   * 获取版本升级基准
   * 回滚后，下次升级应该基于历史最高版本
   */
  static async getUpgradeBaseline(currentVersion: string): Promise<string> {
    try {
      const highestVersion = await this.getHighestVersion();
      
      // 如果当前版本低于历史最高版本，使用历史最高版本作为基准
      if (semver.lt(currentVersion, highestVersion)) {
        this.logger.warn('当前版本低于历史最高版本，使用历史最高版本作为升级基准', {
          current: currentVersion,
          highest: highestVersion
        });
        return highestVersion;
      }

      return currentVersion;

    } catch (error) {
      this.logger.error('获取升级基准失败', error);
      return currentVersion;
    }
  }

  /**
   * 检查是否为回滚状态
   */
  static async isInRollbackState(currentVersion: string): Promise<boolean> {
    try {
      const highestVersion = await this.getHighestVersion();
      return semver.lt(currentVersion, highestVersion);
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取版本变更历史
   */
  static async getVersionHistory(limit: number = 10): Promise<any[]> {
    try {
      const history = await this.loadHistory();
      return history.changes
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit);
    } catch (error) {
      this.logger.error('获取版本历史失败', error);
      return [];
    }
  }

  /**
   * 清理过期的历史记录
   */
  static async cleanupHistory(keepDays: number = 90): Promise<void> {
    try {
      const history = await this.loadHistory();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - keepDays);

      history.changes = history.changes.filter(change => 
        new Date(change.timestamp) > cutoffDate
      );

      await this.saveHistory(history);
      this.logger.info('版本历史已清理', { keepDays, remaining: history.changes.length });

    } catch (error) {
      this.logger.error('清理版本历史失败', error);
    }
  }

  /**
   * 加载版本历史
   */
  private static async loadHistory(): Promise<{
    highestVersion: string;
    changes: Array<{
      timestamp: string;
      from: string;
      to: string;
      operation: 'bump' | 'rollback';
      metadata: any;
    }>;
  }> {
    try {
      if (await fs.pathExists(this.historyFile)) {
        return await fs.readJSON(this.historyFile);
      }
    } catch (error) {
      this.logger.warn('加载版本历史失败，使用默认值', error);
    }

    // 返回默认历史结构
    return {
      highestVersion: '0.0.0',
      changes: []
    };
  }

  /**
   * 保存版本历史
   */
  private static async saveHistory(history: any): Promise<void> {
    try {
      await fs.writeJSON(this.historyFile, history, { spaces: 2 });
    } catch (error) {
      this.logger.error('保存版本历史失败', error);
    }
  }

  /**
   * 从Git标签获取最高版本
   */
  private static async getHighestGitVersion(): Promise<string> {
    try {
      // 添加超时保护，避免Git操作卡死
      const timeoutPromise = new Promise<string>((_, reject) => {
        setTimeout(() => reject(new Error('Git操作超时')), 5000);
      });

      const gitPromise = this.git.tags(['--sort=-version:refname']).then(tags => {
        for (const tag of tags.all) {
          if (tag.startsWith('v')) {
            const version = tag.substring(1);
            if (semver.valid(version)) {
              return version;
            }
          }
        }
        return '0.0.0';
      });

      return await Promise.race([gitPromise, timeoutPromise]);

    } catch (error) {
      this.logger.warn('从Git获取最高版本失败', error);
      return '0.0.0';
    }
  }
}
