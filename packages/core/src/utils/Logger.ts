import chalk from 'chalk';
import * as fs from 'fs-extra';
import * as path from 'path';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export class Logger {
  private static instance: Logger;
  private logLevel: LogLevel = LogLevel.INFO;
  private logFile?: string;
  private silent: boolean = false; // 新增：支持静默模式（GUI 使用）

  private constructor() {}

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  setLogFile(file: string): void {
    this.logFile = file;
    fs.ensureDirSync(path.dirname(file));
  }

  setSilent(silent: boolean): void {
    this.silent = silent;
  }

  debug(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.DEBUG) {
      this.log('DEBUG', chalk.gray(message), ...args);
    }
  }

  info(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.INFO) {
      this.log('INFO', chalk.blue(message), ...args);
    }
  }

  warn(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.WARN) {
      this.log('WARN', chalk.yellow(message), ...args);
    }
  }

  error(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.ERROR) {
      this.log('ERROR', chalk.red(message), ...args);
    }
  }

  success(message: string, ...args: any[]): void {
    this.log('SUCCESS', chalk.green(message), ...args);
  }

  private log(level: string, message: string, ...args: any[]): void {
    const timestamp = new Date().toISOString();
    
    // 控制台输出（除非是静默模式）
    if (!this.silent) {
      console.log(message, ...args);
    }
    
    // 文件输出
    if (this.logFile) {
      const fileMessage = `[${timestamp}] [${level}] ${message} ${args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ')}\n`;
      
      fs.appendFileSync(this.logFile, fileMessage);
    }
  }
}

// 导出单例实例
export const logger = Logger.getInstance();
