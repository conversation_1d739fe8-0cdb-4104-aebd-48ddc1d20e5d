import * as fs from 'fs-extra';
import * as path from 'path';
import chalk from 'chalk';

/**
 * Cocos Creator 项目检测器
 * 专门用于检测和验证 Cocos Creator 项目
 */
export class CocosCreatorDetector {
  
  /**
   * 检测是否为 Cocos Creator 项目
   */
  static isCocosCreatorProject(projectPath: string = process.cwd()): boolean {
    // 方法1: 检查 package.json 中的 creator 字段和 keywords
    const packageJsonResult = this.checkPackageJson(projectPath);
    if (packageJsonResult.isCocos) {
      return true;
    }

    // 方法2: 检查 project.json (Cocos Creator 项目配置文件)
    const projectJsonResult = this.checkProjectJson(projectPath);
    if (projectJsonResult.isCocos) {
      return true;
    }

    // 方法3: 检查必需的目录结构
    const structureResult = this.checkProjectStructure(projectPath);
    if (structureResult.isCocos) {
      return true;
    }

    return false;
  }
  
  /**
   * 检查 package.json 中的 Cocos Creator 标识
   */
  private static checkPackageJson(projectPath: string): {
    isCocos: boolean;
    version?: string;
    confidence: number;
  } {
    try {
      const packageJsonPath = path.join(projectPath, 'package.json');
      if (!fs.existsSync(packageJsonPath)) {
        return { isCocos: false, confidence: 0 };
      }

      const packageJson = fs.readJSONSync(packageJsonPath);
      let confidence = 0;
      let version: string | undefined;

      // 检查 creator 字段
      if (packageJson.creator && packageJson.creator.version) {
        confidence += 50;
        version = packageJson.creator.version;
      }

      // 检查 keywords
      if (packageJson.keywords && Array.isArray(packageJson.keywords)) {
        const cocosKeywords = ['cocos-creator', 'cocos', 'creator'];
        const hasKeyword = cocosKeywords.some(keyword =>
          packageJson.keywords.includes(keyword)
        );
        if (hasKeyword) {
          confidence += 30;
        }
      }

      // 检查 scripts 中的 cocos 相关命令
      if (packageJson.scripts) {
        const scriptValues = Object.values(packageJson.scripts).join(' ');
        if (scriptValues.includes('cocos-creator') || scriptValues.includes('cocos')) {
          confidence += 20;
        }
      }

      return {
        isCocos: confidence >= 50,
        version,
        confidence
      };
    } catch (error) {
      return { isCocos: false, confidence: 0 };
    }
  }

  /**
   * 检查 project.json (Cocos Creator 项目配置)
   */
  private static checkProjectJson(projectPath: string): {
    isCocos: boolean;
    version?: string;
    confidence: number;
  } {
    try {
      const projectJsonPath = path.join(projectPath, 'project.json');
      if (!fs.existsSync(projectJsonPath)) {
        return { isCocos: false, confidence: 0 };
      }

      const projectJson = fs.readJSONSync(projectJsonPath);
      let confidence = 0;

      // 检查是否有 uuid 字段（Cocos Creator 项目特有）
      if (projectJson.uuid) {
        confidence += 40;
      }

      // 检查是否有 engine 字段
      if (projectJson.engine) {
        confidence += 30;
      }

      // 检查名称和描述中是否包含 cocos 相关信息
      const textFields = [projectJson.name, projectJson.description].join(' ').toLowerCase();
      if (textFields.includes('cocos') || textFields.includes('creator')) {
        confidence += 10;
      }

      return {
        isCocos: confidence >= 40,
        version: projectJson.engine,
        confidence
      };
    } catch (error) {
      return { isCocos: false, confidence: 0 };
    }
  }

  /**
   * 检查项目目录结构
   */
  private static checkProjectStructure(projectPath: string): {
    isCocos: boolean;
    confidence: number;
  } {
    let confidence = 0;

    // 检查 assets 目录
    const assetsPath = path.join(projectPath, 'assets');
    if (fs.existsSync(assetsPath) && fs.statSync(assetsPath).isDirectory()) {
      confidence += 20;

      // 检查 assets 下的典型 Cocos Creator 子目录
      const cocosSubDirs = ['scripts', 'scenes', 'resources', 'textures'];
      const existingSubDirs = cocosSubDirs.filter(dir => {
        const dirPath = path.join(assetsPath, dir);
        return fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
      });

      confidence += existingSubDirs.length * 10;
    }

    // 检查其他 Cocos Creator 相关目录
    const cocosDirs = ['build', 'temp', 'library', 'local'];
    const existingCocosDirs = cocosDirs.filter(dir => {
      const dirPath = path.join(projectPath, dir);
      return fs.existsSync(dirPath);
    });

    confidence += existingCocosDirs.length * 5;

    // 检查 creator.d.ts (可选)
    const creatorDtsPath = path.join(projectPath, 'creator.d.ts');
    if (fs.existsSync(creatorDtsPath)) {
      confidence += 15;
    }

    return {
      isCocos: confidence >= 30,
      confidence
    };
  }

  /**
   * 获取详细的项目验证结果
   */
  static validateProject(projectPath: string = process.cwd()): {
    valid: boolean;
    version?: string;
    missingFiles: string[];
    warnings: string[];
    projectInfo?: any;
    detectionMethods: string[];
  } {
    const missingFiles: string[] = [];
    const warnings: string[] = [];
    const detectionMethods: string[] = [];
    let projectInfo: any = null;
    let version: string | undefined;

    // 使用多种方法检测
    const packageResult = this.checkPackageJson(projectPath);
    const projectResult = this.checkProjectJson(projectPath);
    const structureResult = this.checkProjectStructure(projectPath);

    // 记录检测方法
    if (packageResult.isCocos) {
      detectionMethods.push(`package.json (置信度: ${packageResult.confidence}%)`);
      if (packageResult.version) version = packageResult.version;
    }

    if (projectResult.isCocos) {
      detectionMethods.push(`project.json (置信度: ${projectResult.confidence}%)`);
      if (projectResult.version && !version) version = projectResult.version;
    }

    if (structureResult.isCocos) {
      detectionMethods.push(`目录结构 (置信度: ${structureResult.confidence}%)`);
    }

    const isValid = packageResult.isCocos || projectResult.isCocos || structureResult.isCocos;

    if (!isValid) {
      missingFiles.push('package.json 中缺少 creator 字段或 cocos-creator 关键词');
      missingFiles.push('project.json 中缺少 uuid 或 engine 字段');
      missingFiles.push('缺少典型的 Cocos Creator 目录结构');
    }

    // 读取项目信息
    try {
      const packageJsonPath = path.join(projectPath, 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        projectInfo = fs.readJSONSync(packageJsonPath);
      }
    } catch (error) {
      warnings.push(`无法读取 package.json: ${error instanceof Error ? error.message : String(error)}`);
    }

    // 检查可选但推荐的文件
    const optionalFiles = this.getOptionalFiles();
    for (const file of optionalFiles) {
      const fullPath = path.join(projectPath, file.path);
      if (!fs.existsSync(fullPath)) {
        warnings.push(`建议添加 ${file.path}: ${file.description}`);
      }
    }

    return {
      valid: isValid,
      version,
      missingFiles,
      warnings,
      projectInfo,
      detectionMethods
    };
  }
  
  /**
   * 获取 Cocos Creator 版本信息
   */
  static getCocosCreatorVersion(projectPath: string = process.cwd()): string | null {
    try {
      const projectJsonPath = path.join(projectPath, 'project.json');
      if (!fs.existsSync(projectJsonPath)) {
        return null;
      }
      
      const projectJson = fs.readJSONSync(projectJsonPath);
      return projectJson.version || null;
    } catch {
      return null;
    }
  }
  
  /**
   * 检查 Cocos Creator 构建环境
   */
  static validateBuildEnvironment(projectPath: string = process.cwd()): {
    valid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    // 检查构建相关目录
    const buildDirs = ['build', 'temp', 'library'];
    for (const dir of buildDirs) {
      const dirPath = path.join(projectPath, dir);
      if (fs.existsSync(dirPath)) {
        const stat = fs.statSync(dirPath);
        if (!stat.isDirectory()) {
          issues.push(`${dir} 应该是目录而不是文件`);
        }
      }
    }
    
    // 检查资源目录结构
    const assetsPath = path.join(projectPath, 'assets');
    if (fs.existsSync(assetsPath)) {
      const assetsDirs = ['scripts', 'resources', 'scenes'];
      for (const dir of assetsDirs) {
        const dirPath = path.join(assetsPath, dir);
        if (!fs.existsSync(dirPath)) {
          recommendations.push(`建议创建 assets/${dir} 目录`);
        }
      }
    }
    
    // 检查 TypeScript 配置
    const tsConfigPath = path.join(projectPath, 'tsconfig.json');
    if (!fs.existsSync(tsConfigPath)) {
      recommendations.push('建议添加 tsconfig.json 配置文件');
    }
    
    return {
      valid: issues.length === 0,
      issues,
      recommendations
    };
  }
  
  /**
   * 获取项目统计信息
   */
  static getProjectStats(projectPath: string = process.cwd()): {
    assetsCount: number;
    scriptsCount: number;
    scenesCount: number;
    totalSize: number;
  } {
    const stats = {
      assetsCount: 0,
      scriptsCount: 0,
      scenesCount: 0,
      totalSize: 0
    };
    
    try {
      const assetsPath = path.join(projectPath, 'assets');
      if (fs.existsSync(assetsPath)) {
        this.countAssetsRecursive(assetsPath, stats);
      }
    } catch (error) {
      console.warn(`获取项目统计信息失败: ${error instanceof Error ? error.message : String(error)}`);
    }
    
    return stats;
  }
  
  /**
   * 递归统计资源文件
   */
  private static countAssetsRecursive(dirPath: string, stats: any): void {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        this.countAssetsRecursive(itemPath, stats);
      } else {
        stats.assetsCount++;
        stats.totalSize += stat.size;
        
        const ext = path.extname(item).toLowerCase();
        if (['.ts', '.js'].includes(ext)) {
          stats.scriptsCount++;
        } else if (ext === '.scene') {
          stats.scenesCount++;
        }
      }
    }
  }
  
  /**
   * 获取项目必需文件指示器（已废弃，保留用于兼容性）
   */
  private static getProjectIndicators(): Array<{
    path: string;
    description: string;
    isDirectory?: boolean;
  }> {
    return [
      {
        path: 'assets',
        description: '资源目录',
        isDirectory: true
      },
      {
        path: 'package.json',
        description: 'Node.js 包配置文件（应包含 creator 字段）'
      }
    ];
  }
  
  /**
   * 获取可选文件列表
   */
  private static getOptionalFiles(): Array<{
    path: string;
    description: string;
  }> {
    return [
      {
        path: 'tsconfig.json',
        description: 'TypeScript 配置文件'
      },
      {
        path: 'package.json',
        description: 'Node.js 包配置文件'
      },
      {
        path: '.gitignore',
        description: 'Git 忽略文件配置'
      },
      {
        path: 'README.md',
        description: '项目说明文档'
      }
    ];
  }
  
  /**
   * 显示项目验证结果
   */
  static displayValidationResult(result: {
    valid: boolean;
    version?: string;
    missingFiles: string[];
    warnings: string[];
    projectInfo?: any;
    detectionMethods?: string[];
  }): void {
    if (result.valid) {
      console.log(chalk.green('✅ Cocos Creator 项目验证通过'));
      if (result.version) {
        console.log(chalk.gray(`   Cocos Creator 版本: ${result.version}`));
      }
      if (result.detectionMethods && result.detectionMethods.length > 0) {
        console.log(chalk.blue('   检测方法:'));
        result.detectionMethods.forEach(method => {
          console.log(chalk.gray(`     • ${method}`));
        });
      }
    } else {
      console.log(chalk.red('❌ Cocos Creator 项目验证失败'));
      if (result.missingFiles.length > 0) {
        console.log(chalk.red('   检测失败原因:'));
        result.missingFiles.forEach(file => {
          console.log(chalk.red(`     • ${file}`));
        });
      }
    }

    if (result.warnings.length > 0) {
      console.log(chalk.yellow('⚠️  建议:'));
      result.warnings.forEach(warning => {
        console.log(chalk.yellow(`   • ${warning}`));
      });
    }
  }
  
  /**
   * 显示项目统计信息
   */
  static displayProjectStats(stats: {
    assetsCount: number;
    scriptsCount: number;
    scenesCount: number;
    totalSize: number;
  }): void {
    console.log(chalk.blue('📊 项目统计:'));
    console.log(chalk.gray(`   资源文件: ${stats.assetsCount} 个`));
    console.log(chalk.gray(`   脚本文件: ${stats.scriptsCount} 个`));
    console.log(chalk.gray(`   场景文件: ${stats.scenesCount} 个`));
    console.log(chalk.gray(`   总大小: ${(stats.totalSize / 1024 / 1024).toFixed(2)} MB`));
  }
}
