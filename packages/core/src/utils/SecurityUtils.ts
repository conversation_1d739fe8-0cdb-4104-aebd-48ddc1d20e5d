import * as crypto from 'crypto';
import * as path from 'path';
import * as fs from 'fs-extra';
import { Logger } from './Logger';

/**
 * 安全工具类
 * 提供命令注入防护、路径验证、敏感信息处理等安全功能
 */
export class SecurityUtils {
  private static logger = Logger.getInstance();

  /**
   * 验证并清理命令参数，防止命令注入
   */
  static sanitizeCommandArgs(args: string[]): string[] {
    const sanitized: string[] = [];
    
    for (const arg of args) {
      // 检查危险字符
      if (this.containsDangerousChars(arg)) {
        this.logger.warn('检测到潜在危险字符，已过滤', { arg });
        continue;
      }

      // 转义特殊字符
      const escaped = this.escapeShellArg(arg);
      sanitized.push(escaped);
    }

    return sanitized;
  }

  /**
   * 安全执行Git命令
   */
  static sanitizeGitCommand(command: string, args: string[]): { command: string; args: string[] } {
    // 验证Git命令白名单
    const allowedCommands = [
      'status', 'add', 'commit', 'push', 'pull', 'fetch', 'checkout', 
      'branch', 'tag', 'log', 'diff', 'reset', 'merge', 'clone'
    ];

    if (!allowedCommands.includes(command)) {
      throw new Error(`不允许的Git命令: ${command}`);
    }

    // 清理参数
    const sanitizedArgs = this.sanitizeCommandArgs(args);

    // 验证参数格式
    for (const arg of sanitizedArgs) {
      if (arg.includes('..') && !arg.startsWith('--')) {
        throw new Error(`潜在的路径遍历攻击: ${arg}`);
      }
    }

    return { command, args: sanitizedArgs };
  }

  /**
   * 验证文件路径安全性
   */
  static validateFilePath(filePath: string, allowedBasePaths: string[] = []): boolean {
    try {
      // 解析绝对路径
      const absolutePath = path.resolve(filePath);
      
      // 检查路径遍历
      if (filePath.includes('..') || filePath.includes('~')) {
        this.logger.warn('检测到路径遍历尝试', { filePath });
        return false;
      }

      // 检查是否在允许的基础路径内
      if (allowedBasePaths.length > 0) {
        const isAllowed = allowedBasePaths.some(basePath => {
          const absoluteBasePath = path.resolve(basePath);
          return absolutePath.startsWith(absoluteBasePath);
        });

        if (!isAllowed) {
          this.logger.warn('文件路径不在允许范围内', { filePath, allowedBasePaths });
          return false;
        }
      }

      // 检查危险路径
      const dangerousPaths = [
        '/etc', '/usr/bin', '/bin', '/sbin', '/boot',
        'C:\\Windows', 'C:\\Program Files', 'C:\\System32'
      ];

      for (const dangerousPath of dangerousPaths) {
        if (absolutePath.startsWith(path.resolve(dangerousPath))) {
          this.logger.warn('尝试访问危险路径', { filePath, dangerousPath });
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error('路径验证失败', { filePath, error });
      return false;
    }
  }

  /**
   * 安全的文件操作
   */
  static async safeFileOperation<T>(
    operation: () => Promise<T>,
    filePath: string,
    allowedBasePaths: string[] = []
  ): Promise<T> {
    // 验证路径安全性
    if (!this.validateFilePath(filePath, allowedBasePaths)) {
      throw new Error(`不安全的文件路径: ${filePath}`);
    }

    try {
      return await operation();
    } catch (error) {
      this.logger.error('文件操作失败', { filePath, error });
      throw error;
    }
  }

  /**
   * 验证配置文件权限
   */
  static async validateConfigPermissions(configPath: string): Promise<void> {
    try {
      const stats = await fs.stat(configPath);
      const mode = stats.mode & parseInt('777', 8);
      
      // 检查权限是否过于宽松 (644 或更严格)
      if (mode > parseInt('644', 8)) {
        this.logger.warn('配置文件权限过于宽松，建议设置为600', { 
          configPath, 
          currentMode: mode.toString(8) 
        });
        
        // 自动修正权限
        await fs.chmod(configPath, parseInt('600', 8));
        this.logger.info('配置文件权限已修正', { configPath });
      }
    } catch (error) {
      // 权限检查失败不应该阻止程序运行
      this.logger.warn('无法验证配置文件权限', { configPath, error });
    }
  }

  /**
   * 检查是否包含危险字符
   */
  private static containsDangerousChars(input: string): boolean {
    const dangerousPatterns = [
      /[;&|`$(){}[\]]/,  // Shell 特殊字符
      /\$\(/,            // 命令替换
      /`.*`/,            // 反引号命令执行
      /\|\s*\w+/,        // 管道命令
      />\s*\/dev/,       // 重定向到设备
      /rm\s+-rf/,        // 危险的删除命令
      /sudo\s+/,         // sudo 命令
      /chmod\s+/,        // 权限修改
      /chown\s+/,        // 所有者修改
    ];

    return dangerousPatterns.some(pattern => pattern.test(input));
  }

  /**
   * 转义Shell参数
   */
  private static escapeShellArg(arg: string): string {
    // 如果参数包含空格或特殊字符，用引号包围
    if (/[\s"'\\$`(){}[\];|&<>]/.test(arg)) {
      return `"${arg.replace(/["\\$`]/g, '\\$&')}"`;
    }
    return arg;
  }

  /**
   * 生成安全的随机字符串
   */
  static generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 计算文件哈希值
   */
  static async calculateFileHash(filePath: string, algorithm: string = 'sha256'): Promise<string> {
    const hash = crypto.createHash(algorithm);
    const stream = fs.createReadStream(filePath);
    
    return new Promise((resolve, reject) => {
      stream.on('data', data => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', reject);
    });
  }

  /**
   * 验证文件完整性
   */
  static async verifyFileIntegrity(filePath: string, expectedHash: string, algorithm: string = 'sha256'): Promise<boolean> {
    try {
      const actualHash = await this.calculateFileHash(filePath, algorithm);
      return actualHash === expectedHash;
    } catch (error) {
      this.logger.error('文件完整性验证失败', { filePath, error });
      return false;
    }
  }

  /**
   * 清理敏感信息
   */
  static sanitizeSensitiveData(data: any): any {
    const sensitiveKeys = ['password', 'token', 'key', 'secret', 'credential'];
    
    if (typeof data === 'string') {
      return data.replace(/([a-zA-Z]*(?:password|token|key|secret|credential)[a-zA-Z]*\s*[:=]\s*)([^\s,}]+)/gi, '$1***');
    }
    
    if (typeof data === 'object' && data !== null) {
      const sanitized = Array.isArray(data) ? [] : {};
      
      for (const [key, value] of Object.entries(data)) {
        const lowerKey = key.toLowerCase();
        if (sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))) {
          (sanitized as any)[key] = '***';
        } else {
          (sanitized as any)[key] = this.sanitizeSensitiveData(value);
        }
      }
      
      return sanitized;
    }
    
    return data;
  }
}
