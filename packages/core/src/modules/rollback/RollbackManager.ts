import * as fs from 'fs-extra';
import * as path from 'path';
import * as semver from 'semver';
import { simpleGit, SimpleGit } from 'simple-git';
import { VersionManager } from '../version/VersionManager';
import { BuildManager } from '../build/BuildManager';
import { DeployManager } from '../deploy/DeployManager';
import { ConfigManager } from '../config/ConfigManager';
import { Logger } from '../../utils/Logger';

/**
 * 回滚信息接口
 */
export interface RollbackInfo {
  version: string;
  date: string;
  commitHash: string;
  buildExists: boolean;
  tagExists: boolean;
  description?: string;
}

/**
 * 回滚选项接口
 */
export interface RollbackOptions {
  platform?: string;
  environment?: string;
  force?: boolean;
  skipBuild?: boolean;
  skipDeploy?: boolean;
  createBackup?: boolean;
}

/**
 * 回滚历史记录接口
 */
export interface RollbackRecord {
  id: string;
  fromVersion: string;
  toVersion: string;
  timestamp: string;
  success: boolean;
  platform?: string;
  environment?: string;
  reason?: string;
  error?: string;
}

/**
 * 回滚管理器
 * 负责版本回滚的核心逻辑
 */
export class RollbackManager {
  private versionManager: VersionManager;
  private buildManager: BuildManager;
  private deployManager: DeployManager;
  private configManager: ConfigManager;
  private git: SimpleGit;
  private logger: Logger;
  private projectRoot: string;

  constructor() {
    this.projectRoot = process.cwd();
    this.versionManager = new VersionManager();
    this.buildManager = new BuildManager();
    this.deployManager = new DeployManager();
    this.configManager = new ConfigManager();
    this.git = simpleGit(this.projectRoot);
    this.logger = Logger.getInstance();
  }

  /**
   * 获取可回滚的版本列表
   */
  async getRollbackVersions(limit: number = 10): Promise<RollbackInfo[]> {
    try {
      this.logger.info('获取可回滚版本列表', { limit });

      const versions = await this.versionManager.getAvailableVersions();
      const currentVersion = await this.versionManager.getCurrentVersion();

      const rollbackVersions: RollbackInfo[] = [];

      for (const version of versions.slice(0, limit)) {
        const rollbackInfo = await this.getRollbackInfo(version);
        rollbackVersions.push(rollbackInfo);
      }

      this.logger.info('获取可回滚版本完成', { 
        total: versions.length, 
        returned: rollbackVersions.length 
      });

      return rollbackVersions;

    } catch (error) {
      this.logger.error('获取可回滚版本失败', error);
      throw error;
    }
  }

  /**
   * 回滚到指定版本
   */
  async rollbackToVersion(
    targetVersion: string, 
    options: RollbackOptions = {}
  ): Promise<RollbackRecord> {
    const rollbackId = `rollback_${Date.now()}`;
    const startTime = new Date().toISOString();

    try {
      this.logger.info('开始回滚到指定版本', { 
        targetVersion, 
        options, 
        rollbackId 
      });

      const currentVersion = await this.versionManager.getCurrentVersion();

      // 验证目标版本
      await this.validateRollbackTarget(targetVersion);

      // 检查工作区状态
      await this.checkWorkspaceStatus();

      // 执行回滚
      const nextVersion = await this.executeRollback(
        currentVersion, 
        targetVersion, 
        options
      );

      const record: RollbackRecord = {
        id: rollbackId,
        fromVersion: currentVersion,
        toVersion: nextVersion,
        timestamp: startTime,
        success: true,
        platform: options.platform,
        environment: options.environment
      };

      // 保存回滚记录
      await this.saveRollbackRecord(record);

      this.logger.info('回滚完成', record);
      return record;

    } catch (error) {
      const record: RollbackRecord = {
        id: rollbackId,
        fromVersion: await this.versionManager.getCurrentVersion(),
        toVersion: targetVersion,
        timestamp: startTime,
        success: false,
        platform: options.platform,
        environment: options.environment,
        error: error instanceof Error ? error.message : String(error)
      };

      await this.saveRollbackRecord(record);
      this.logger.error('回滚失败', error);
      throw error;
    }
  }

  /**
   * 回滚到上一个版本
   */
  async rollbackToLastVersion(options: RollbackOptions = {}): Promise<RollbackRecord> {
    try {
      const versions = await this.versionManager.getAvailableVersions();
      const currentVersion = await this.versionManager.getCurrentVersion();

      const currentIndex = versions.indexOf(currentVersion);
      if (currentIndex === -1 || currentIndex === versions.length - 1) {
        throw new Error('没有可回滚的上一个版本');
      }

      const lastVersion = versions[currentIndex + 1];
      return await this.rollbackToVersion(lastVersion, options);

    } catch (error) {
      this.logger.error('回滚到上一版本失败', error);
      throw error;
    }
  }

  /**
   * 获取回滚状态
   */
  async getRollbackStatus(): Promise<{
    currentVersion: string;
    availableVersions: number;
    lastRollback?: RollbackRecord;
    canRollback: boolean;
  }> {
    try {
      const currentVersion = await this.versionManager.getCurrentVersion();
      const versions = await this.versionManager.getAvailableVersions();
      const rollbackHistory = await this.getRollbackHistory(1);

      return {
        currentVersion,
        availableVersions: versions.length - 1,
        lastRollback: rollbackHistory[0],
        canRollback: versions.length > 1
      };

    } catch (error) {
      this.logger.error('获取回滚状态失败', error);
      throw error;
    }
  }

  /**
   * 创建回滚检查点
   */
  async createRollbackCheckpoint(name?: string): Promise<{
    checkpointName: string;
    version: string;
    tag: string;
  }> {
    try {
      const currentVersion = await this.versionManager.getCurrentVersion();
      const checkpointName = name || `checkpoint-${Date.now()}`;
      const tag = `${currentVersion}-${checkpointName}`;

      await this.versionManager.createTag(tag, `Rollback checkpoint: ${checkpointName}`);

      const result = {
        checkpointName,
        version: currentVersion,
        tag
      };

      this.logger.info('回滚检查点创建成功', result);
      return result;

    } catch (error) {
      this.logger.error('创建回滚检查点失败', error);
      throw error;
    }
  }

  /**
   * 获取回滚历史记录
   */
  async getRollbackHistory(limit: number = 20): Promise<RollbackRecord[]> {
    try {
      const historyFile = path.join(this.projectRoot, '.version-craft', 'rollback-history.json');
      
      if (!await fs.pathExists(historyFile)) {
        return [];
      }

      const history: RollbackRecord[] = await fs.readJSON(historyFile);
      return history
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit);

    } catch (error) {
      this.logger.error('获取回滚历史失败', error);
      return [];
    }
  }

  /**
   * 验证回滚可行性
   */
  async validateRollback(targetVersion: string): Promise<{
    valid: boolean;
    issues: string[];
    warnings: string[];
  }> {
    const issues: string[] = [];
    const warnings: string[] = [];

    try {
      // 检查版本是否存在
      const versions = await this.versionManager.getAvailableVersions();
      if (!versions.includes(targetVersion)) {
        issues.push(`目标版本 ${targetVersion} 不存在`);
      }

      // 检查是否是当前版本
      const currentVersion = await this.versionManager.getCurrentVersion();
      if (targetVersion === currentVersion) {
        issues.push('目标版本与当前版本相同');
      }

      // 检查 Git 标签是否存在
      const tagExists = await this.checkGitTagExists(targetVersion);
      if (!tagExists) {
        warnings.push(`Git 标签 v${targetVersion} 不存在`);
      }

      // 检查工作区状态
      const status = await this.git.status();
      if (status.files.length > 0) {
        warnings.push('工作区有未提交的更改');
      }

      return {
        valid: issues.length === 0,
        issues,
        warnings
      };

    } catch (error) {
      issues.push(`验证失败: ${error instanceof Error ? error.message : String(error)}`);
      return { valid: false, issues, warnings };
    }
  }

  // 私有方法

  private async getRollbackInfo(version: string): Promise<RollbackInfo> {
    try {
      const tagExists = await this.checkGitTagExists(version);
      
      // 简化实现，实际项目中可以从 Git 历史获取更详细信息
      return {
        version,
        date: new Date().toISOString().split('T')[0],
        commitHash: 'unknown',
        buildExists: true, // 简化假设构建存在
        tagExists,
        description: `Version ${version}`
      };

    } catch (error) {
      return {
        version,
        date: 'unknown',
        commitHash: 'unknown',
        buildExists: false,
        tagExists: false,
        description: 'Error getting version info'
      };
    }
  }

  private async validateRollbackTarget(targetVersion: string): Promise<void> {
    const validation = await this.validateRollback(targetVersion);
    if (!validation.valid) {
      throw new Error(`回滚验证失败: ${validation.issues.join(', ')}`);
    }
  }

  private async checkWorkspaceStatus(): Promise<void> {
    const status = await this.git.status();
    if (status.files.length > 0) {
      // 自动暂存更改
      await this.git.stash(['push', '-m', `回滚前自动暂存 - ${new Date().toISOString()}`]);
      this.logger.info('工作区更改已自动暂存');
    }
  }

  private async executeRollback(
    currentVersion: string,
    targetVersion: string,
    options: RollbackOptions
  ): Promise<string> {
    // 1. 计算新版本号
    const nextVersion = await this.calculateNextRollbackVersion(currentVersion, targetVersion);

    // 2. Git checkout 到目标版本
    const tagName = targetVersion.startsWith('v') ? targetVersion : `v${targetVersion}`;
    await this.git.checkout([tagName]);

    // 3. 更新版本文件
    await this.updateVersionFiles(nextVersion);

    // 4. 创建新标签
    await this.versionManager.createTag(nextVersion, `Rollback to ${targetVersion} code state`);

    // 5. 重新构建（如果需要）
    if (!options.skipBuild) {
      if (options.platform) {
        await this.buildManager.buildPlatform(options.platform);
      } else {
        await this.buildManager.buildAllPlatforms();
      }
    }

    // 6. 重新部署（如果需要）
    if (!options.skipDeploy && options.environment) {
      await this.deployManager.deployToEnvironment(
        options.environment as 'staging' | 'production',
        options.platform
      );
    }

    return nextVersion;
  }

  private async calculateNextRollbackVersion(
    currentVersion: string,
    targetVersion: string
  ): Promise<string> {
    const currentParsed = semver.parse(currentVersion);
    if (!currentParsed) {
      throw new Error(`当前版本格式无效: ${currentVersion}`);
    }

    let nextVersion: string;

    if (currentParsed.prerelease.length > 0) {
      // 预发布版本处理
      const prereleaseType = currentParsed.prerelease[0] as string;
      const prereleaseNumber = (currentParsed.prerelease[1] as number) || 0;
      const baseVersion = `${currentParsed.major}.${currentParsed.minor}.${currentParsed.patch}`;
      nextVersion = `${baseVersion}-${prereleaseType}.${prereleaseNumber + 1}-rollback-to-${targetVersion}`;
    } else {
      // 正式版本处理
      const patchIncremented = semver.inc(currentVersion, 'patch');
      nextVersion = `${patchIncremented}-rollback-to-${targetVersion}`;
    }

    return nextVersion;
  }

  private async updateVersionFiles(version: string): Promise<void> {
    const filesToUpdate = [
      {
        path: 'version-craft.config.json',
        updater: async () => {
          const config = await this.configManager.loadConfig();
          config.project.version = version;
          const configPath = path.join(this.projectRoot, 'version-craft.config.json');
          await fs.writeJSON(configPath, config, { spaces: 2 });
        }
      },
      {
        path: 'package.json',
        updater: async () => {
          const packageJsonPath = path.join(this.projectRoot, 'package.json');
          if (await fs.pathExists(packageJsonPath)) {
            const packageJson = await fs.readJSON(packageJsonPath);
            packageJson.version = version;
            await fs.writeJSON(packageJsonPath, packageJson, { spaces: 2 });
          }
        }
      }
    ];

    for (const fileInfo of filesToUpdate) {
      try {
        await fileInfo.updater();
        this.logger.info(`版本文件已更新: ${fileInfo.path}`);
      } catch (error) {
        this.logger.warn(`更新版本文件失败: ${fileInfo.path}`, error);
      }
    }
  }

  private async checkGitTagExists(version: string): Promise<boolean> {
    try {
      const tags = await this.git.tags();
      const tagName = version.startsWith('v') ? version : `v${version}`;
      return tags.all.includes(tagName);
    } catch {
      return false;
    }
  }

  private async saveRollbackRecord(record: RollbackRecord): Promise<void> {
    try {
      const historyDir = path.join(this.projectRoot, '.version-craft');
      const historyFile = path.join(historyDir, 'rollback-history.json');

      await fs.ensureDir(historyDir);

      let history: RollbackRecord[] = [];
      if (await fs.pathExists(historyFile)) {
        history = await fs.readJSON(historyFile);
      }

      history.unshift(record);
      
      // 保留最近 100 条记录
      if (history.length > 100) {
        history = history.slice(0, 100);
      }

      await fs.writeJSON(historyFile, history, { spaces: 2 });

    } catch (error) {
      this.logger.error('保存回滚记录失败', error);
    }
  }
}
