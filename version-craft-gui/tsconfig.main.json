{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "target": "ES2020",
    "module": "CommonJS",
    "lib": ["ES2020"],
    "outDir": "./dist/main",
    "rootDir": "./src/main",
    "noEmit": false,
    "jsx": "react",
    
    /* Node.js specific */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    
    /* Type definitions */
    "types": ["node", "electron"]
  },
  "include": [
    "src/main/**/*.ts",
    "src/shared/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "release",
    "src/renderer"
  ]
}
