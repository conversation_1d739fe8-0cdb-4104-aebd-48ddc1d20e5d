import { spawn, ChildProcess } from 'child_process';
import { join } from 'path';
import fs from 'fs-extra';
import { BrowserWindow } from 'electron';

export class VersionCraftService {
  private projectPath: string;
  private cliPath: string;
  private runningProcesses: Map<string, ChildProcess> = new Map();

  constructor(projectPath: string) {
    this.projectPath = projectPath;
    // 假设 CLI 在上级目录的 dist 文件夹中
    this.cliPath = join(__dirname, '../../../dist/cli.js');
  }

  /**
   * 执行 CLI 命令
   */
  private async executeCommand(args: string[], options: {
    onProgress?: (data: string) => void;
    processId?: string;
  } = {}): Promise<{ success: boolean; output?: string; error?: string }> {
    return new Promise((resolve) => {
      const child = spawn('node', [this.cliPath, ...args], {
        cwd: this.projectPath,
        stdio: 'pipe',
        shell: true
      });

      // 如果提供了进程ID，保存进程引用
      if (options.processId) {
        this.runningProcesses.set(options.processId, child);
      }

      let output = '';
      let error = '';

      child.stdout?.on('data', (data) => {
        const text = data.toString();
        output += text;
        
        // 实时进度回调
        if (options.onProgress) {
          options.onProgress(text);
        }
      });

      child.stderr?.on('data', (data) => {
        const text = data.toString();
        error += text;
        
        // 错误也通过进度回调传递
        if (options.onProgress) {
          options.onProgress(text);
        }
      });

      child.on('close', (code) => {
        // 清理进程引用
        if (options.processId) {
          this.runningProcesses.delete(options.processId);
        }

        if (code === 0) {
          resolve({ success: true, output });
        } else {
          resolve({ success: false, error: error || '命令执行失败' });
        }
      });

      child.on('error', (err) => {
        // 清理进程引用
        if (options.processId) {
          this.runningProcesses.delete(options.processId);
        }
        
        resolve({ success: false, error: err.message });
      });
    });
  }

  /**
   * 获取当前版本
   */
  async getCurrentVersion(): Promise<any> {
    try {
      // 读取 package.json 获取版本信息
      const packagePath = join(this.projectPath, 'package.json');
      if (await fs.pathExists(packagePath)) {
        const packageJson = await fs.readJson(packagePath);
        
        // 获取额外的版本信息
        const gitInfo = await this.getGitInfo();
        
        return {
          version: packageJson.version,
          name: packageJson.name,
          description: packageJson.description,
          ...gitInfo,
          lastModified: (await fs.stat(packagePath)).mtime
        };
      }
      
      throw new Error('package.json 文件不存在');
    } catch (error) {
      throw new Error(`获取当前版本失败: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * 获取版本历史
   */
  async getVersionHistory(): Promise<any[]> {
    const result = await this.executeCommand(['history', '--json']);
    
    if (result.success && result.output) {
      try {
        return JSON.parse(result.output);
      } catch {
        // 如果 JSON 解析失败，返回简单的历史记录
        return this.parseSimpleHistory(result.output);
      }
    }
    
    // 如果命令失败，尝试从 Git 获取历史
    return this.getGitVersionHistory();
  }

  /**
   * 版本升级
   */
  async bumpVersion(options: {
    type: 'major' | 'minor' | 'patch';
    prerelease?: string;
    message?: string;
  }): Promise<any> {
    const args = ['bump', options.type];
    
    if (options.prerelease) {
      args.push('--prerelease', options.prerelease);
    }
    
    if (options.message) {
      args.push('--message', options.message);
    }

    const result = await this.executeCommand(args);
    
    if (result.success) {
      // 通知渲染进程版本已更改
      this.notifyVersionChanged();
      return { message: '版本升级成功', output: result.output };
    } else {
      throw new Error(result.error || '版本升级失败');
    }
  }

  /**
   * 版本回滚
   */
  async rollbackVersion(targetVersion: string, force: boolean = false): Promise<any> {
    const args = ['rollback-to', targetVersion];
    
    if (force) {
      args.push('--force');
    }

    const result = await this.executeCommand(args);
    
    if (result.success) {
      // 通知渲染进程版本已更改
      this.notifyVersionChanged();
      return { message: '版本回滚成功', output: result.output };
    } else {
      throw new Error(result.error || '版本回滚失败');
    }
  }

  /**
   * 开始构建
   */
  async startBuild(platform: string, options: any = {}): Promise<any> {
    const buildId = `build_${Date.now()}`;
    const args = ['build', platform];
    
    // 添加构建选项
    if (options.minify) args.push('--minify');
    if (options.debug) args.push('--debug');

    // 异步执行构建，通过进度回调实时更新
    this.executeCommand(args, {
      processId: buildId,
      onProgress: (data) => {
        this.notifyBuildProgress(buildId, data);
      }
    }).then((result) => {
      // 构建完成通知
      this.notifyBuildComplete(buildId, result.success, result.error);
    });

    return { 
      buildId, 
      message: '构建已开始',
      platform,
      startTime: new Date().toISOString()
    };
  }

  /**
   * 取消构建
   */
  cancelBuild(buildId: string): boolean {
    const process = this.runningProcesses.get(buildId);
    if (process) {
      process.kill('SIGTERM');
      this.runningProcesses.delete(buildId);
      return true;
    }
    return false;
  }

  /**
   * 获取 Git 信息
   */
  private async getGitInfo(): Promise<any> {
    try {
      const gitDir = join(this.projectPath, '.git');
      if (await fs.pathExists(gitDir)) {
        // 这里可以使用 simple-git 获取更详细的 Git 信息
        return {
          hasGit: true,
          branch: 'main', // 简化处理
          lastCommit: new Date().toISOString()
        };
      }
      return { hasGit: false };
    } catch {
      return { hasGit: false };
    }
  }

  /**
   * 从 Git 获取版本历史
   */
  private async getGitVersionHistory(): Promise<any[]> {
    // 简化实现，返回模拟数据
    return [
      {
        version: '1.0.0',
        date: new Date().toISOString(),
        message: '初始版本',
        author: 'Developer'
      }
    ];
  }

  /**
   * 解析简单的历史记录
   */
  private parseSimpleHistory(output: string): any[] {
    // 简化实现，解析文本输出
    const lines = output.split('\n').filter(line => line.trim());
    return lines.map((line, index) => ({
      version: `1.0.${index}`,
      message: line,
      date: new Date().toISOString()
    }));
  }

  /**
   * 通知版本变更
   */
  private notifyVersionChanged(): void {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send('version-changed', {
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 通知构建进度
   */
  private notifyBuildProgress(buildId: string, data: string): void {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send('build-progress', {
        buildId,
        data,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 通知构建完成
   */
  private notifyBuildComplete(buildId: string, success: boolean, error?: string): void {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send('build-progress', {
        buildId,
        completed: true,
        success,
        error,
        timestamp: new Date().toISOString()
      });
    }
  }
}
