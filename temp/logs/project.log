2025-8-25 12:24:49 - log: Load engine in D:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
2025-8-25 12:24:50 - log: Register native engine in D:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native
2025-8-25 12:24:51 - log: Request namespace: device-list
2025-8-25 12:25:01 - error: Cannot read properties of undefined (reading 'callback')TypeError: Cannot read properties of undefined (reading 'callback')
    at D:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-worker\lib\browser.ccc:1:1804
    at new Promise (<anonymous>)
    at Worker.send (D:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-worker\lib\browser.ccc:1:1736)
    at ProgrammingWorker.request (D:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\programming\dist\worker\browser.ccc:1:595)
    at Package.<anonymous> (D:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\programming\dist\worker\browser.ccc:1:2068)
    at Package.exec (D:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\package.ccc:1:4383)
    at D:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\package\dist\browser\index.js:1:2963
    at D:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-base-ipc\dist\sender.ccc:1:1833
    at Array.forEach (<anonymous>)
    at EventSender.immediately (D:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@base\electron-base-ipc\dist\sender.ccc:1:1821)
    (Target: programming, Message: packer-driver/get-loader-context)
